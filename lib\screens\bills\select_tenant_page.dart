import 'package:flutter/material.dart';
import '../../models/tenant/tenant.dart';
import '../../services/service_locator.dart';
import 'add_bill_page.dart';

class SelectTenantPage extends StatefulWidget {
  final bool isForPayment; // Whether this is for adding a payment or a bill

  const SelectTenantPage({super.key, this.isForPayment = false});

  @override
  State<SelectTenantPage> createState() => _SelectTenantPageState();
}

class _SelectTenantPageState extends State<SelectTenantPage> {
  bool _isLoading = true;
  List<Tenant> _tenants = [];
  String? _searchQuery;
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _loadTenants();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadTenants() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final tenants = await serviceLocator.tenantService.getAllTenants();

      // Sort tenants alphabetically by name
      tenants.sort(
        (a, b) => '${a.firstName} ${a.lastName}'.compareTo(
          '${b.firstName} ${b.lastName}',
        ),
      );

      if (!mounted) return;

      setState(() {
        _tenants = tenants;
        _isLoading = false;
      });
    } catch (e) {
      if (!mounted) return;

      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('Error loading tenants: $e')));
      setState(() {
        _isLoading = false;
      });
    }
  }

  List<Tenant> get _filteredTenants {
    if (_searchQuery == null || _searchQuery!.isEmpty) {
      return _tenants;
    }

    final query = _searchQuery!.toLowerCase();
    return _tenants.where((tenant) {
      final fullName = '${tenant.firstName} ${tenant.lastName}'.toLowerCase();
      final email = tenant.email.toLowerCase();
      return fullName.contains(query) || email.contains(query);
    }).toList();
  }

  void _onTenantSelected(Tenant tenant) async {
    if (widget.isForPayment) {
      // Navigate to payment page with selected tenant
      // This will be implemented when we create the payment selection page
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Payment selection will be implemented')),
      );
    } else {
      // Navigate to add bill page with selected tenant
      final result = await Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => AddBillPage(initialTenantId: tenant.id),
        ),
      );
      if (result == true && mounted) {
        // Bill was added successfully, return to previous screen
        Navigator.of(context).pop(true);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          widget.isForPayment
              ? 'Select Tenant for Payment'
              : 'Select Tenant for Bill',
        ),
      ),
      body: Column(
        children: [
          // Search bar
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                labelText: 'Search tenants',
                hintText: 'Search by name or email',
                prefixIcon: const Icon(Icons.search),
                suffixIcon:
                    _searchQuery != null && _searchQuery!.isNotEmpty
                        ? IconButton(
                          icon: const Icon(Icons.clear),
                          onPressed: () {
                            _searchController.clear();
                            setState(() {
                              _searchQuery = null;
                            });
                          },
                        )
                        : null,
                border: const OutlineInputBorder(),
              ),
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
              },
            ),
          ),

          // Tenant list
          Expanded(
            child:
                _isLoading
                    ? const Center(child: CircularProgressIndicator())
                    : _filteredTenants.isEmpty
                    ? Center(
                      child: Text(
                        _searchQuery != null && _searchQuery!.isNotEmpty
                            ? 'No tenants match your search'
                            : 'No tenants found',
                      ),
                    )
                    : ListView.builder(
                      itemCount: _filteredTenants.length,
                      itemBuilder: (context, index) {
                        final tenant = _filteredTenants[index];
                        return _buildTenantCard(tenant);
                      },
                    ),
          ),
        ],
      ),
    );
  }

  Widget _buildTenantCard(Tenant tenant) {
    final roomInfo =
        tenant.roomId != null
            ? FutureBuilder(
              future: serviceLocator.roomService.getRoomById(tenant.roomId!),
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return const Text('Loading room info...');
                }

                if (snapshot.hasError || !snapshot.hasData) {
                  return const Text('Room info not available');
                }

                final room = snapshot.data!;
                return FutureBuilder(
                  future: serviceLocator.propertyService.getPropertyById(
                    room.propertyId,
                  ),
                  builder: (context, propertySnapshot) {
                    if (propertySnapshot.connectionState ==
                        ConnectionState.waiting) {
                      return Text('Room: ${room.name}');
                    }

                    if (propertySnapshot.hasError ||
                        !propertySnapshot.hasData) {
                      return Text('Room: ${room.name}');
                    }

                    final property = propertySnapshot.data!;
                    return Text(
                      'Room: ${room.name} (${property.name})',
                      style: TextStyle(color: Colors.grey[600]),
                    );
                  },
                );
              },
            )
            : const Text(
              'No room assigned',
              style: TextStyle(fontStyle: FontStyle.italic),
            );

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: Theme.of(context).colorScheme.primary,
          child: Text(
            tenant.firstName.isNotEmpty
                ? tenant.firstName[0].toUpperCase()
                : '?',
            style: const TextStyle(color: Colors.white),
          ),
        ),
        title: Text(
          '${tenant.firstName} ${tenant.lastName}',
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [Text(tenant.email), const SizedBox(height: 4), roomInfo],
        ),
        trailing: const Icon(Icons.arrow_forward_ios, size: 16),
        onTap: () => _onTenantSelected(tenant),
      ),
    );
  }
}
