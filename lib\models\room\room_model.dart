import 'amenity_model.dart';

/// Enum for room occupancy status
enum RoomOccupancyStatus { vacant, occupied, reserved, maintenance }

/// Extension for easier handling of occupancy status
extension RoomOccupancyStatusExtension on RoomOccupancyStatus {
  String get displayName {
    switch (this) {
      case RoomOccupancyStatus.vacant:
        return 'Vacant';
      case RoomOccupancyStatus.occupied:
        return 'Occupied';
      case RoomOccupancyStatus.reserved:
        return 'Reserved';
      case RoomOccupancyStatus.maintenance:
        return 'Under Maintenance';
    }
  }

  String get dbValue {
    return toString().split('.').last;
  }

  static RoomOccupancyStatus fromString(String value) {
    return RoomOccupancyStatus.values.firstWhere(
      (status) => status.dbValue == value,
      orElse: () => RoomOccupancyStatus.vacant,
    );
  }
}

/// Represents a room in a property
class Room {
  final String id;
  String name;
  String propertyId; // ID of the property this room belongs to
  RoomOccupancyStatus occupancyStatus;
  String? roomTypeId; // ID of the predefined room type
  String? customRoomType; // Custom room type name if not using predefined type
  double rentalPrice;
  double? size; // in square feet/meters
  int? floor; // Floor number where the room is located
  bool isFurnished;
  List<RoomAmenity> amenities;
  String? description;
  String? imageUrl;
  String? notes;
  Map<String, dynamic>? additionalInfo;
  DateTime createdAt;
  DateTime updatedAt;

  Room({
    required this.id,
    required this.name,
    required this.propertyId,
    required this.occupancyStatus,
    this.roomTypeId,
    this.customRoomType,
    required this.rentalPrice,
    this.size,
    this.floor,
    required this.isFurnished,
    List<RoomAmenity>? amenities,
    this.description,
    this.imageUrl,
    this.notes,
    this.additionalInfo,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) : amenities = amenities ?? [],
       createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now(),
       assert(
         (roomTypeId != null && customRoomType == null) ||
             (roomTypeId == null && customRoomType != null),
         'Either roomTypeId or customRoomType must be provided, but not both',
       );

  /// Get the room type display name
  String get roomTypeName {
    if (customRoomType != null) {
      return customRoomType!;
    } else if (roomTypeId != null) {
      // Ideally, we would fetch the name from the database using the ID
      // For now, we'll return a placeholder
      return 'Standard Room';
    }
    return 'Standard Room';
  }

  /// Create a room from JSON data
  factory Room.fromJson(Map<String, dynamic> json) {
    return Room(
      id: json['id'] as String,
      name: json['name'] as String,
      propertyId: json['property_id'] as String,
      occupancyStatus: RoomOccupancyStatusExtension.fromString(
        json['occupancy_status'] as String,
      ),
      roomTypeId: json['room_type_id'] as String?,
      customRoomType: json['custom_room_type'] as String?,
      rentalPrice: (json['rental_price'] as num).toDouble(),
      size: json['size'] != null ? (json['size'] as num).toDouble() : null,
      floor: json['floor'] != null ? (json['floor'] as num).toInt() : null,
      isFurnished: json['is_furnished'] as bool,
      amenities:
          (json['room_amenities'] as List<dynamic>?)
              ?.map((e) => RoomAmenity.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      description: json['description'] as String?,
      imageUrl: json['image_url'] as String?,
      notes: json['notes'] as String?,
      additionalInfo: json['additional_info'] as Map<String, dynamic>?,
      createdAt:
          json['created_at'] != null
              ? DateTime.parse(json['created_at'] as String)
              : DateTime.now(),
      updatedAt:
          json['updated_at'] != null
              ? DateTime.parse(json['updated_at'] as String)
              : DateTime.now(),
    );
  }

  /// Convert room to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'property_id': propertyId,
      'occupancy_status': occupancyStatus.dbValue,
      'room_type_id': roomTypeId,
      'custom_room_type': customRoomType,
      'rental_price': rentalPrice,
      'size': size,
      'floor': floor,
      'is_furnished': isFurnished,
      'description': description,
      'image_url': imageUrl,
      'notes': notes,
      'additional_info': additionalInfo,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  /// Create a copy of this room with given fields replaced with new values
  Room copyWith({
    String? name,
    String? propertyId,
    RoomOccupancyStatus? occupancyStatus,
    String? roomTypeId,
    String? customRoomType,
    double? rentalPrice,
    double? size,
    int? floor,
    bool? isFurnished,
    List<RoomAmenity>? amenities,
    String? description,
    String? imageUrl,
    String? notes,
    Map<String, dynamic>? additionalInfo,
  }) {
    return Room(
      id: id,
      name: name ?? this.name,
      propertyId: propertyId ?? this.propertyId,
      occupancyStatus: occupancyStatus ?? this.occupancyStatus,
      roomTypeId: roomTypeId ?? this.roomTypeId,
      customRoomType: customRoomType ?? this.customRoomType,
      rentalPrice: rentalPrice ?? this.rentalPrice,
      size: size ?? this.size,
      floor: floor ?? this.floor,
      isFurnished: isFurnished ?? this.isFurnished,
      amenities: amenities ?? this.amenities,
      description: description ?? this.description,
      imageUrl: imageUrl ?? this.imageUrl,
      notes: notes ?? this.notes,
      additionalInfo: additionalInfo ?? this.additionalInfo,
      createdAt: createdAt,
      updatedAt: DateTime.now(),
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is Room &&
        other.id == id &&
        other.name == name &&
        other.propertyId == propertyId &&
        other.occupancyStatus == occupancyStatus &&
        other.roomTypeId == roomTypeId &&
        other.customRoomType == customRoomType &&
        other.rentalPrice == rentalPrice &&
        other.size == size &&
        other.isFurnished == isFurnished &&
        other.description == description &&
        other.imageUrl == imageUrl &&
        other.notes == notes;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        name.hashCode ^
        propertyId.hashCode ^
        occupancyStatus.hashCode ^
        roomTypeId.hashCode ^
        customRoomType.hashCode ^
        rentalPrice.hashCode ^
        size.hashCode ^
        isFurnished.hashCode ^
        description.hashCode ^
        imageUrl.hashCode ^
        notes.hashCode;
  }
}
