import 'package:flutter/material.dart';
import '../../services/service_locator.dart';
import '../../utils/validators.dart';
import '../../widgets/ui_components.dart';
import 'password_reset_otp_screen.dart';

class ForgotPasswordScreen extends StatefulWidget {
  const ForgotPasswordScreen({super.key});

  @override
  State<ForgotPasswordScreen> createState() => ForgotPasswordScreenState();
}

class ForgotPasswordScreenState extends State<ForgotPasswordScreen> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  bool _isLoading = false;
  bool _hasAttemptedReset = false;

  @override
  void dispose() {
    _emailController.dispose();
    super.dispose();
  }

  // Reset password
  Future<void> _resetPassword() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
      _hasAttemptedReset = true;
    });

    try {
      // Request OTP for password reset
      await serviceLocator.authService.requestEmailOTP(
        email: _emailController.text.trim(),
      );

      if (mounted) {
        showSuccessMessage(
          context,
          'Verification code sent to your email',
        );
        
        // Navigate to OTP verification screen for password reset
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => PasswordResetOTPScreen(
              email: _emailController.text.trim(),
            ),
          ),
        );
      }
    } catch (error) {
      if (mounted) {
        showErrorMessage(
          context,
          'Error sending verification code: ${error.toString()}',
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Reset Password'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: Center(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(24.0),
          child: Form(
            key: _formKey,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Image.asset(
                  'assets/images/Shadow-Home.png',
                  height: 80,
                  width: 80,
                ),
                const SizedBox(height: 24),
                const Text(
                  'Forgot Your Password?',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),
                const Text(
                  'Enter your email and we\'ll send you a verification code to reset your password',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: 16,
                  ),
                ),
                const SizedBox(height: 32),
                
                // Email
                CustomTextField(
                  controller: _emailController,
                  labelText: 'Email',
                  prefixIcon: Icons.email,
                  keyboardType: TextInputType.emailAddress,
                  validator: (value) {
                    final emailError = Validators.validateEmail(value);
                    if (emailError != null) {
                      return emailError;
                    }
                    
                    // Additional validation for the forgot password flow
                    if (_hasAttemptedReset) {
                      // Here you could add checks against a known list of emails
                      // For example, checking if the email exists in your system
                      // This is just a placeholder for demonstration
                    }
                    
                    return null;
                  },
                  textInputAction: TextInputAction.done,
                  onFieldSubmitted: (_) => _resetPassword(),
                ),
                const SizedBox(height: 32),
                
                // Reset Button
                CustomButton(
                  text: 'Send Verification Code',
                  onPressed: _resetPassword,
                  isLoading: _isLoading,
                ),
                const SizedBox(height: 16),
                
                // Back to Login
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('Back to Login'),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
} 