import 'package:supabase_flutter/supabase_flutter.dart';
import 'service_locator.dart';
import '../utils/logger.dart';

class AuthService {
  final SupabaseClient client;

  AuthService(this.client);

  // Get the current authenticated user
  User? get currentUser => client.auth.currentUser;

  // Check if the user is logged in
  bool get isLoggedIn => client.auth.currentSession != null;
  
  // Check if token is about to expire and refresh if needed
  Future<bool> checkAndRefreshTokenIfNeeded() async {
    try {
      final session = client.auth.currentSession;
      if (session == null) {
        return false;
      }
      
      // Get expiry time and calculate when we should refresh (e.g., 5 minutes before expiry)
      final expiresAt = session.expiresAt;
      if (expiresAt == null) {
        return false;
      }
      
      final now = DateTime.now();
      final expiryDateTime = DateTime.fromMillisecondsSinceEpoch(expiresAt * 1000);
      
      // Calculate time until expiry
      final timeUntilExpiryInMinutes = expiryDateTime.difference(now).inMinutes;
      
      // If token expires in less than 5 minutes, refresh it
      if (timeUntilExpiryInMinutes < 5) {
        AppLogger.info('Token expires soon, refreshing');
        return await serviceLocator.supabaseService.refreshToken();
      }
      
      return true;
    } catch (e) {
      AppLogger.error('Error checking token expiry: $e');
      return false;
    }
  }

  // Sign up with email and password
  Future<AuthResponse> signUp({
    required String email,
    required String password,
    Map<String, dynamic>? data,
  }) async {
    final response = await client.auth.signUp(
      email: email,
      password: password,
      data: data,
    );

    // Create secure session if signup successful
    if (response.user != null) {
      await serviceLocator.sessionService.createSession(response.user!.id);
      
      // Ensure user profile exists
      await client.rpc(
        'ensure_user_profile',
        params: {
          'user_id': response.user!.id,
          'user_email': email,
          'user_metadata': data,
        },
      );
    }

    return response;
  }

  // Sign in with email and password
  Future<AuthResponse> signInWithPassword({
    required String email,
    required String password,
  }) async {
    final response = await client.auth.signInWithPassword(
      email: email,
      password: password,
    );

    // Create secure session if login successful
    if (response.user != null) {
      await serviceLocator.sessionService.createSession(response.user!.id);
      
      // Ensure user profile exists
      await client.rpc(
        'ensure_user_profile',
        params: {
          'user_id': response.user!.id,
          'user_email': email,
          'user_metadata': response.user!.userMetadata,
        },
      );
    }

    return response;
  }

  // Sign out
  Future<void> signOut() async {
    // Invalidate session before signing out from Supabase
    await serviceLocator.sessionService.invalidateSession();
    await client.auth.signOut();
  }

  // Request OTP for email verification
  Future<void> requestEmailOTP({
    required String email,
    String? redirectUrl,
  }) async {
    await client.auth.signInWithOtp(email: email, emailRedirectTo: redirectUrl);
  }

  // Verify OTP for email
  Future<AuthResponse> verifyEmailOTP({
    required String email,
    required String token,
    OtpType type = OtpType.signup,
  }) async {
    final response = await client.auth.verifyOTP(
      email: email,
      token: token,
      type: type,
    );

    // Create secure session if verification successful
    if (response.user != null) {
      await serviceLocator.sessionService.createSession(response.user!.id);
      
      // Ensure user profile exists
      await client.rpc(
        'ensure_user_profile',
        params: {
          'user_id': response.user!.id,
          'user_email': email,
          'user_metadata': response.user!.userMetadata,
        },
      );
    }

    return response;
  }

  // Reset password
  Future<void> resetPasswordForEmail(String email) async {
    await client.auth.resetPasswordForEmail(email);
  }

  // Update password with current password verification
  Future<bool> updatePassword(
    String currentPassword,
    String newPassword,
  ) async {
    try {
      // First verify the current password by attempting to sign in
      final email = currentUser?.email;
      if (email == null) {
        return false;
      }

      // Try to sign in with current password to verify it
      final verifyResponse = await client.auth.signInWithPassword(
        email: email,
        password: currentPassword,
      );

      // If sign in failed, the current password is incorrect
      if (verifyResponse.user == null) {
        return false;
      }

      // Rotate session when password is updated (security best practice)
      await serviceLocator.sessionService.rotateSession();

      // Update to the new password
      final response = await client.auth.updateUser(
        UserAttributes(password: newPassword),
      );

      return response.user != null;
    } catch (e) {
      return false;
    }
  }

  // Update user data
  Future<UserResponse> updateUserData(Map<String, dynamic> data) async {
    // Rotate session when user data is updated (security best practice)
    await serviceLocator.sessionService.rotateSession();

    return await client.auth.updateUser(UserAttributes(data: data));
  }

  // Check if session is active and valid
  Future<bool> validateSession() async {
    // First check if logged in via Supabase
    if (!isLoggedIn) return false;

    // Then validate our secure session
    final hasSession = await serviceLocator.sessionService.hasActiveSession();

    // If Supabase session exists but our secure session doesn't,
    // create a new secure session
    if (!hasSession && currentUser != null) {
      await serviceLocator.sessionService.createSession(currentUser!.id);
      return true;
    }
    
    // If we have a session, check if token needs refreshing
    if (hasSession) {
      await checkAndRefreshTokenIfNeeded();
    }

    return hasSession;
  }
}
