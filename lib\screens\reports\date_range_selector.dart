import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

class DateRangeSelector extends StatelessWidget {
  final DateTimeRange selectedRange;
  final Function(DateTimeRange) onRangeSelected;
  final String selectedPeriod;
  final Function(String) onPeriodChanged;

  const DateRangeSelector({
    super.key,
    required this.selectedRange,
    required this.onRangeSelected,
    required this.selectedPeriod,
    required this.onPeriodChanged,
  });

  void _updateDateRangeByPeriod(String period, BuildContext context) {
    final now = DateTime.now();
    DateTimeRange range;
    
    switch (period) {
      case 'this_month':
        range = DateTimeRange(
          start: DateTime(now.year, now.month, 1),
          end: DateTime(now.year, now.month + 1, 0),
        );
        break;
      case 'last_month':
        final lastMonth = DateTime(now.year, now.month - 1);
        range = DateTimeRange(
          start: DateTime(lastMonth.year, lastMonth.month, 1),
          end: DateTime(lastMonth.year, lastMonth.month + 1, 0),
        );
        break;
      case 'custom':
        _showCustomDateRangePicker(context);
        return;
      default:
        range = DateTimeRange(
          start: DateTime(now.year, now.month, 1),
          end: now,
        );
    }
    
    onPeriodChanged(period);
    onRangeSelected(range);
  }

  Future<void> _showCustomDateRangePicker(BuildContext context) async {
    final newRange = await showDateRangePicker(
      context: context,
      initialDateRange: selectedRange,
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(context).colorScheme.copyWith(
              primary: Theme.of(context).colorScheme.primary,
              onPrimary: Colors.white,
            ),
          ),
          child: child!,
        );
      },
    );
    
    if (newRange != null) {
      onPeriodChanged('custom');
      onRangeSelected(newRange);
    }
  }

  @override
  Widget build(BuildContext context) {
    final dateFormat = DateFormat('MMM d, yyyy');
    final startFormatted = dateFormat.format(selectedRange.start);
    final endFormatted = dateFormat.format(selectedRange.end);

    return Container(
      padding: const EdgeInsets.all(16.0),
      child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
          Row(
            children: [
              Expanded(
                child: _buildDateOption(
                  context,
                  'This Month',
                  'this_month',
                  Icons.calendar_today,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildDateOption(
                  context,
                  'Last Month',
                  'last_month',
                  Icons.calendar_month,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildDateOption(
                  context,
                  'Custom',
                  'custom',
                  Icons.date_range,
                ),
              ),
            ],
          ),
          if (selectedPeriod == 'custom') ...[
            const SizedBox(height: 12),
        Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primary.withAlpha(26),
              borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: Theme.of(context).colorScheme.primary.withAlpha(51),
                ),
            ),
                child: Row(
                  children: [
                    Icon(
                    Icons.date_range,
                    size: 20,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                  const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                      '$startFormatted - $endFormatted',
                      style: TextStyle(
                          fontWeight: FontWeight.w500,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.edit_calendar),
                    onPressed: () => _showCustomDateRangePicker(context),
                      color: Theme.of(context).colorScheme.primary,
                    tooltip: 'Change date range',
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(),
                    ),
                  ],
                ),
              ),
          ],
        ],
      ),
    );
  }

  Widget _buildDateOption(BuildContext context, String label, String period, IconData icon) {
    final isSelected = selectedPeriod == period;
    final theme = Theme.of(context);
    
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () => _updateDateRangeByPeriod(period, context),
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
          decoration: BoxDecoration(
            color: isSelected ? theme.colorScheme.primary : Colors.transparent,
            border: Border.all(
              color: isSelected ? theme.colorScheme.primary : theme.dividerColor,
              width: isSelected ? 2 : 1,
            ),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                icon,
                color: isSelected ? Colors.white : theme.colorScheme.primary,
                size: 24,
              ),
              const SizedBox(height: 4),
              Text(
                label,
                style: TextStyle(
                  color: isSelected ? Colors.white : theme.colorScheme.onSurface,
                  fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                  fontSize: 13,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }
} 