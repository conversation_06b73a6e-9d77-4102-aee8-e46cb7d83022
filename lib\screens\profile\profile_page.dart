import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:image_picker/image_picker.dart';
import 'package:intl/intl.dart';
import '../../models/user/user_profile_model.dart';
import '../../services/service_locator.dart';
import '../../utils/logger.dart';
import '../../utils/network_utils.dart';

import '../../l10n/app_localizations.dart';

class ProfilePage extends StatefulWidget {
  const ProfilePage({super.key});

  @override
  State<ProfilePage> createState() => _ProfilePageState();
}

class _ProfilePageState extends State<ProfilePage> {
  bool _isLoading = true;
  bool _isSaving = false;
  bool _isChangingPassword = false;
  UserProfile? _userProfile;
  final _formKey = GlobalKey<FormState>();
  final _passwordFormKey = GlobalKey<FormState>();
  final _fullNameController = TextEditingController();
  final _phoneController = TextEditingController();
  final _addressController = TextEditingController();
  final _currentPasswordController = TextEditingController();
  final _newPasswordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  DateTime? _dateOfBirth;
  String? _profileImageUrl;
  Uint8List? _imageBytes;
  bool _obscureCurrentPassword = true;
  bool _obscureNewPassword = true;
  bool _obscureConfirmPassword = true;
  String _passwordStrength = '';
  Color _passwordStrengthColor = Colors.grey;

  @override
  void initState() {
    super.initState();
    // Don't call _loadUserProfile here, it will be called in didChangeDependencies
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _loadUserProfile();
  }

  @override
  void dispose() {
    _fullNameController.dispose();
    _phoneController.dispose();
    _addressController.dispose();
    _currentPasswordController.dispose();
    _newPasswordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  Future<void> _loadUserProfile() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
    });

    try {
      // First ensure profile exists
      await serviceLocator.userProfileService.ensureProfileExists();

      // Then get the profile
      final profile =
          await serviceLocator.userProfileService.getCurrentUserProfile();

      if (profile != null && mounted) {
        setState(() {
          _userProfile = profile;
          _fullNameController.text = profile.fullName ?? '';
          _phoneController.text = profile.phoneNumber ?? '';
          _addressController.text = profile.address ?? '';
          _dateOfBirth = profile.dateOfBirth;
          _profileImageUrl = profile.profileImageUrl;
        });
      } else if (mounted) {
        // Handle case where profile is null but we're still mounted
        final user = serviceLocator.authService.currentUser;
        if (user != null) {
          // Create a default profile from user data
          final defaultProfile = UserProfile.fromUserMetadata(
            user.id,
            user.userMetadata,
            user.email,
          );

          setState(() {
            _userProfile = defaultProfile;
            _fullNameController.text = defaultProfile.fullName ?? '';
            _phoneController.text = defaultProfile.phoneNumber ?? '';
            _addressController.text = defaultProfile.address ?? '';
            _dateOfBirth = defaultProfile.dateOfBirth;
            _profileImageUrl = defaultProfile.profileImageUrl;
          });
        }
      }
    } catch (e) {
      AppLogger.error('Error loading user profile: $e');
      if (mounted) {
        // Now it's safe to use ScaffoldMessenger here
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to load profile: ${e.toString()}'),
            backgroundColor: Colors.red,
            action: SnackBarAction(
              label: 'Retry',
              textColor: Colors.white,
              onPressed: _loadUserProfile,
            ),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _saveProfile() async {
    if (_formKey.currentState?.validate() != true) return;

    setState(() {
      _isSaving = true;
    });

    try {
      // Check for internet connectivity first
      final hasInternet = await NetworkUtils.hasInternetConnection();
      if (!hasInternet && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text(
              'No internet connection. Please check your network settings and try again.',
            ),
            backgroundColor: Colors.orange,
            duration: const Duration(seconds: 5),
            action: SnackBarAction(
              label: 'Retry',
              textColor: Colors.white,
              onPressed: _saveProfile,
            ),
          ),
        );
        throw Exception('No internet connection');
      }
      
      if (_userProfile == null) {
        throw Exception('User profile not loaded');
      }

      // Upload image if selected
      String? imageUrl = _profileImageUrl;
      if (_imageBytes != null) {
        final timestamp = DateTime.now().millisecondsSinceEpoch;
        imageUrl = await serviceLocator.userProfileService.uploadProfileImage(
          _imageBytes!,
          'profile_$timestamp.jpg',
        );
      }

      // Update profile
      final updatedProfile = _userProfile!.copyWith(
        fullName: _fullNameController.text,
        phoneNumber: _phoneController.text,
        address: _addressController.text,
        dateOfBirth: _dateOfBirth,
        profileImageUrl: imageUrl,
      );

      final result = await serviceLocator.userProfileService.updateUserProfile(
        updatedProfile,
      );

      if (result != null && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Profile updated successfully'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 2),
          ),
        );

        setState(() {
          _userProfile = result;
          _profileImageUrl = result.profileImageUrl;
          _imageBytes = null;
        });
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('Failed to update profile. Please check your connection and try again.'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 4),
            action: SnackBarAction(
              label: 'Retry',
              textColor: Colors.white,
              onPressed: _saveProfile,
            ),
          ),
        );
      }
    } catch (e, stackTrace) {
      AppLogger.error('Error saving profile: $e');
      AppLogger.error('Stack trace: $stackTrace');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: ${e.toString()}'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 5),
            action: SnackBarAction(
              label: 'Retry',
              textColor: Colors.white,
              onPressed: _saveProfile,
            ),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSaving = false;
        });
      }
    }
  }

  Future<void> _changePassword() async {
    if (_passwordFormKey.currentState?.validate() != true) return;

    setState(() {
      _isChangingPassword = true;
    });

    try {
      // Update password with current password verification
      final result = await serviceLocator.authService.updatePassword(
        _currentPasswordController.text,
        _newPasswordController.text,
      );

      if (result && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Password changed successfully'),
            backgroundColor: Colors.green,
          ),
        );

        // Clear password fields
        _currentPasswordController.clear();
        _newPasswordController.clear();
        _confirmPasswordController.clear();

        // Close the dialog
        Navigator.of(context).pop();
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text(
              'Failed to change password. Please check your current password and try again.',
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      AppLogger.error('Error changing password: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isChangingPassword = false;
        });
      }
    }
  }

  // Check password strength and return feedback
  void _checkPasswordStrength(String password, StateSetter setDialogState) {
    if (password.isEmpty) {
      setDialogState(() {
        _passwordStrength = '';
        _passwordStrengthColor = Colors.grey;
      });
      return;
    }

    // Check for consecutive numbers like "123", "234", etc.
    bool hasConsecutiveNumbers = false;
    for (int i = 0; i < password.length - 2; i++) {
      if (password
          .substring(i, i + 3)
          .contains(
            RegExp(r'123|234|345|456|567|678|789|987|876|765|654|543|432|321'),
          )) {
        hasConsecutiveNumbers = true;
        break;
      }
    }

    // Check for name in password
    bool containsName = false;
    final fullName = _fullNameController.text.toLowerCase();
    if (fullName.isNotEmpty) {
      final nameParts = fullName.split(' ');
      for (final part in nameParts) {
        if (part.length > 2 && password.toLowerCase().contains(part)) {
          containsName = true;
          break;
        }
      }
    }

    // Check for other strength criteria
    bool hasUppercase = password.contains(RegExp(r'[A-Z]'));
    bool hasLowercase = password.contains(RegExp(r'[a-z]'));
    bool hasDigits = password.contains(RegExp(r'[0-9]'));
    bool hasSpecialChars = password.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]'));
    bool hasMinLength = password.length >= 6;

    // Calculate strength
    int strength = 0;
    if (hasMinLength) strength++;
    if (hasUppercase) strength++;
    if (hasLowercase) strength++;
    if (hasDigits) strength++;
    if (hasSpecialChars) strength++;
    if (!hasConsecutiveNumbers) strength++;
    if (!containsName) strength++;

    // Update strength message and color
    setDialogState(() {
      if (strength <= 2) {
        _passwordStrength = 'Weak';
        _passwordStrengthColor = Colors.red;
      } else if (strength <= 4) {
        _passwordStrength = 'Moderate';
        _passwordStrengthColor = Colors.orange;
      } else if (strength <= 6) {
        _passwordStrength = 'Strong';
        _passwordStrengthColor = Colors.green;
      } else {
        _passwordStrength = 'Very Strong';
        _passwordStrengthColor = Colors.green.shade800;
      }
    });
  }

  // Password validation
  String? _validatePassword(String? value) {
    final localizations = AppLocalizations.of(context);

    if (value == null || value.isEmpty) {
      return localizations?.translate('passwordRequired') ??
          'Password is required';
    }

    if (value.length < 6) {
      return localizations?.translate('passwordLength') ??
          'Password must be at least 6 characters';
    }

    // Check for consecutive numbers
    for (int i = 0; i < value.length - 2; i++) {
      if (value
          .substring(i, i + 3)
          .contains(
            RegExp(r'123|234|345|456|567|678|789|987|876|765|654|543|432|321'),
          )) {
        return localizations?.translate('passwordConsecutiveNumbers') ??
            'Password should not contain consecutive numbers like 123 or 321';
      }
    }

    // Check for name in password
    final fullName = _fullNameController.text.toLowerCase();
    if (fullName.isNotEmpty) {
      final nameParts = fullName.split(' ');
      for (final part in nameParts) {
        if (part.length > 2 && value.toLowerCase().contains(part)) {
          return localizations?.translate('passwordContainsName') ??
              'Password should not contain your name';
        }
      }
    }

    // Check for minimum complexity
    bool hasUpperOrLower = value.contains(RegExp(r'[A-Za-z]'));
    bool hasDigits = value.contains(RegExp(r'[0-9]'));

    if (!hasUpperOrLower || !hasDigits) {
      return localizations?.translate('passwordComplexity') ??
          'Password must include both letters and numbers';
    }

    return null;
  }

  void _showChangePasswordDialog() {
    final localizations = AppLocalizations.of(context);

    // Reset password visibility states and strength
    setState(() {
      _obscureCurrentPassword = true;
      _obscureNewPassword = true;
      _obscureConfirmPassword = true;
      _passwordStrength = '';
      _passwordStrengthColor = Colors.grey;
    });

    showDialog(
      context: context,
      builder:
          (context) => StatefulBuilder(
            builder:
                (context, setDialogState) => AlertDialog(
                  title: Text(
                    localizations?.translate('changePassword') ??
                        'Change Password',
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                  content: Form(
                    key: _passwordFormKey,
                    child: SingleChildScrollView(
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            localizations?.translate('passwordSecurityNote') ??
                                'For security, please enter your current password.',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey[600],
                            ),
                          ),
                          const SizedBox(height: 16),
                          TextFormField(
                            controller: _currentPasswordController,
                            decoration: InputDecoration(
                              labelText:
                                  localizations?.translate('currentPassword') ??
                                  'Current Password',
                              border: const OutlineInputBorder(),
                              prefixIcon: const Icon(Icons.lock_outline),
                              contentPadding: const EdgeInsets.symmetric(
                                vertical: 12,
                                horizontal: 16,
                              ),
                              suffixIcon: IconButton(
                                icon: Icon(
                                  _obscureCurrentPassword
                                      ? Icons.visibility_off
                                      : Icons.visibility,
                                  color: Colors.grey,
                                ),
                                onPressed: () {
                                  setDialogState(() {
                                    _obscureCurrentPassword =
                                        !_obscureCurrentPassword;
                                  });
                                },
                              ),
                            ),
                            obscureText: _obscureCurrentPassword,
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return localizations?.translate(
                                      'currentPasswordRequired',
                                    ) ??
                                    'Current password is required';
                              }
                              return null;
                            },
                          ),
                          const SizedBox(height: 16),
                          Divider(color: Colors.grey[300]),
                          const SizedBox(height: 16),
                          Text(
                            localizations?.translate('newPasswordTitle') ??
                                'New Password',
                            style: TextStyle(
                              fontWeight: FontWeight.w500,
                              color: Theme.of(context).colorScheme.primary,
                            ),
                          ),
                          const SizedBox(height: 12),
                          TextFormField(
                            controller: _newPasswordController,
                            decoration: InputDecoration(
                              labelText:
                                  localizations?.translate('newPassword') ??
                                  'New Password',
                              border: const OutlineInputBorder(),
                              prefixIcon: const Icon(Icons.lock_outline),
                              contentPadding: const EdgeInsets.symmetric(
                                vertical: 12,
                                horizontal: 16,
                              ),
                              suffixIcon: IconButton(
                                icon: Icon(
                                  _obscureNewPassword
                                      ? Icons.visibility_off
                                      : Icons.visibility,
                                  color: Colors.grey,
                                ),
                                onPressed: () {
                                  setDialogState(() {
                                    _obscureNewPassword = !_obscureNewPassword;
                                  });
                                },
                              ),
                            ),
                            obscureText: _obscureNewPassword,
                            validator: _validatePassword,
                            onChanged:
                                (value) => _checkPasswordStrength(
                                  value,
                                  setDialogState,
                                ),
                          ),
                          if (_passwordStrength.isNotEmpty)
                            Padding(
                              padding: const EdgeInsets.only(
                                top: 8.0,
                                left: 12.0,
                              ),
                              child: Row(
                                children: [
                                  Text(
                                    'Strength: ',
                                    style: TextStyle(
                                      fontSize: 12,
                                      color: Colors.grey[700],
                                    ),
                                  ),
                                  Text(
                                    _passwordStrength,
                                    style: TextStyle(
                                      fontSize: 12,
                                      fontWeight: FontWeight.bold,
                                      color: _passwordStrengthColor,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          Padding(
                            padding: const EdgeInsets.only(
                              top: 8.0,
                              left: 12.0,
                            ),
                            child: Text(
                              localizations?.translate(
                                    'passwordRequirements',
                                  ) ??
                                  'Password must be at least 6 characters and include both letters and numbers. It should not contain your name or consecutive numbers like "123".',
                              style: TextStyle(
                                fontSize: 11,
                                color: Colors.grey[600],
                                fontStyle: FontStyle.italic,
                              ),
                            ),
                          ),
                          const SizedBox(height: 16),
                          TextFormField(
                            controller: _confirmPasswordController,
                            decoration: InputDecoration(
                              labelText:
                                  localizations?.translate('confirmPassword') ??
                                  'Confirm Password',
                              border: const OutlineInputBorder(),
                              prefixIcon: const Icon(Icons.lock_outline),
                              contentPadding: const EdgeInsets.symmetric(
                                vertical: 12,
                                horizontal: 16,
                              ),
                              suffixIcon: IconButton(
                                icon: Icon(
                                  _obscureConfirmPassword
                                      ? Icons.visibility_off
                                      : Icons.visibility,
                                  color: Colors.grey,
                                ),
                                onPressed: () {
                                  setDialogState(() {
                                    _obscureConfirmPassword =
                                        !_obscureConfirmPassword;
                                  });
                                },
                              ),
                            ),
                            obscureText: _obscureConfirmPassword,
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return localizations?.translate(
                                      'confirmPasswordRequired',
                                    ) ??
                                    'Please confirm your password';
                              }
                              if (value != _newPasswordController.text) {
                                return localizations?.translate(
                                      'passwordsDoNotMatch',
                                    ) ??
                                    'Passwords do not match';
                              }
                              return null;
                            },
                          ),
                        ],
                      ),
                    ),
                  ),
                  actions: [
                    TextButton(
                      onPressed: () => Navigator.of(context).pop(),
                      child: Text(
                        localizations?.translate('cancel') ?? 'Cancel',
                        style: TextStyle(color: Colors.grey[700]),
                      ),
                    ),
                    ElevatedButton(
                      onPressed: _isChangingPassword ? null : _changePassword,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Theme.of(context).colorScheme.primary,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 10,
                        ),
                      ),
                      child:
                          _isChangingPassword
                              ? const SizedBox(
                                height: 16,
                                width: 16,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  color: Colors.white,
                                ),
                              )
                              : Text(
                                localizations?.translate('changePassword') ??
                                    'Change Password',
                              ),
                    ),
                  ],
                ),
          ),
    );
  }

  Future<void> _pickImage() async {
    try {
      final picker = ImagePicker();
      final pickedFile = await picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 800,
        maxHeight: 800,
        imageQuality: 85,
      );

      if (pickedFile != null) {
        final bytes = await pickedFile.readAsBytes();
        setState(() {
          _imageBytes = bytes;
        });
      }
    } catch (e) {
      AppLogger.error('Error picking image: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to pick image'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _selectDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate:
          _dateOfBirth ??
          DateTime.now().subtract(const Duration(days: 365 * 18)),
      firstDate: DateTime(1900),
      lastDate: DateTime.now(),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.light(
              primary: Theme.of(context).colorScheme.primary,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null && picked != _dateOfBirth) {
      setState(() {
        _dateOfBirth = picked;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: Text(localizations?.translate('profile') ?? 'Edit Profile'),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 0,
        centerTitle: true,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                // Progress indicator
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
                  color: Colors.white,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Complete your profile!',
                        style: TextStyle(
                          color: Theme.of(context).colorScheme.primary,
                          fontWeight: FontWeight.w600,
                          fontSize: 16,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Add your details to get the most out of your property management experience.',
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: 12,
                        ),
                      ),
                      const SizedBox(height: 12),
                      LinearProgressIndicator(
                        value: 0.8,
                        backgroundColor: Colors.grey[300],
                        valueColor: AlwaysStoppedAnimation<Color>(
                          Theme.of(context).colorScheme.primary,
                        ),
                      ),
                    ],
                  ),
                ),
                
                // Profile content
                Expanded(
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(20.0),
                    child: Form(
                      key: _formKey,
                      child: Column(
                        children: [
                          // Profile Image
                          GestureDetector(
                            onTap: _pickImage,
                            child: Stack(
                              children: [
                                Container(
                                  width: 100,
                                  height: 100,
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    color: Colors.grey[200],
                                    image: _imageBytes != null
                                        ? DecorationImage(
                                            image: MemoryImage(_imageBytes!),
                                            fit: BoxFit.cover,
                                          )
                                        : _profileImageUrl != null
                                            ? DecorationImage(
                                                image: NetworkImage(_profileImageUrl!),
                                                fit: BoxFit.cover,
                                              )
                                            : null,
                                  ),
                                  child: _imageBytes == null && _profileImageUrl == null
                                      ? Icon(
                                          Icons.person,
                                          size: 50,
                                          color: Colors.grey[400],
                                        )
                                      : null,
                                ),
                                Positioned(
                                  bottom: 0,
                                  right: 0,
                                  child: Container(
                                    padding: const EdgeInsets.all(6),
                                    decoration: BoxDecoration(
                                      color: Theme.of(context).colorScheme.primary,
                                      shape: BoxShape.circle,
                                      border: Border.all(color: Colors.white, width: 2),
                                    ),
                                    child: const Icon(
                                      Icons.edit,
                                      color: Colors.white,
                                      size: 16,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          const SizedBox(height: 30),

                          // Name Field
                          _buildTextField(
                            controller: _fullNameController,
                            label: 'Name',
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'Name is required';
                              }
                              return null;
                            },
                          ),
                          const SizedBox(height: 20),

                          // Email Field (Read-only)
                          _buildTextField(
                            initialValue: _userProfile?.email ?? '',
                            label: 'Email Address',
                            readOnly: true,
                            suffixIcon: Container(
                              margin: const EdgeInsets.all(8),
                              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                              decoration: BoxDecoration(
                                color: Colors.blue[50],
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Text(
                                'VERIFIED',
                                style: TextStyle(
                                  color: Colors.blue[600],
                                  fontSize: 10,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                          ),
                          const SizedBox(height: 20),

                          // Phone Field
                          _buildTextField(
                            controller: _phoneController,
                            label: 'Phone Number',
                            keyboardType: TextInputType.phone,
                          ),
                          const SizedBox(height: 20),

                          // Date of Birth Field
                          GestureDetector(
                            onTap: _selectDate,
                            child: _buildTextField(
                              initialValue: _dateOfBirth != null
                                  ? DateFormat('dd/MM/yyyy').format(_dateOfBirth!)
                                  : '',
                              label: 'Date of Birth',
                              readOnly: true,
                              suffixIcon: const Icon(Icons.calendar_today, size: 20),
                            ),
                          ),
                          const SizedBox(height: 20),

                          // Address Field
                          _buildTextField(
                            controller: _addressController,
                            label: 'Address',
                            keyboardType: TextInputType.streetAddress,
                          ),
                          const SizedBox(height: 40),

                          // Save Button
                          SizedBox(
                            width: double.infinity,
                            child: ElevatedButton(
                              onPressed: _isSaving ? null : _saveProfile,
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Theme.of(context).colorScheme.primary,
                                foregroundColor: Colors.white,
                                padding: const EdgeInsets.symmetric(vertical: 16),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                elevation: 0,
                              ),
                              child: _isSaving
                                  ? const SizedBox(
                                      height: 20,
                                      width: 20,
                                      child: CircularProgressIndicator(
                                        strokeWidth: 2,
                                        color: Colors.white,
                                      ),
                                    )
                                  : const Text(
                                      'Save Changes',
                                      style: TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                            ),
                          ),
                          const SizedBox(height: 20),

                          // Security Settings
                          Container(
                            width: double.infinity,
                            padding: const EdgeInsets.all(16),
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(12),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.grey.withValues(alpha: 0.1),
                                  blurRadius: 10,
                                  offset: const Offset(0, 2),
                                ),
                              ],
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Security Settings',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                    color: Colors.grey[800],
                                  ),
                                ),
                                const SizedBox(height: 12),
                                ListTile(
                                  contentPadding: EdgeInsets.zero,
                                  leading: Container(
                                    padding: const EdgeInsets.all(8),
                                    decoration: BoxDecoration(
                                      color: Colors.orange[50],
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    child: Icon(
                                      Icons.lock_outline,
                                      color: Colors.orange[600],
                                      size: 20,
                                    ),
                                  ),
                                  title: const Text(
                                    'Change Password',
                                    style: TextStyle(fontWeight: FontWeight.w500),
                                  ),
                                  trailing: const Icon(
                                    Icons.arrow_forward_ios,
                                    size: 16,
                                    color: Colors.grey,
                                  ),
                                  onTap: _showChangePasswordDialog,
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
    );
  }

  Widget _buildTextField({
    TextEditingController? controller,
    String? initialValue,
    required String label,
    bool readOnly = false,
    TextInputType? keyboardType,
    Widget? suffixIcon,
    String? Function(String?)? validator,
  }) {
    return TextFormField(
      controller: controller,
      initialValue: controller == null ? initialValue : null,
      readOnly: readOnly,
      keyboardType: keyboardType,
      validator: validator,
      style: TextStyle(
        color: readOnly ? Colors.grey[600] : Colors.black,
      ),
      decoration: InputDecoration(
        labelText: label,
        labelStyle: TextStyle(
          color: Colors.grey[600],
          fontSize: 14,
        ),
        filled: true,
        fillColor: Colors.white,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.grey[300]!),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.grey[300]!),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Theme.of(context).colorScheme.primary),
        ),
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
        suffixIcon: suffixIcon,
      ),
    );
  }
}
