import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../models/property/property_model.dart';
import '../../models/room/room_model.dart';
import '../../utils/logger.dart';
import '../service_locator.dart';

/// Exception thrown when trying to delete a property that has rooms
class PropertyHasRoomsException implements Exception {
  final String message;
  final List<Room> rooms;

  PropertyHasRoomsException(this.message, this.rooms);

  @override
  String toString() => message;
}

/// Service for managing properties using Supabase
class PropertyService {
  // Stream controller for property changes
  final _propertyStreamController =
      StreamController<List<Property>>.broadcast();

  // Singleton instance
  static final PropertyService _instance = PropertyService._internal();

  // Supabase client
  late final SupabaseClient _supabase;

  // Factory constructor
  factory PropertyService() {
    return _instance;
  }

  // Private constructor
  PropertyService._internal() {
    _supabase = Supabase.instance.client;
    _refreshProperties();
  }

  /// Get stream of property updates
  Stream<List<Property>> get propertiesStream =>
      _propertyStreamController.stream;

  /// Fetch all properties for the current user
  Future<List<Property>> _fetchAllProperties() async {
    return await serviceLocator.supabaseService.executeWithRefresh(() async {
      try {
        // Use the view that properly joins properties with utility bills
        final response = await _supabase
            .from('properties_with_utility_bills')
            .select()
            .order('created_at');

        final List<dynamic> data = response;
        return data.map((propertyData) {
          // Extract utility bills from the utility_bills JSONB column
          final List<dynamic> utilityBillsData =
              propertyData['utility_bills'] ?? [];
          final utilityBills =
              utilityBillsData
                  .map(
                    (billData) =>
                        UtilityBill.fromJson(billData as Map<String, dynamic>),
                  )
                  .toList();

          // Extract payment details from JSON column
          List<PaymentDetails> paymentDetailsList = [];
          if (propertyData['payment_details'] != null) {
            final paymentData = propertyData['payment_details'];
            if (paymentData is List) {
              // Array format (preferred)
              paymentDetailsList =
                  paymentData
                      .map(
                        (e) =>
                            PaymentDetails.fromJson(e as Map<String, dynamic>),
                      )
                      .toList();
            } else if (paymentData is Map<String, dynamic>) {
              // Single object format (backwards compatibility)
              paymentDetailsList = [PaymentDetails.fromJson(paymentData)];
            }
          } else if (propertyData['payment_method'] != null) {
            // Fallback to legacy individual columns format
            final method = PaymentMethod.values.firstWhere(
              (e) =>
                  e.toString().split('.').last ==
                  propertyData['payment_method'],
              orElse: () => PaymentMethod.cash,
            );
            paymentDetailsList = [
              PaymentDetails(
                method: method,
                paybillNumber: propertyData['paybill_number'] as String?,
                tillNumber: propertyData['till_number'] as String?,
                bankName: propertyData['bank_name'] as String?,
                accountNumber: propertyData['account_number'] as String?,
                notes: propertyData['payment_notes'] as String?,
              ),
            ];
          }

          // Create property with the extracted utility bills and payment details
          final property = Property.fromJson(propertyData);

          // Set utility bills and payment details properly
          return property.copyWith(
            utilityBills: utilityBills,
            paymentDetails: paymentDetailsList,
          );
        }).toList();
      } catch (error) {
        AppLogger.error('Error fetching properties', error);
        return [];
      }
    });
  }

  /// Manually refresh properties data
  Future<void> _refreshProperties() async {
    final properties = await _fetchAllProperties();
    if (!_propertyStreamController.isClosed) {
      _propertyStreamController.add(properties);
    }
  }

  /// Get all properties
  Future<List<Property>> getAllProperties() async {
    final properties = await _fetchAllProperties();

    // Update the stream with fresh data
    if (!_propertyStreamController.isClosed) {
      _propertyStreamController.add(properties);
    }

    return properties;
  }

  /// Get a property by ID
  Future<Property?> getPropertyById(String id) async {
    return await serviceLocator.supabaseService.executeWithRefresh(() async {
      try {
        // Only log this in debug mode to avoid noisy output in production
        if (kDebugMode) {
          AppLogger.info('Fetching property by ID: $id');
        }

        // Use the view that properly joins properties with utility bills
        final response =
            await _supabase
                .from('properties_with_utility_bills')
                .select()
                .eq('id', id)
                .single();

        final propertyData = response;

        // Extract utility bills from the utility_bills JSONB column
        final List<dynamic> utilityBillsData =
            propertyData['utility_bills'] ?? [];
        final utilityBills =
            utilityBillsData
                .map(
                  (billData) =>
                      UtilityBill.fromJson(billData as Map<String, dynamic>),
                )
                .toList();

        // Extract payment details from JSON column
        List<PaymentDetails> paymentDetailsList = [];
        if (propertyData['payment_details'] != null) {
          final paymentData = propertyData['payment_details'];
          if (paymentData is List) {
            // Array format (preferred)
            paymentDetailsList =
                paymentData
                    .map(
                      (e) => PaymentDetails.fromJson(e as Map<String, dynamic>),
                    )
                    .toList();
          } else if (paymentData is Map<String, dynamic>) {
            // Single object format (backwards compatibility)
            paymentDetailsList = [PaymentDetails.fromJson(paymentData)];
          }
        } else if (propertyData['payment_method'] != null) {
          // Fallback to legacy individual columns format
          final method = PaymentMethod.values.firstWhere(
            (e) =>
                e.toString().split('.').last == propertyData['payment_method'],
            orElse: () => PaymentMethod.cash,
          );
          paymentDetailsList = [
            PaymentDetails(
              method: method,
              paybillNumber: propertyData['paybill_number'] as String?,
              tillNumber: propertyData['till_number'] as String?,
              bankName: propertyData['bank_name'] as String?,
              accountNumber: propertyData['account_number'] as String?,
              notes: propertyData['payment_notes'] as String?,
            ),
          ];
        }

        // Create property with the extracted utility bills and payment details
        final property = Property.fromJson(propertyData);

        // Set utility bills and payment details properly
        return property.copyWith(
          utilityBills: utilityBills,
          paymentDetails: paymentDetailsList,
        );
      } catch (error) {
        AppLogger.error('Error fetching property by ID: $error');
        return null;
      }
    });
  }

  /// Add a new property
  Future<Property> addProperty({
    required String name,
    required String address,
    required String city,
    required String state,
    required String zipCode,
    String? description,
    String? imageUrl,
    List<UtilityBill>? utilityBills,
    List<PaymentDetails>? paymentDetails,
    Map<String, dynamic>? additionalInfo,
  }) async {
    return await serviceLocator.supabaseService.executeWithRefresh(() async {
      try {
        // Get the current user's ID
        final userId = _supabase.auth.currentUser?.id;
        if (userId == null) {
          throw Exception('User is not authenticated');
        }

        // Prepare the insert data
        final insertData = {
          'name': name,
          'address': address,
          'city': city,
          'state': state,
          'zip_code': zipCode,
          'description': description,
          'image_url': imageUrl,
          'additional_info': additionalInfo,
          'user_id': userId, // Set the user_id field
        };

        // Only add payment_details if it exists (for backward compatibility)
        if (paymentDetails != null && paymentDetails.isNotEmpty) {
          try {
            insertData['payment_details'] =
                paymentDetails.map((p) => p.toJson()).toList();
          } catch (e) {
            // If payment_details column doesn't exist, continue without it
            AppLogger.warning(
              'Payment details column may not exist in database',
              e,
            );
          }
        }

        // Insert the property
        final response =
            await _supabase
                .from('properties')
                .insert(insertData)
                .select()
                .single();

        final propertyId = response['id'];

        // Add utility bills if provided
        if (utilityBills != null && utilityBills.isNotEmpty) {
          final billsData =
              utilityBills
                  .map(
                    (bill) => {
                      'property_id': propertyId,
                      'name': bill.name,
                      'rate': bill.rate,
                      'unit': bill.unit,
                      'notes': bill.notes,
                    },
                  )
                  .toList();

          await _supabase.from('utility_bills').insert(billsData);
        }

        // Refresh the properties stream
        _refreshProperties();

        // Return the newly created property with utility bills
        return await getPropertyById(propertyId) ??
            Property(
              id: propertyId,
              name: name,
              address: address,
              city: city,
              state: state,
              zipCode: zipCode,
              description: description,
              imageUrl: imageUrl,
              utilityBills: utilityBills ?? [],
              paymentDetails: paymentDetails ?? [],
              additionalInfo: additionalInfo,
              createdAt: DateTime.now(),
              updatedAt: DateTime.now(),
            );
      } catch (error) {
        AppLogger.error('Error adding property', error);
        throw Exception('Failed to add property: $error');
      }
    });
  }

  /// Update an existing property
  Future<Property?> updateProperty({
    required String id,
    String? name,
    String? address,
    String? city,
    String? state,
    String? zipCode,
    String? description,
    String? imageUrl,
    List<UtilityBill>? utilityBills,
    List<PaymentDetails>? paymentDetails,
    Map<String, dynamic>? additionalInfo,
  }) async {
    return await serviceLocator.supabaseService.executeWithRefresh(() async {
      try {
        // Get current property to determine changes
        final currentProperty = await getPropertyById(id);
        if (currentProperty == null) {
          return null;
        }

        // Update property data
        final updateData = <String, dynamic>{};
        if (name != null) updateData['name'] = name;
        if (address != null) updateData['address'] = address;
        if (city != null) updateData['city'] = city;
        if (state != null) updateData['state'] = state;
        if (zipCode != null) updateData['zip_code'] = zipCode;
        if (description != null) updateData['description'] = description;
        if (imageUrl != null) updateData['image_url'] = imageUrl;
        // Only update payment_details if we can (for backward compatibility)
        try {
          if (paymentDetails != null) {
            if (paymentDetails.isNotEmpty) {
              updateData['payment_details'] =
                  paymentDetails.map((p) => p.toJson()).toList();
            } else {
              // Empty list - clear existing payment details
              updateData['payment_details'] = null;
            }
          }
        } catch (e) {
          // If payment_details column doesn't exist, continue without it
          AppLogger.warning(
            'Payment details column may not exist in database',
            e,
          );
        }
        if (additionalInfo != null)
          updateData['additional_info'] = additionalInfo;

        if (updateData.isNotEmpty) {
          await _supabase.from('properties').update(updateData).eq('id', id);
        }

        // Update utility bills if provided
        if (utilityBills != null) {
          // Delete existing utility bills
          await _supabase.from('utility_bills').delete().eq('property_id', id);

          // Insert new utility bills
          if (utilityBills.isNotEmpty) {
            final billsData =
                utilityBills
                    .map(
                      (bill) => {
                        'property_id': id,
                        'name': bill.name,
                        'rate': bill.rate,
                        'unit': bill.unit,
                        'notes': bill.notes,
                      },
                    )
                    .toList();

            await _supabase.from('utility_bills').insert(billsData);
          }
        }

        // Refresh the properties stream
        _refreshProperties();

        // Return the updated property
        return await getPropertyById(id);
      } catch (error) {
        AppLogger.error('Error updating property', error);
        return null;
      }
    });
  }

  /// Check if property has rooms
  Future<List<Room>> getRoomsInProperty(String propertyId) async {
    try {
      return await serviceLocator.roomService.getRoomsByPropertyId(propertyId);
    } catch (error) {
      AppLogger.error('Error checking rooms in property', error);
      return [];
    }
  }

  /// Delete a property (with optional room reassignment)
  Future<bool> deleteProperty(String id, {bool forceDelete = false}) async {
    return await serviceLocator.supabaseService.executeWithRefresh(() async {
      try {
        // Check if property has rooms and forceDelete is false
        if (!forceDelete) {
          final rooms = await getRoomsInProperty(id);
          if (rooms.isNotEmpty) {
            throw PropertyHasRoomsException(
              'Property has ${rooms.length} room(s) that must be reassigned before deletion',
              rooms,
            );
          }
        }

        // The utility bills and rooms will be automatically deleted due to the CASCADE constraint
        await _supabase.from('properties').delete().eq('id', id);

        // Refresh the properties stream
        _refreshProperties();

        return true;
      } catch (error) {
        if (error is PropertyHasRoomsException) {
          rethrow;
        }
        AppLogger.error('Error deleting property', error);
        return false;
      }
    });
  }

  /// Add or update a utility bill for a property
  Future<bool> updateUtilityBill(
    String propertyId,
    UtilityBill bill, {
    String? billId,
  }) async {
    return await serviceLocator.supabaseService.executeWithRefresh(() async {
      try {
        if (billId != null) {
          // Update existing bill
          await _supabase
              .from('utility_bills')
              .update({
                'name': bill.name,
                'rate': bill.rate,
                'unit': bill.unit,
                'notes': bill.notes,
              })
              .eq('id', billId);
        } else {
          // Add new bill
          await _supabase.from('utility_bills').insert({
            'property_id': propertyId,
            'name': bill.name,
            'rate': bill.rate,
            'unit': bill.unit,
            'notes': bill.notes,
          });
        }
        return true;
      } catch (error) {
        AppLogger.error('Error updating utility bill', error);
        return false;
      }
    });
  }

  /// Remove a utility bill
  Future<bool> removeUtilityBill(String billId) async {
    return await serviceLocator.supabaseService.executeWithRefresh(() async {
      try {
        await _supabase.from('utility_bills').delete().eq('id', billId);
        return true;
      } catch (error) {
        AppLogger.error('Error removing utility bill', error);
        return false;
      }
    });
  }

  /// Dispose of resources
  void dispose() {
    _propertyStreamController.close();
  }
}
