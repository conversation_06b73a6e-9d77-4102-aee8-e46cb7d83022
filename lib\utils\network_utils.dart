import 'dart:io';
import '../utils/logger.dart';

class NetworkUtils {
  /// Check if the device has internet connectivity
  static Future<bool> hasInternetConnection() async {
    try {
      // Try to connect to a reliable server
      final result = await InternetAddress.lookup('google.com');
      if (result.isNotEmpty && result[0].rawAddress.isNotEmpty) {
        return true;
      }
      return false;
    } on SocketException catch (e) {
      AppLogger.error('No internet connection: $e');
      return false;
    } catch (e) {
      AppLogger.error('Error checking internet connection: $e');
      return false;
    }
  }
}