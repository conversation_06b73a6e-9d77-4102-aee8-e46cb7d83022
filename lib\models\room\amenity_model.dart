/// Represents an amenity for a room
class Amenity {
  final String? id;
  String name;
  String? description;
  String? icon;
  bool isPredefined;
  DateTime? createdAt;
  DateTime? updatedAt;

  Amenity({
    this.id,
    required this.name,
    this.description,
    this.icon,
    this.isPredefined = false,
    this.createdAt,
    this.updatedAt,
  });

  /// Create an amenity from JSON data
  factory Amenity.fromJson(Map<String, dynamic> json) {
    return Amenity(
      id: json['id'] as String?,
      name: json['name'] as String,
      description: json['description'] as String?,
      icon: json['icon'] as String?,
      isPredefined: json['is_predefined'] as bool? ?? false,
      createdAt: json['created_at'] != null 
          ? DateTime.parse(json['created_at'] as String) 
          : null,
      updatedAt: json['updated_at'] != null 
          ? DateTime.parse(json['updated_at'] as String) 
          : null,
    );
  }

  /// Convert amenity to JSON
  Map<String, dynamic> toJ<PERSON>() {
    return {
      if (id != null) 'id': id,
      'name': name,
      if (description != null) 'description': description,
      if (icon != null) 'icon': icon,
      'is_predefined': isPredefined,
      if (createdAt != null) 'created_at': createdAt!.toIso8601String(),
      if (updatedAt != null) 'updated_at': updatedAt!.toIso8601String(),
    };
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    
    return other is Amenity &&
      other.id == id &&
      other.name == name &&
      other.description == description &&
      other.icon == icon &&
      other.isPredefined == isPredefined;
  }

  @override
  int get hashCode {
    return id.hashCode ^
      name.hashCode ^
      description.hashCode ^
      icon.hashCode ^
      isPredefined.hashCode;
  }
}

/// Represents a room-amenity association that can be either a predefined amenity
/// or a custom amenity name
class RoomAmenity {
  final String? id;
  final String roomId;
  final String? amenityId;
  final String? customAmenityName;
  final Amenity? amenity; // Populated when fetching from database with join
  final DateTime? createdAt;
  final DateTime? updatedAt;

  RoomAmenity({
    this.id,
    required this.roomId,
    this.amenityId,
    this.customAmenityName,
    this.amenity,
    this.createdAt,
    this.updatedAt,
  }) : assert(
         (amenityId != null && customAmenityName == null) || 
         (amenityId == null && customAmenityName != null),
         'Either amenityId or customAmenityName must be provided, but not both'
       );

  /// Create a room amenity from JSON data
  factory RoomAmenity.fromJson(Map<String, dynamic> json) {
    return RoomAmenity(
      id: json['id'] as String?,
      roomId: json['room_id'] as String,
      amenityId: json['amenity_id'] as String?,
      customAmenityName: json['custom_amenity_name'] as String?,
      amenity: json['amenity'] != null 
          ? Amenity.fromJson(json['amenity'] as Map<String, dynamic>) 
          : null,
      createdAt: json['created_at'] != null 
          ? DateTime.parse(json['created_at'] as String) 
          : null,
      updatedAt: json['updated_at'] != null 
          ? DateTime.parse(json['updated_at'] as String) 
          : null,
    );
  }

  /// Convert room amenity to JSON
  Map<String, dynamic> toJson() {
    return {
      if (id != null) 'id': id,
      'room_id': roomId,
      if (amenityId != null) 'amenity_id': amenityId,
      if (customAmenityName != null) 'custom_amenity_name': customAmenityName,
      if (createdAt != null) 'created_at': createdAt!.toIso8601String(),
      if (updatedAt != null) 'updated_at': updatedAt!.toIso8601String(),
    };
  }

  /// Get the display name of this amenity
  String get displayName {
    if (amenity != null) {
      return amenity!.name;
    }
    return customAmenityName ?? '';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    
    return other is RoomAmenity &&
      other.id == id &&
      other.roomId == roomId &&
      other.amenityId == amenityId &&
      other.customAmenityName == customAmenityName;
  }

  @override
  int get hashCode {
    return id.hashCode ^
      roomId.hashCode ^
      amenityId.hashCode ^
      customAmenityName.hashCode;
  }
} 