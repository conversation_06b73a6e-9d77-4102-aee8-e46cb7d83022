import 'package:uuid/uuid.dart';
import 'package:flutter/material.dart';

enum ActivityType {
  // Authentication & User Activities
  login,
  logout,

  // Property Activities
  propertyCreated,
  propertyUpdated,
  propertyDeleted,

  // Room Activities
  roomCreated,
  roomUpdated,
  roomDeleted,
  roomStatusChanged,
  roomAssignment,
  roomVacated,
  roomInspection,
  roomCleaning,
  roomRepair,

  // Tenant Activities
  tenantCreated,
  tenantUpdated,
  tenantDeleted,
  tenantActivated,
  tenantDeactivated,
  tenantMovedOut,
  tenantReactivated,
  tenantProfileUpdated,
  tenantContactUpdated,

  // Lease Activities
  leaseCreated,
  leaseUpdated,
  leaseRenewed,
  leaseTerminated,
  leaseExpired,
  vacateNotice,

  // Payment Activities
  paymentReceived,
  paymentRefunded,
  paymentOverdue,
  paymentReminder,
  depositReceived,
  depositRefunded,

  // Maintenance Activities
  maintenanceRequest,
  maintenanceScheduled,
  maintenanceInProgress,
  maintenanceCompleted,
  maintenanceCancelled,
  emergencyRepair,

  // Communication Activities
  messageReceived,
  messageSent,
  notificationSent,
  emailSent,
  smsReceived,

  // Document Activities
  documentUploaded,
  documentDeleted,
  documentShared,
  contractSigned,

  // Inspection Activities
  inspectionScheduled,
  inspectionCompleted,
  inspectionFailed,

  // Security Activities
  keyIssued,
  keyReturned,
  accessGranted,
  accessRevoked,

  // Billing Activities
  billGenerated,
  billSent,
  billPaid,
  billOverdue,

  // System Activities
  dataExported,
  backupCreated,
  systemUpdate,

  other
}

class ActivityLog {
  final String id;
  final ActivityType type;
  final String? userId;
  final String? tenantId;
  final String? roomId;
  final String? propertyId;
  final String action;
  final Map<String, dynamic>? details;
  final DateTime createdAt;

  ActivityLog({
    String? id,
    required this.type,
    this.userId,
    this.tenantId,
    this.roomId,
    this.propertyId,
    required this.action,
    this.details,
    DateTime? createdAt,
  })  : id = id ?? const Uuid().v4(),
        createdAt = createdAt ?? DateTime.now();

  // Create ActivityLog from JSON
  factory ActivityLog.fromJson(Map<String, dynamic> json) {
    return ActivityLog(
      id: json['id'],
      type: _parseActivityType(json['type']),
      userId: json['user_id'],
      tenantId: json['tenant_id'],
      roomId: json['room_id'],
      propertyId: json['property_id'],
      action: json['action'],
      details: json['details'],
      createdAt: DateTime.parse(json['created_at']),
    );
  }

  // Convert ActivityLog to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.name,
      'user_id': userId,
      'tenant_id': tenantId,
      'room_id': roomId,
      'property_id': propertyId,
      'action': action,
      'details': details,
      'created_at': createdAt.toIso8601String(),
    };
  }

  // Parse activity type from string
  static ActivityType _parseActivityType(String? type) {
    if (type == null) return ActivityType.other;

    // Try to match the enum name directly first
    for (ActivityType activityType in ActivityType.values) {
      if (activityType.name == type) {
        return activityType;
      }
    }

    // Legacy mappings for backward compatibility
    switch (type) {
      case 'tenantCreation':
        return ActivityType.tenantCreated;
      case 'tenantUpdate':
        return ActivityType.tenantUpdated;
      case 'roomUpdate':
        return ActivityType.roomUpdated;
      case 'propertyUpdate':
        return ActivityType.propertyUpdated;
      default:
        return ActivityType.other;
    }
  }
}

class ActivityLogEntry {
  final String id;
  final String action;
  final String description;
  final DateTime createdAt;
  final Map<String, dynamic>? changes;
  final String? userId;
  final String entityId;
  final String entityType;

  ActivityLogEntry({
    required this.id,
    required this.action,
    required this.description,
    required this.createdAt,
    this.changes,
    this.userId,
    required this.entityId,
    required this.entityType,
  });

  factory ActivityLogEntry.fromJson(Map<String, dynamic> json) {
    return ActivityLogEntry(
      id: json['id'] as String,
      action: json['action'] as String,
      description: json['description'] as String,
      createdAt: DateTime.parse(json['created_at'] as String),
      changes: json['changes'] as Map<String, dynamic>?,
      userId: json['user_id'] as String?,
      entityId: json['entity_id'] as String,
      entityType: json['entity_type'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'action': action,
      'description': description,
      'created_at': createdAt.toIso8601String(),
      'changes': changes,
      'user_id': userId,
      'entity_id': entityId,
      'entity_type': entityType,
    };
  }
}

/// Helper class to provide UI styling for different activity types
class ActivityTypeHelper {
  /// Get icon for activity type
  static IconData getIcon(ActivityType type) {
    switch (type) {
      // Authentication & User Activities
      case ActivityType.login:
        return Icons.login;
      case ActivityType.logout:
        return Icons.logout;

      // Property Activities
      case ActivityType.propertyCreated:
        return Icons.add_business;
      case ActivityType.propertyUpdated:
        return Icons.edit_location;
      case ActivityType.propertyDeleted:
        return Icons.delete_forever;

      // Room Activities
      case ActivityType.roomCreated:
        return Icons.add_home;
      case ActivityType.roomUpdated:
        return Icons.edit;
      case ActivityType.roomDeleted:
        return Icons.delete;
      case ActivityType.roomStatusChanged:
        return Icons.swap_horiz;
      case ActivityType.roomAssignment:
        return Icons.person_add;
      case ActivityType.roomVacated:
        return Icons.exit_to_app;
      case ActivityType.roomInspection:
        return Icons.search;
      case ActivityType.roomCleaning:
        return Icons.cleaning_services;
      case ActivityType.roomRepair:
        return Icons.build;

      // Tenant Activities
      case ActivityType.tenantCreated:
        return Icons.person_add;
      case ActivityType.tenantUpdated:
        return Icons.person;
      case ActivityType.tenantDeleted:
        return Icons.person_remove;
      case ActivityType.tenantActivated:
        return Icons.person_add_alt;
      case ActivityType.tenantDeactivated:
        return Icons.person_off;
      case ActivityType.tenantMovedOut:
        return Icons.exit_to_app;
      case ActivityType.tenantReactivated:
        return Icons.person_add_alt_1;
      case ActivityType.tenantProfileUpdated:
        return Icons.account_circle;
      case ActivityType.tenantContactUpdated:
        return Icons.contact_phone;

      // Lease Activities
      case ActivityType.leaseCreated:
        return Icons.description;
      case ActivityType.leaseUpdated:
        return Icons.edit_note;
      case ActivityType.leaseRenewed:
        return Icons.refresh;
      case ActivityType.leaseTerminated:
        return Icons.cancel;
      case ActivityType.leaseExpired:
        return Icons.schedule;
      case ActivityType.vacateNotice:
        return Icons.notification_important;

      // Payment Activities
      case ActivityType.paymentReceived:
        return Icons.payment;
      case ActivityType.paymentRefunded:
        return Icons.money_off;
      case ActivityType.paymentOverdue:
        return Icons.warning;
      case ActivityType.paymentReminder:
        return Icons.notifications;
      case ActivityType.depositReceived:
        return Icons.account_balance;
      case ActivityType.depositRefunded:
        return Icons.account_balance_wallet;

      // Maintenance Activities
      case ActivityType.maintenanceRequest:
        return Icons.report_problem;
      case ActivityType.maintenanceScheduled:
        return Icons.schedule;
      case ActivityType.maintenanceInProgress:
        return Icons.construction;
      case ActivityType.maintenanceCompleted:
        return Icons.check_circle;
      case ActivityType.maintenanceCancelled:
        return Icons.cancel;
      case ActivityType.emergencyRepair:
        return Icons.emergency;

      // Communication Activities
      case ActivityType.messageReceived:
        return Icons.message;
      case ActivityType.messageSent:
        return Icons.send;
      case ActivityType.notificationSent:
        return Icons.notifications_active;
      case ActivityType.emailSent:
        return Icons.email;
      case ActivityType.smsReceived:
        return Icons.sms;

      // Document Activities
      case ActivityType.documentUploaded:
        return Icons.upload_file;
      case ActivityType.documentDeleted:
        return Icons.delete_outline;
      case ActivityType.documentShared:
        return Icons.share;
      case ActivityType.contractSigned:
        return Icons.verified;

      // Inspection Activities
      case ActivityType.inspectionScheduled:
        return Icons.event;
      case ActivityType.inspectionCompleted:
        return Icons.fact_check;
      case ActivityType.inspectionFailed:
        return Icons.error;

      // Security Activities
      case ActivityType.keyIssued:
        return Icons.key;
      case ActivityType.keyReturned:
        return Icons.key_off;
      case ActivityType.accessGranted:
        return Icons.lock_open;
      case ActivityType.accessRevoked:
        return Icons.lock;

      // Billing Activities
      case ActivityType.billGenerated:
        return Icons.receipt;
      case ActivityType.billSent:
        return Icons.send;
      case ActivityType.billPaid:
        return Icons.paid;
      case ActivityType.billOverdue:
        return Icons.warning;

      // System Activities
      case ActivityType.dataExported:
        return Icons.download;
      case ActivityType.backupCreated:
        return Icons.backup;
      case ActivityType.systemUpdate:
        return Icons.system_update;

      case ActivityType.other:
        return Icons.event_note;
    }
  }

  /// Get color for activity type
  static Color getColor(ActivityType type) {
    switch (type) {
      // Authentication & User Activities
      case ActivityType.login:
      case ActivityType.logout:
        return Colors.blue;

      // Property Activities
      case ActivityType.propertyCreated:
        return Colors.green;
      case ActivityType.propertyUpdated:
        return Colors.orange;
      case ActivityType.propertyDeleted:
        return Colors.red;

      // Room Activities
      case ActivityType.roomCreated:
        return Colors.green;
      case ActivityType.roomUpdated:
        return Colors.orange;
      case ActivityType.roomDeleted:
        return Colors.red;
      case ActivityType.roomStatusChanged:
        return Colors.blue;
      case ActivityType.roomAssignment:
        return Colors.green;
      case ActivityType.roomVacated:
        return Colors.orange;
      case ActivityType.roomInspection:
        return Colors.purple;
      case ActivityType.roomCleaning:
        return Colors.cyan;
      case ActivityType.roomRepair:
        return Colors.brown;

      // Tenant Activities
      case ActivityType.tenantCreated:
      case ActivityType.tenantActivated:
      case ActivityType.tenantReactivated:
        return Colors.green;
      case ActivityType.tenantUpdated:
      case ActivityType.tenantProfileUpdated:
      case ActivityType.tenantContactUpdated:
        return Colors.orange;
      case ActivityType.tenantDeleted:
      case ActivityType.tenantDeactivated:
      case ActivityType.tenantMovedOut:
        return Colors.red;

      // Lease Activities
      case ActivityType.leaseCreated:
      case ActivityType.leaseRenewed:
        return Colors.green;
      case ActivityType.leaseUpdated:
        return Colors.orange;
      case ActivityType.leaseTerminated:
      case ActivityType.leaseExpired:
        return Colors.red;
      case ActivityType.vacateNotice:
        return Colors.amber;

      // Payment Activities
      case ActivityType.paymentReceived:
      case ActivityType.depositReceived:
        return Colors.green;
      case ActivityType.paymentRefunded:
      case ActivityType.depositRefunded:
        return Colors.orange;
      case ActivityType.paymentOverdue:
        return Colors.red;
      case ActivityType.paymentReminder:
        return Colors.amber;

      // Maintenance Activities
      case ActivityType.maintenanceRequest:
        return Colors.orange;
      case ActivityType.maintenanceScheduled:
        return Colors.blue;
      case ActivityType.maintenanceInProgress:
        return Colors.amber;
      case ActivityType.maintenanceCompleted:
        return Colors.green;
      case ActivityType.maintenanceCancelled:
        return Colors.red;
      case ActivityType.emergencyRepair:
        return Colors.red;

      // Communication Activities
      case ActivityType.messageReceived:
      case ActivityType.messageSent:
      case ActivityType.notificationSent:
      case ActivityType.emailSent:
      case ActivityType.smsReceived:
        return Colors.blue;

      // Document Activities
      case ActivityType.documentUploaded:
      case ActivityType.documentShared:
      case ActivityType.contractSigned:
        return Colors.green;
      case ActivityType.documentDeleted:
        return Colors.red;

      // Inspection Activities
      case ActivityType.inspectionScheduled:
        return Colors.blue;
      case ActivityType.inspectionCompleted:
        return Colors.green;
      case ActivityType.inspectionFailed:
        return Colors.red;

      // Security Activities
      case ActivityType.keyIssued:
      case ActivityType.accessGranted:
        return Colors.green;
      case ActivityType.keyReturned:
      case ActivityType.accessRevoked:
        return Colors.orange;

      // Billing Activities
      case ActivityType.billGenerated:
      case ActivityType.billSent:
        return Colors.blue;
      case ActivityType.billPaid:
        return Colors.green;
      case ActivityType.billOverdue:
        return Colors.red;

      // System Activities
      case ActivityType.dataExported:
      case ActivityType.backupCreated:
      case ActivityType.systemUpdate:
        return Colors.grey;

      case ActivityType.other:
        return Colors.grey;
    }
  }

  /// Get display name for activity type
  static String getDisplayName(ActivityType type) {
    switch (type) {
      // Authentication & User Activities
      case ActivityType.login:
        return 'Login';
      case ActivityType.logout:
        return 'Logout';

      // Property Activities
      case ActivityType.propertyCreated:
        return 'Property Created';
      case ActivityType.propertyUpdated:
        return 'Property Updated';
      case ActivityType.propertyDeleted:
        return 'Property Deleted';

      // Room Activities
      case ActivityType.roomCreated:
        return 'Room Created';
      case ActivityType.roomUpdated:
        return 'Room Updated';
      case ActivityType.roomDeleted:
        return 'Room Deleted';
      case ActivityType.roomStatusChanged:
        return 'Room Status Changed';
      case ActivityType.roomAssignment:
        return 'Room Assigned';
      case ActivityType.roomVacated:
        return 'Room Vacated';
      case ActivityType.roomInspection:
        return 'Room Inspection';
      case ActivityType.roomCleaning:
        return 'Room Cleaning';
      case ActivityType.roomRepair:
        return 'Room Repair';

      // Tenant Activities
      case ActivityType.tenantCreated:
        return 'Tenant Created';
      case ActivityType.tenantUpdated:
        return 'Tenant Updated';
      case ActivityType.tenantDeleted:
        return 'Tenant Deleted';
      case ActivityType.tenantActivated:
        return 'Tenant Activated';
      case ActivityType.tenantDeactivated:
        return 'Tenant Deactivated';
      case ActivityType.tenantMovedOut:
        return 'Tenant Moved Out';
      case ActivityType.tenantReactivated:
        return 'Tenant Reactivated';
      case ActivityType.tenantProfileUpdated:
        return 'Profile Updated';
      case ActivityType.tenantContactUpdated:
        return 'Contact Updated';

      // Lease Activities
      case ActivityType.leaseCreated:
        return 'Lease Created';
      case ActivityType.leaseUpdated:
        return 'Lease Updated';
      case ActivityType.leaseRenewed:
        return 'Lease Renewed';
      case ActivityType.leaseTerminated:
        return 'Lease Terminated';
      case ActivityType.leaseExpired:
        return 'Lease Expired';
      case ActivityType.vacateNotice:
        return 'Vacate Notice';

      // Payment Activities
      case ActivityType.paymentReceived:
        return 'Payment Received';
      case ActivityType.paymentRefunded:
        return 'Payment Refunded';
      case ActivityType.paymentOverdue:
        return 'Payment Overdue';
      case ActivityType.paymentReminder:
        return 'Payment Reminder';
      case ActivityType.depositReceived:
        return 'Deposit Received';
      case ActivityType.depositRefunded:
        return 'Deposit Refunded';

      // Maintenance Activities
      case ActivityType.maintenanceRequest:
        return 'Maintenance Request';
      case ActivityType.maintenanceScheduled:
        return 'Maintenance Scheduled';
      case ActivityType.maintenanceInProgress:
        return 'Maintenance In Progress';
      case ActivityType.maintenanceCompleted:
        return 'Maintenance Completed';
      case ActivityType.maintenanceCancelled:
        return 'Maintenance Cancelled';
      case ActivityType.emergencyRepair:
        return 'Emergency Repair';

      // Communication Activities
      case ActivityType.messageReceived:
        return 'Message Received';
      case ActivityType.messageSent:
        return 'Message Sent';
      case ActivityType.notificationSent:
        return 'Notification Sent';
      case ActivityType.emailSent:
        return 'Email Sent';
      case ActivityType.smsReceived:
        return 'SMS Received';

      // Document Activities
      case ActivityType.documentUploaded:
        return 'Document Uploaded';
      case ActivityType.documentDeleted:
        return 'Document Deleted';
      case ActivityType.documentShared:
        return 'Document Shared';
      case ActivityType.contractSigned:
        return 'Contract Signed';

      // Inspection Activities
      case ActivityType.inspectionScheduled:
        return 'Inspection Scheduled';
      case ActivityType.inspectionCompleted:
        return 'Inspection Completed';
      case ActivityType.inspectionFailed:
        return 'Inspection Failed';

      // Security Activities
      case ActivityType.keyIssued:
        return 'Key Issued';
      case ActivityType.keyReturned:
        return 'Key Returned';
      case ActivityType.accessGranted:
        return 'Access Granted';
      case ActivityType.accessRevoked:
        return 'Access Revoked';

      // Billing Activities
      case ActivityType.billGenerated:
        return 'Bill Generated';
      case ActivityType.billSent:
        return 'Bill Sent';
      case ActivityType.billPaid:
        return 'Bill Paid';
      case ActivityType.billOverdue:
        return 'Bill Overdue';

      // System Activities
      case ActivityType.dataExported:
        return 'Data Exported';
      case ActivityType.backupCreated:
        return 'Backup Created';
      case ActivityType.systemUpdate:
        return 'System Update';

      case ActivityType.other:
        return 'Other';
    }
  }
}
