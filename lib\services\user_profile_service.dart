import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/user/user_profile_model.dart';
import '../utils/logger.dart';

class UserProfileService {
  final SupabaseClient client;

  UserProfileService(this.client);

  // Get a user profile by ID
  Future<UserProfile?> getUserProfile(String userId) async {
    try {
      // Try to get from profiles table
      try {
        final response = await client
            .from('profiles')
            .select()
            .eq('id', userId)
            .maybeSingle();
            
        if (response != null) {
          return UserProfile.fromJson(response);
        }
      } catch (e) {
        AppLogger.error('Error getting user profile by ID: $e');
      }
      
      // If we couldn't get the profile, return null
      return null;
    } catch (e) {
      AppLogger.error('Unexpected error in getUserProfile: $e');
      return null;
    }
  }

  // Get the current user's profile
  Future<UserProfile?> getCurrentUserProfile() async {
    try {
      final user = client.auth.currentUser;
      if (user == null) {
        return null;
      }

      try {
        // First try to get from profiles table
        final response =
            await client.from('profiles').select().eq('id', user.id).single();

        // Profile exists in profiles table, which now contains all necessary information

        // Return profile from JSON
        return UserProfile.fromJson(response);
      } catch (e) {
        AppLogger.error('Error getting user profile: $e');

        // Specific handling for PostgrestException with code PGRST116 (no rows returned)
        if (e.toString().contains('PGRST116') ||
            e.toString().contains('contains 0 rows')) {
          AppLogger.info(
            'Profile not found (PGRST116), creating new profile for user ${user.id}',
          );

          // Create a new profile from user metadata
          final newProfile = UserProfile.fromUserMetadata(
            user.id,
            user.userMetadata,
            user.email,
          );

          // Insert the new profile into profiles table
          try {
            await client.from('profiles').insert(newProfile.toJson());
            AppLogger.info('Created new profile for user ${user.id}');

            // Return the newly created profile
            return newProfile;
          } catch (insertError) {
            AppLogger.error('Error creating profile: $insertError');
            // Still return the profile even if we couldn't save it to the database
            return newProfile;
          }
        }

        // For other errors, try to return a profile from user metadata
        return UserProfile.fromUserMetadata(
          user.id,
          user.userMetadata,
          user.email,
        );
      }
    } catch (e) {
      AppLogger.error('Unexpected error in getCurrentUserProfile: $e');
      return null;
    }
  }

  // Ensure profile exists for current user
  Future<bool> ensureProfileExists() async {
    try {
      final user = client.auth.currentUser;
      if (user == null) {
        return false;
      }

      // Check if profile exists in profiles table
      final profileExists = await _checkProfileExists(user.id);

      if (!profileExists) {
        // Create a new profile with all necessary information
        final newProfile = UserProfile.fromUserMetadata(
          user.id,
          user.userMetadata,
          user.email,
        );

        // Insert into profiles table
        await client.from('profiles').insert(newProfile.toJson());
        return true;
      }

      return true;
    } catch (e) {
      AppLogger.error('Error ensuring profile exists: $e');
      return false;
    }
  }

  // Check if profile exists in profiles table
  Future<bool> _checkProfileExists(String userId) async {
    try {
      final response =
          await client
              .from('profiles')
              .select('id')
              .eq('id', userId)
              .maybeSingle();

      return response != null;
    } catch (e) {
      AppLogger.error('Error checking if profile exists: $e');
      return false;
    }
  }

  // Note: user_profiles table is kept for backward compatibility but profile updates
  // are now handled entirely through the profiles table which contains all necessary fields

  // Update user profile
  Future<UserProfile?> updateUserProfile(UserProfile updatedProfile) async {
    try {
      // Add timeout to prevent hanging
      return await _updateUserProfileWithTimeout(updatedProfile);
    } catch (e, stackTrace) {
      AppLogger.error('Error updating user profile: $e');
      AppLogger.error('Stack trace: $stackTrace');
      
      // Rethrow with more context to help debugging
      throw Exception('Failed to update profile: $e');
    }
  }
  
  // Helper method with timeout
  Future<UserProfile?> _updateUserProfileWithTimeout(UserProfile updatedProfile) async {
    return await _performProfileUpdate(updatedProfile).timeout(
      const Duration(seconds: 30),
      onTimeout: () {
        throw Exception('Profile update timed out. Please try again.');
      },
    );
  }

  // Perform the actual profile update
  Future<UserProfile?> _performProfileUpdate(UserProfile updatedProfile) async {
    final user = client.auth.currentUser;
    if (user == null) {
      AppLogger.error('No authenticated user found when updating profile');
      throw Exception('No authenticated user found');
    }

    AppLogger.info('Updating profile for user: ${user.id}');

    // Update user metadata in auth.users
    try {
      await client.auth.updateUser(
        UserAttributes(data: updatedProfile.toUserMetadata()),
      );
      AppLogger.info('Successfully updated auth user metadata');
    } catch (e) {
      AppLogger.error('Failed to update auth user metadata: $e');
      // Continue with profile update even if auth metadata fails
    }

    // Check if profile exists in profiles table
    final profileExists = await _checkProfileExists(user.id);

    AppLogger.info('Profile exists: $profileExists');

    // Update profiles table (now contains all profile information)
    try {
      if (profileExists) {
        // Update existing profile
        await client
            .from('profiles')
            .update(updatedProfile.toJson())
            .eq('id', user.id);
        AppLogger.info('Successfully updated existing profile');
      } else {
        // Insert new profile
        await client.from('profiles').insert(updatedProfile.toJson());
        AppLogger.info('Successfully inserted new profile');
      }
    } catch (e) {
      AppLogger.error('Failed to update/insert profile: $e');
      throw Exception('Failed to update profile: $e');
    }

    // Return the updated profile
    final result = await getCurrentUserProfile();
    AppLogger.info('Profile update completed successfully');
    return result;
  }

  // Update email with OTP verification
  Future<bool> updateEmail(String newEmail) async {
    try {
      final user = client.auth.currentUser;
      if (user == null) {
        throw Exception('No authenticated user found');
      }

      // Request email change with OTP verification
      await client.auth.updateUser(UserAttributes(email: newEmail));

      return true;
    } catch (e) {
      AppLogger.error('Error updating email: $e');
      return false;
    }
  }

  // Upload profile image
  Future<String?> uploadProfileImage(
    Uint8List imageBytes,
    String fileName,
  ) async {
    try {
      final user = client.auth.currentUser;
      if (user == null) {
        throw Exception('No authenticated user found');
      }

      final String path = 'profile_images/${user.id}/$fileName';

      await client.storage
          .from('user_uploads')
          .uploadBinary(
            path,
            imageBytes,
            fileOptions: const FileOptions(cacheControl: '3600', upsert: true),
          );

      // Get public URL
      final imageUrl = client.storage.from('user_uploads').getPublicUrl(path);

      // Update user metadata with new image URL
      final currentProfile = await getCurrentUserProfile();
      if (currentProfile != null) {
        final updatedProfile = currentProfile.copyWith(
          profileImageUrl: imageUrl,
        );
        await updateUserProfile(updatedProfile);
      }

      return imageUrl;
    } catch (e) {
      AppLogger.error('Error uploading profile image: $e');
      return null;
    }
  }
}

