import 'package:flutter/material.dart';
import '../../utils/logger.dart';

enum ExpenseFrequency {
  oneTime,
  weekly,
  monthly,
  quarterly,
  annually,
  custom,
}

class ExpenseModel {
  final String? id;
  final int? expenseNumber;
  final String title;
  final String description;
  final double amount;
  final DateTime date;
  final String? categoryId;
  final String? categoryName; // For display purposes
  final Color? categoryColor;
  final String? propertyId;
  final String? propertyName; // For display purposes
  final String? roomId;
  final String? roomName; // For display purposes
  final String? vendorId;
  final String? vendorName; // For display purposes
  final String? receiptNumber; // Receipt or invoice number
  final ExpenseFrequency? frequency;
  final DateTime? nextDueDate;
  final DateTime? endDate;
  final int? occurrences;
  final int? occurrencesCompleted;
  final bool isRecurring;
  final String? receiptUrl;
  final String? userId;
  final DateTime createdAt;
  final DateTime? updatedAt;

  ExpenseModel({
    this.id,
    this.expenseNumber,
    required this.title,
    required this.description,
    required this.amount,
    required this.date,
    this.categoryId,
    this.categoryName,
    this.categoryColor,
    this.propertyId,
    this.propertyName,
    this.roomId,
    this.roomName,
    this.vendorId,
    this.vendorName,
    this.receiptNumber,
    this.frequency,
    this.nextDueDate,
    this.endDate,
    this.occurrences,
    this.occurrencesCompleted,
    this.isRecurring = false,
    this.receiptUrl,
    this.userId,
    required this.createdAt,
    this.updatedAt,
  });

  String get formattedExpenseNumber {
    if (expenseNumber == null) return 'N/A';
    return 'EXP-#$expenseNumber';
  }

  factory ExpenseModel.fromJson(Map<String, dynamic> json) {
    Color? categoryColor;
    try {
      if (json['category_color'] != null && json['category_color'].toString().isNotEmpty) {
        categoryColor = Color(int.parse('0xFF${json['category_color']}'));
      }
    } catch (e) {
      AppLogger.warning('Failed to parse category color: ${e.toString()}');
    }
    
    return ExpenseModel(
      id: json['id'],
      expenseNumber: json['expense_number'],
      title: json['title'],
      description: json['description'] ?? '',
      amount: json['amount'] != null ? double.parse(json['amount'].toString()) : 0.0,
      date: json['date'] != null ? DateTime.parse(json['date']) : DateTime.now(),
      categoryId: json['category_id'],
      categoryName: json['category_name'],
      categoryColor: categoryColor,
      propertyId: json['property_id'],
      propertyName: json['property_name'],
      roomId: json['room_id'],
      roomName: json['room_name'],
      vendorId: json['vendor_id'],
      vendorName: json['vendor_name'] ?? json['vendor_name_resolved'],
      receiptNumber: json['receipt_number'],
      frequency: json['frequency'] != null
          ? ExpenseFrequency.values.firstWhere(
              (e) => e.toString().split('.').last == json['frequency'],
              orElse: () => ExpenseFrequency.oneTime,
            )
          : null,
      nextDueDate: json['next_due_date'] != null
          ? DateTime.parse(json['next_due_date'])
          : null,
      endDate:
          json['end_date'] != null ? DateTime.parse(json['end_date']) : null,
      occurrences: json['occurrences'],
      occurrencesCompleted: json['occurrences_completed'],
      isRecurring: json['is_recurring'] ?? false,
      receiptUrl: json['receipt_url'],
      userId: json['user_id'],
      createdAt: json['created_at'] != null 
          ? DateTime.parse(json['created_at']) 
          : DateTime.now(),
      updatedAt: json['updated_at'] != null
          ? DateTime.parse(json['updated_at'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'expense_number': expenseNumber,
      'title': title,
      'description': description,
      'amount': amount,
      'date': date.toIso8601String(),
      'category_id': categoryId,
      'property_id': propertyId,
      'room_id': roomId,
      'vendor_id': vendorId,
      'vendor_name': vendorName,
      'receipt_number': receiptNumber,
      'frequency': frequency?.toString().split('.').last,
      'next_due_date': nextDueDate?.toIso8601String(),
      'end_date': endDate?.toIso8601String(),
      'occurrences': occurrences,
      'occurrences_completed': occurrencesCompleted,
      'is_recurring': isRecurring,
      'receipt_url': receiptUrl,
      'user_id': userId,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  ExpenseModel copyWith({
    String? id,
    int? expenseNumber,
    String? title,
    String? description,
    double? amount,
    DateTime? date,
    String? categoryId,
    String? categoryName,
    Color? categoryColor,
    String? propertyId,
    String? propertyName,
    String? roomId,
    String? roomName,
    String? vendorId,
    String? vendorName,
    String? receiptNumber,
    ExpenseFrequency? frequency,
    DateTime? nextDueDate,
    DateTime? endDate,
    int? occurrences,
    int? occurrencesCompleted,
    bool? isRecurring,
    String? receiptUrl,
    String? userId,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return ExpenseModel(
      id: id ?? this.id,
      expenseNumber: expenseNumber ?? this.expenseNumber,
      title: title ?? this.title,
      description: description ?? this.description,
      amount: amount ?? this.amount,
      date: date ?? this.date,
      categoryId: categoryId ?? this.categoryId,
      categoryName: categoryName ?? this.categoryName,
      categoryColor: categoryColor ?? this.categoryColor,
      propertyId: propertyId ?? this.propertyId,
      propertyName: propertyName ?? this.propertyName,
      roomId: roomId ?? this.roomId,
      roomName: roomName ?? this.roomName,
      vendorId: vendorId ?? this.vendorId,
      vendorName: vendorName ?? this.vendorName,
      receiptNumber: receiptNumber ?? this.receiptNumber,
      frequency: frequency ?? this.frequency,
      nextDueDate: nextDueDate ?? this.nextDueDate,
      endDate: endDate ?? this.endDate,
      occurrences: occurrences ?? this.occurrences,
      occurrencesCompleted: occurrencesCompleted ?? this.occurrencesCompleted,
      isRecurring: isRecurring ?? this.isRecurring,
      receiptUrl: receiptUrl ?? this.receiptUrl,
      userId: userId ?? this.userId,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
} 