import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import '../../models/bill/bill.dart';
import '../../services/service_locator.dart';
import '../payments/record_payment_page.dart';

class BillDetailPage extends StatelessWidget {
  final String billId;

  const BillDetailPage({super.key, required this.billId});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Bill Details')),
      body: FutureBuilder<Bill>(
        future: _loadBill(),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          }

          if (snapshot.hasError) {
            return Center(child: Text('Error: ${snapshot.error}'));
          }

          if (!snapshot.hasData) {
            return const Center(child: Text('Bill not found'));
          }

          final bill = snapshot.data!;
          final settingsService = serviceLocator.settingsService;

          return SingleChildScrollView(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Bill number with copy button
                if (bill.billNumber != null) ...[
                  Card(
                    elevation: 1,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                      side: BorderSide(color: Colors.grey.shade300),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(12.0),
                      child: Row(
                        children: [
                          Icon(Icons.receipt, color: Colors.blue.shade700, size: 20),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              'Bill #${bill.billNumber}',
                              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                          IconButton(
                            icon: const Icon(Icons.copy, size: 20),
                            tooltip: 'Copy bill number',
                            onPressed: () {
                              Clipboard.setData(ClipboardData(text: bill.billNumber!));
                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(
                                  content: const Text('Bill number copied to clipboard'),
                                  behavior: SnackBarBehavior.floating,
                                  width: 280,
                                  duration: const Duration(seconds: 2),
                                ),
                              );
                            },
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),
                ],
                
                // Bill title and status
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        bill.title,
                        style: Theme.of(context).textTheme.headlineSmall,
                      ),
                    ),
                    _buildStatusChip(bill),
                  ],
                ),

                const SizedBox(height: 16),

                // Description
                Text(
                  'Description',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(bill.description),
                
                const SizedBox(height: 16),
                
                // Include in rent indicator
                if (bill.includeInRent) ...[
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.blue.shade100,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.blue.shade200),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(Icons.check_circle_outline, color: Colors.blue.shade800),
                        const SizedBox(width: 8),
                        Text(
                          'Included in Rent',
                          style: TextStyle(color: Colors.blue.shade800),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 16),
                ],
                
                // Utility details (if applicable)
                if (bill.type == BillType.utility && bill.utilityType != null) ...[
                  _buildSectionCard(
                    title: 'Utility Details',
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _buildDetailRow('Utility Type', bill.utilityType!.name.toUpperCase()),
                        
                        if (bill.previousMeterReading != null && bill.currentMeterReading != null) ...[
                          _buildDetailRow('Previous Meter Reading', bill.previousMeterReading!.toString()),
                          _buildDetailRow('Current Meter Reading', bill.currentMeterReading!.toString()),
                        ],
                        
                        if (bill.unitConsumed != null) 
                          _buildDetailRow('Units Consumed', bill.unitConsumed!.toString()),
                          
                        if (bill.ratePerUnit != null)
                          _buildDetailRow('Rate per Unit', settingsService.formatCurrency(bill.ratePerUnit!)),
                      ],
                    ),
                  ),
                  const SizedBox(height: 16),
                ],

                // Amount and payment status
                _buildSectionCard(
                  title: 'Amount',
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            settingsService.formatCurrency(bill.amount),
                            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          if (bill.isPartiallyPaid())
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.end,
                              children: [
                                Text(
                                  'Paid',
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: Colors.grey.shade700,
                                  ),
                                ),
                                Text(
                                  settingsService.formatCurrency(bill.paidAmount!),
                                  style: const TextStyle(
                                    color: Colors.green,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                        ],
                      ),

                      if (bill.isPartiallyPaid()) ...[
                        const SizedBox(height: 8),
                        const Divider(),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              'Remaining',
                              style: TextStyle(
                                fontWeight: FontWeight.w500,
                                color: Colors.grey.shade700,
                              ),
                            ),
                            Text(
                              settingsService.formatCurrency(bill.getRemainingAmount()),
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Colors.red,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ],
                  ),
                ),

                const SizedBox(height: 16),
                
                // Bill Components Section
                if (bill.billComponents != null && bill.billComponents!.isNotEmpty) ...[
                  Text(
                    'Bill Components',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  
                  Card(
                    margin: const EdgeInsets.only(bottom: 16),
                    elevation: 1,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(12),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          ...bill.billComponents!.map((component) {
                            final title = component['title'] ?? 'Unnamed Component';
                            final amount = component['amount'] ?? 0.0;
                            final description = component['description'] ?? '';
                            
                            return Padding(
                              padding: const EdgeInsets.symmetric(vertical: 4),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                children: [
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          title,
                                          style: const TextStyle(fontWeight: FontWeight.w500),
                                        ),
                                        if (description.isNotEmpty)
                                          Text(
                                            description,
                                            style: TextStyle(
                                              fontSize: 12,
                                              color: Colors.grey[600],
                                            ),
                                          ),
                                      ],
                                    ),
                                  ),
                                  Text(
                                    settingsService.formatCurrency(amount),
                                    style: TextStyle(
                                      fontWeight: FontWeight.w500,
                                      color: amount > 0 ? Colors.black : Colors.grey,
                                    ),
                                  ),
                                ],
                              ),
                            );
                          }),
                          
                          const Divider(height: 24),
                          
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              const Text(
                                'Total',
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              Text(
                                settingsService.formatCurrency(bill.amount),
                                style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ],

                // Bill details
                _buildSectionCard(
                  title: 'Bill Details',
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildDetailRow('Due Date', DateFormat('MMMM d, yyyy').format(bill.dueDate)),
                      _buildDetailRow('Bill Type', bill.type.name.toUpperCase()),
                      _buildDetailRow('Recurrence', bill.recurrence.name.toUpperCase()),
                      
                      if (bill.paidAt != null)
                        _buildDetailRow('Payment Date', DateFormat('MMMM d, yyyy').format(bill.paidAt!)),
                    ],
                  ),
                ),

                const SizedBox(height: 16),

                // Tenant information
                _buildSectionCard(
                  title: 'Related Information',
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      if (bill.tenantId != null)
                        FutureBuilder(
                          future: _loadTenantName(bill.tenantId!),
                          builder: (context, snapshot) {
                            if (snapshot.hasData) {
                              return _buildDetailRow('Tenant', snapshot.data!);
                            }
                            return _buildDetailRow('Tenant', 'Loading...');
                          },
                        ),

                      if (bill.roomId != null)
                        FutureBuilder(
                          future: _loadRoomName(bill.roomId!),
                          builder: (context, snapshot) {
                            if (snapshot.hasData) {
                              return _buildDetailRow('Room', snapshot.data!);
                            }
                            return _buildDetailRow('Room', 'Loading...');
                          },
                        ),

                      if (bill.propertyId != null)
                        FutureBuilder(
                          future: _loadPropertyName(bill.propertyId!),
                          builder: (context, snapshot) {
                            if (snapshot.hasData) {
                              return _buildDetailRow('Property', snapshot.data!);
                            }
                            return _buildDetailRow('Property', 'Loading...');
                          },
                        ),
                    ],
                  ),
                ),

                // Notes
                if (bill.notes != null && bill.notes!.isNotEmpty) ...[
                  const SizedBox(height: 16),
                  _buildSectionCard(
                    title: 'Notes',
                    child: Text(bill.notes!),
                  ),
                ],

                const SizedBox(height: 24),

                // Action button
                if (bill.status != BillStatus.paid && !bill.isFullyPaid())
                  ElevatedButton.icon(
                    onPressed: () => _recordPayment(context, bill),
                    icon: const Icon(Icons.payment),
                    label: const Text('Record Payment'),
                    style: ElevatedButton.styleFrom(
                      minimumSize: const Size.fromHeight(50),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                  ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildSectionCard({required String title, required Widget child}) {
    return Card(
      elevation: 1,
      margin: const EdgeInsets.only(bottom: 8),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(color: Colors.grey.shade200),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            child,
          ],
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: TextStyle(
                fontWeight: FontWeight.w500,
                color: Colors.grey.shade700,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusChip(Bill bill) {
    Color color;
    String label;

    if (bill.status == BillStatus.paid) {
      color = Colors.green;
      label = 'PAID';
    } else if (bill.isOverdue()) {
      color = Colors.red;
      label = 'OVERDUE';
    } else {
      color = Colors.orange;
      label = 'PENDING';
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: color.withAlpha(51), // 0.2 opacity is roughly equivalent to alpha 51 (255 * 0.2)
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: color),
      ),
      child: Text(
        label,
        style: TextStyle(
          color: color,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Future<Bill> _loadBill() async {
    return await serviceLocator.billService.getBill(billId);
  }

  Future<String> _loadTenantName(String tenantId) async {
    try {
      final tenant = await serviceLocator.tenantService.getTenantById(tenantId);
      return '${tenant?.firstName ?? 'Unknown'} ${tenant?.lastName ?? ''}';
    } catch (e) {
      return 'Unknown';
    }
  }

  Future<String> _loadRoomName(String roomId) async {
    try {
      final room = await serviceLocator.roomService.getRoomById(roomId);
      return room?.name ?? 'Unknown Room';
    } catch (e) {
      return 'Unknown';
    }
  }

  Future<String> _loadPropertyName(String propertyId) async {
    try {
      final property =
          await serviceLocator.propertyService.getPropertyById(propertyId);
      return property?.name ?? 'Unknown Property';
    } catch (e) {
      return 'Unknown';
    }
  }

  void _recordPayment(BuildContext context, Bill bill) async {
    // Check if bill is already fully paid
    if (bill.isFullyPaid()) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('This bill is already fully paid'),
          behavior: SnackBarBehavior.floating,
        ),
      );
      return;
    }

    final result = await Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => RecordPaymentPage(bill: bill),
      ),
    );

    if (result == true) {
      // Refresh the page after payment
      // Note: Since we're using FutureBuilder, it will automatically rebuild
    }
  }
}
