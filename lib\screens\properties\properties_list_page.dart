import 'package:flutter/material.dart';
import '../../models/property/property_model.dart';
import '../../services/service_locator.dart';
import '../../models/room/room_model.dart';
import '../../widgets/navigation/app_drawer.dart';
import 'property_detail_page.dart';
import 'property_form_page.dart';

class PropertiesListPage extends StatefulWidget {
  const PropertiesListPage({super.key});

  @override
  State<PropertiesListPage> createState() => _PropertiesListPageState();
}

class _PropertiesListPageState extends State<PropertiesListPage> {
  List<Property> _properties = [];
  bool _isLoading = true;

  // Search and filtering
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  String _sortBy = 'name'; // Default sort
  String? _filterByCity;

  // Get list of unique cities from properties
  List<String> get _availableCities =>
      _properties.map((p) => p.city).toSet().toList()..sort();

  @override
  void initState() {
    super.initState();
    _loadProperties();

    // Listen to search query changes
    _searchController.addListener(() {
      setState(() {
        _searchQuery = _searchController.text;
      });
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  // Get filtered, sorted, and searched properties
  List<Property> get _filteredProperties {
    // Start with all properties
    List<Property> filtered = List.from(_properties);

    // Apply search
    if (_searchQuery.isNotEmpty) {
      filtered =
          filtered.where((property) {
            return property.name.toLowerCase().contains(
                  _searchQuery.toLowerCase(),
                ) ||
                property.address.toLowerCase().contains(
                  _searchQuery.toLowerCase(),
                ) ||
                property.city.toLowerCase().contains(
                  _searchQuery.toLowerCase(),
                );
          }).toList();
    }

    // Apply city filter
    if (_filterByCity != null) {
      filtered =
          filtered.where((property) => property.city == _filterByCity).toList();
    }

    // Apply sort
    switch (_sortBy) {
      case 'name':
        filtered.sort((a, b) => a.name.compareTo(b.name));
        break;
      case 'city':
        filtered.sort((a, b) => a.city.compareTo(b.city));
        break;
      case 'created':
        filtered.sort(
          (a, b) => b.createdAt.compareTo(a.createdAt),
        ); // Newest first
        break;
    }

    return filtered;
  }

  Future<void> _loadProperties() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Get properties from service
      final properties =
          await serviceLocator.propertyService.getAllProperties();

      if (mounted) {
        setState(() {
          _properties = properties;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _properties = [];
          _isLoading = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.error_outline, color: Colors.white),
                const SizedBox(width: 8),
                Expanded(
                  child: Text('Error loading properties: ${e.toString()}'),
                ),
              ],
            ),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
            duration: const Duration(seconds: 4),
          ),
        );
      }
    }
  }

  Widget _buildPlaceholderImage() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.grey[200],
        borderRadius: BorderRadius.circular(12),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: Image.asset(
          'assets/images/Apartment.png',
          height: 105,
          width: 100,
          fit: BoxFit.cover,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Properties'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            tooltip: 'Refresh',
            onPressed: _loadProperties,
          ),
        ],
      ),
      drawer: const AppDrawer(),
      body: Column(
        children: [
          // Search and filter bar
          _buildSearchFilterBar(),

          // Properties list
          Expanded(
            child:
                _isLoading
                    ? const Center(child: CircularProgressIndicator())
                    : _buildPropertiesList(),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        tooltip: 'Add Property',
        onPressed: _addProperty,
        icon: const Icon(Icons.add),
        label: const Text('ADD PROPERTY'),
      ),
    );
  }

  Widget _buildSearchFilterBar() {
    return Container(
      padding: const EdgeInsets.fromLTRB(16, 8, 16, 0),
      child: Column(
        children: [
          // Search bar
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'Search properties...',
              prefixIcon: const Icon(Icons.search),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: Colors.grey.shade300),
              ),
              contentPadding: const EdgeInsets.symmetric(vertical: 0),
              suffixIcon:
                  _searchQuery.isNotEmpty
                      ? IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () {
                          _searchController.clear();
                        },
                      )
                      : null,
            ),
          ),

          // Filter and sort options
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              children: [
                // City filter
                if (_availableCities.isNotEmpty)
                  Expanded(
                    child: DropdownButtonHideUnderline(
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey.shade300),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: DropdownButton<String?>(
                          hint: const Text('Filter by City'),
                          value: _filterByCity,
                          isExpanded: true,
                          icon: const Icon(Icons.filter_list, size: 18),
                          items: [
                            const DropdownMenuItem<String?>(
                              value: null,
                              child: Text('All Cities'),
                            ),
                            ..._availableCities.map((city) {
                              return DropdownMenuItem<String?>(
                                value: city,
                                child: Text(city),
                              );
                            }),
                          ],
                          onChanged: (value) {
                            setState(() {
                              _filterByCity = value;
                            });
                          },
                        ),
                      ),
                    ),
                  ),

                const SizedBox(width: 8),

                // Sort by options
                Expanded(
                  child: DropdownButtonHideUnderline(
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey.shade300),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: DropdownButton<String>(
                        hint: const Text('Sort by'),
                        value: _sortBy,
                        isExpanded: true,
                        icon: const Icon(Icons.sort, size: 18),
                        items: const [
                          DropdownMenuItem(value: 'name', child: Text('Name')),
                          DropdownMenuItem(value: 'city', child: Text('City')),
                          DropdownMenuItem(
                            value: 'created',
                            child: Text('Newest'),
                          ),
                        ],
                        onChanged: (value) {
                          setState(() {
                            _sortBy = value!;
                          });
                        },
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPropertiesList() {
    final filteredProperties = _filteredProperties;

    if (_properties.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: Colors.grey.withAlpha(26),
                shape: BoxShape.circle,
              ),
              child: const Icon(Icons.home_work, size: 80, color: Colors.grey),
            ),
            const SizedBox(height: 24),
            Text(
              'No properties yet',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 12),
            const Text(
              'Tap the + button to add your first property',
              style: TextStyle(color: Colors.grey),
            ),
          ],
        ),
      );
    }

    if (filteredProperties.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.search_off, size: 64, color: Colors.grey.shade400),
            const SizedBox(height: 16),
            Text(
              'No matching properties',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 8),
            Text(
              'Try adjusting your search or filters',
              style: TextStyle(color: Colors.grey.shade600),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadProperties,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: filteredProperties.length,
        itemBuilder: (context, index) {
          final property = filteredProperties[index];
          return _buildPropertyCard(property);
        },
      ),
    );
  }

  Widget _buildPropertyCard(Property property) {
    return FutureBuilder<List<Room>>(
      future: serviceLocator.roomService.getRoomsByPropertyId(property.id),
      builder: (context, snapshot) {
        // Room counts
        final totalRooms = snapshot.hasData ? snapshot.data!.length : 0;
        final vacantRooms =
            snapshot.hasData
                ? snapshot.data!
                    .where(
                      (r) => r.occupancyStatus == RoomOccupancyStatus.vacant,
                    )
                    .length
                : 0;
        final occupiedRooms = totalRooms - vacantRooms;

        return Card(
          margin: const EdgeInsets.only(bottom: 16),
          elevation: 2,
          clipBehavior: Clip.antiAlias,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          child: InkWell(
            onTap: () => _viewPropertyDetails(property),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Left side: Property image (20-30% width)
                SizedBox(
                  width: 100, // ~30% of a typical mobile screen
                  height: 105, // Fixed height
                  child: _buildPlaceholderImage(),
                ),

                // Right side: Property details (70-80% width)
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.fromLTRB(12, 12, 12, 8),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        // Property name
                        Text(
                          property.name,
                          style: const TextStyle(
                            fontSize: 17,
                            fontWeight: FontWeight.bold,
                            color: Colors.black87,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),

                        const SizedBox(height: 4),

                        // Address
                        Row(
                          children: [
                            const Icon(
                              Icons.location_on,
                              size: 12,
                              color: Colors.black54,
                            ),
                            const SizedBox(width: 2),
                            Expanded(
                              child: Text(
                                '${property.address}, ${property.city}',
                                style: const TextStyle(
                                  color: Colors.black87,
                                  fontSize: 11,
                                  fontWeight: FontWeight.w500,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ),

                        const SizedBox(height: 6),

                        // Room statistics row
                        Row(
                          children: [
                            // Total rooms
                            _buildRoomStatChip(
                              label: 'Total',
                              count: totalRooms,
                              color: Theme.of(context).primaryColor,
                              icon: Icons.home,
                            ),
                            const SizedBox(width: 6),

                            // Vacant rooms
                            _buildRoomStatChip(
                              label: 'Vacant',
                              count: vacantRooms,
                              color: Colors.green,
                              icon: Icons.check_circle,
                            ),
                            const SizedBox(width: 6),

                            // Occupied rooms
                            _buildRoomStatChip(
                              label: 'Occupied',
                              count: occupiedRooms,
                              color: Colors.blue,
                              icon: Icons.person,
                            ),
                          ],
                        ),

                        const SizedBox(height: 4),

                        // Action buttons row
                        Row(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            _buildIconButton(
                              icon: Icons.edit,
                              color: Colors.blue,
                              onPressed: () => _editProperty(property),
                            ),
                            _buildIconButton(
                              icon: Icons.visibility,
                              color: Colors.green,
                              onPressed: () => _viewPropertyDetails(property),
                            ),
                            _buildIconButton(
                              icon: Icons.delete,
                              color: Colors.red,
                              onPressed: () => _deleteProperty(property),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  // Helper to build a room stat chip
  Widget _buildRoomStatChip({
    required String label,
    required int count,
    required Color color,
    required IconData icon,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: color.withAlpha(30),
        borderRadius: BorderRadius.circular(4),
        border: Border.all(color: color.withAlpha(100), width: 1),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 10, color: color),
          const SizedBox(width: 3),
          Text(
            '$count $label',
            style: TextStyle(
              fontSize: 10,
              fontWeight: FontWeight.bold,
              color: color.withAlpha(230),
            ),
          ),
        ],
      ),
    );
  }

  // Compact icon button
  Widget _buildIconButton({
    required IconData icon,
    required VoidCallback onPressed,
    required Color color,
  }) {
    return InkWell(
      onTap: onPressed,
      borderRadius: BorderRadius.circular(4),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
        child: Icon(icon, size: 20, color: color),
      ),
    );
  }

  void _addProperty() async {
    final result = await Navigator.push<bool>(
      context,
      MaterialPageRoute(builder: (context) => const PropertyFormPage()),
    );

    if (result == true) {
      _loadProperties();
    }
  }

  void _editProperty(Property property) async {
    final result = await Navigator.push<bool>(
      context,
      MaterialPageRoute(
        builder: (context) => PropertyFormPage(property: property),
      ),
    );

    if (result == true) {
      _loadProperties();
    }
  }

  void _viewPropertyDetails(Property property) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => PropertyDetailPage(propertyId: property.id),
      ),
    ).then((_) => _loadProperties());
  }

  void _deleteProperty(Property property) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Delete Property'),
            content: Text(
              'Are you sure you want to delete "${property.name}"?',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('CANCEL'),
              ),
              TextButton(
                onPressed: () async {
                  Navigator.pop(context);

                  // Capture the ScaffoldMessengerState before the async gap
                  final scaffoldMessenger = ScaffoldMessenger.of(context);

                  setState(() {
                    _isLoading = true;
                  });

                  try {
                    final success = await serviceLocator.propertyService
                        .deleteProperty(property.id);

                    if (mounted) {
                      if (success) {
                        _loadProperties();
                        scaffoldMessenger.showSnackBar(
                          SnackBar(
                            content: Row(
                              children: [
                                const Icon(
                                  Icons.check_circle,
                                  color: Colors.white,
                                ),
                                const SizedBox(width: 8),
                                Text(
                                  'Property "${property.name}" deleted successfully',
                                ),
                              ],
                            ),
                            backgroundColor: Colors.green,
                            behavior: SnackBarBehavior.floating,
                          ),
                        );
                      } else {
                        setState(() {
                          _isLoading = false;
                        });
                        scaffoldMessenger.showSnackBar(
                          const SnackBar(
                            backgroundColor: Colors.red,
                            behavior: SnackBarBehavior.floating,
                            content: Row(
                              children: [
                                Icon(Icons.error_outline, color: Colors.white),
                                SizedBox(width: 8),
                                Expanded(
                                  child: Text('Failed to delete property'),
                                ),
                              ],
                            ),
                          ),
                        );
                      }
                    }
                  } catch (e) {
                    if (mounted) {
                      setState(() {
                        _isLoading = false;
                      });
                      scaffoldMessenger.showSnackBar(
                        SnackBar(
                          backgroundColor: Colors.red,
                          behavior: SnackBarBehavior.floating,
                          content: Row(
                            children: [
                              const Icon(
                                Icons.error_outline,
                                color: Colors.white,
                              ),
                              const SizedBox(width: 8),
                              Expanded(child: Text('Error: ${e.toString()}')),
                            ],
                          ),
                        ),
                      );
                    }
                  }
                },
                style: TextButton.styleFrom(foregroundColor: Colors.red),
                child: const Text('DELETE'),
              ),
            ],
          ),
    );
  }
}
