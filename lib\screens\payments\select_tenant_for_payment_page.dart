import 'package:flutter/material.dart';
import '../../models/bill/bill.dart';
import '../../models/tenant/tenant.dart';
import '../../services/service_locator.dart';
import '../../utils/logger.dart';
import 'select_bills_for_payment_page.dart';

class SelectTenantForPaymentPage extends StatefulWidget {
  const SelectTenantForPaymentPage({super.key});

  @override
  State<SelectTenantForPaymentPage> createState() => _SelectTenantForPaymentPageState();
}

class _SelectTenantForPaymentPageState extends State<SelectTenantForPaymentPage> {
  bool _isLoading = true;
  bool _hasError = false;
  String _errorMessage = '';
  List<Tenant> _tenants = [];
  String _searchQuery = '';
  List<Tenant> _filteredTenants = [];
  
  // Map to store bill counts for each tenant
  Map<String, int> _tenantBillCounts = {};
  
  // Sorting options
  String _sortBy = 'name'; // Options: 'name', 'email', 'status', 'bills'
  bool _sortAscending = true;
  
  // Filter options
  TenantStatus? _statusFilter;

  @override
  void initState() {
    super.initState();
    _loadTenants();
  }

  void _showErrorSnackbar(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.red,
          action: SnackBarAction(
            label: 'Retry',
            textColor: Colors.white,
            onPressed: _loadTenants,
          ),
        ),
      );
    }
  }

  Future<void> _loadTenants() async {
    setState(() {
      _isLoading = true;
      _hasError = false;
      _errorMessage = '';
      _tenantBillCounts = {};
    });

    try {
      // First try to get all tenants
      final allTenants = await serviceLocator.tenantService.getAllTenants();
      
      // Then try to get tenants with outstanding bills
      List<Tenant> tenantsWithBills = [];
      try {
        tenantsWithBills = await serviceLocator.billTenantService.getTenantsWithOutstandingBills();
      } catch (e) {
        // If this fails, we'll just use all tenants
        AppLogger.warning('Error fetching tenants with bills, falling back to all tenants: $e');
      }
      
      // If we have tenants with bills, use them, otherwise use all tenants
      List<Tenant> tenants = tenantsWithBills.isNotEmpty ? tenantsWithBills : allTenants;
      
      // Load bill counts for each tenant
      await _loadTenantBillCounts(tenants);
      
      if (mounted) {
        setState(() {
          _tenants = tenants;
          _applyFiltersAndSort();
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
          _hasError = true;
          _errorMessage = 'Error loading tenants: $e';
        });
        _showErrorSnackbar('Failed to load tenants: $e');
      }
    }
  }
  
  Future<void> _loadTenantBillCounts(List<Tenant> tenants) async {
    // Create a list of futures to execute in parallel
    final futures = tenants.map((tenant) async {
      try {
        final bills = await serviceLocator.billTenantService.getBillsByTenantId(
          tenant.id,
          statusFilter: [BillStatus.pending],
        );
        return MapEntry(tenant.id, bills.length);
      } catch (e) {
        AppLogger.error('Error fetching bill count for tenant ${tenant.id}: $e');
        return MapEntry(tenant.id, 0);
      }
    }).toList();
    
    // Wait for all futures to complete
    final results = await Future.wait(futures);
    
    // Convert results to a map
    final billCounts = Map.fromEntries(results);
    
    if (mounted) {
      setState(() {
        _tenantBillCounts = billCounts;
      });
    }
  }

  void _applyFiltersAndSort() {
    // Apply filters
    List<Tenant> filtered = List.from(_tenants);
    
    // Apply status filter if set
    if (_statusFilter != null) {
      filtered = filtered.where((tenant) => tenant.status == _statusFilter).toList();
    }
    
    // Apply search filter
    if (_searchQuery.isNotEmpty) {
      final searchLower = _searchQuery.toLowerCase();
      filtered = filtered.where((tenant) {
        final name = '${tenant.firstName} ${tenant.lastName}'.toLowerCase();
        final email = tenant.email.toLowerCase();
        return name.contains(searchLower) || email.contains(searchLower);
      }).toList();
    }
    
    // Apply sorting
    filtered.sort((a, b) {
      int result;
      switch (_sortBy) {
        case 'name':
          result = '${a.firstName} ${a.lastName}'.compareTo('${b.firstName} ${b.lastName}');
          break;
        case 'email':
          result = a.email.compareTo(b.email);
          break;
        case 'status':
          result = a.status.name.compareTo(b.status.name);
          break;
        case 'bills':
          final aCount = _tenantBillCounts[a.id] ?? 0;
          final bCount = _tenantBillCounts[b.id] ?? 0;
          result = aCount.compareTo(bCount);
          break;
        default:
          result = '${a.firstName} ${a.lastName}'.compareTo('${b.firstName} ${b.lastName}');
      }
      
      return _sortAscending ? result : -result;
    });
    
    setState(() {
      _filteredTenants = filtered;
    });
  }

  void _filterTenants(String query) {
    setState(() {
      _searchQuery = query;
      _applyFiltersAndSort();
    });
  }

  void _selectTenant(Tenant tenant) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => SelectBillsForPaymentPage(tenant: tenant),
      ),
    ).then((result) {
      if (result == true && mounted) {
        Navigator.of(context).pop(true); // Return true to indicate payment was processed
      }
    });
  }

  void _showSortFilterBottomSheet() {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setModalState) {
            return Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Sort & Filter',
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                      TextButton(
                        onPressed: () {
                          setModalState(() {
                            _sortBy = 'name';
                            _sortAscending = true;
                            _statusFilter = null;
                          });
                        },
                        child: const Text('Reset'),
                      ),
                    ],
                  ),
                  const Divider(),
                  
                  // Sort options
                  Text(
                    'Sort by',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const SizedBox(height: 8),
                  Wrap(
                    spacing: 8.0,
                    children: [
                      ChoiceChip(
                        label: const Text('Name'),
                        selected: _sortBy == 'name',
                        onSelected: (selected) {
                          if (selected) {
                            setModalState(() {
                              _sortBy = 'name';
                            });
                          }
                        },
                      ),
                      ChoiceChip(
                        label: const Text('Email'),
                        selected: _sortBy == 'email',
                        onSelected: (selected) {
                          if (selected) {
                            setModalState(() {
                              _sortBy = 'email';
                            });
                          }
                        },
                      ),
                      ChoiceChip(
                        label: const Text('Status'),
                        selected: _sortBy == 'status',
                        onSelected: (selected) {
                          if (selected) {
                            setModalState(() {
                              _sortBy = 'status';
                            });
                          }
                        },
                      ),
                      ChoiceChip(
                        label: const Text('Bill Count'),
                        selected: _sortBy == 'bills',
                        onSelected: (selected) {
                          if (selected) {
                            setModalState(() {
                              _sortBy = 'bills';
                            });
                          }
                        },
                      ),
                    ],
                  ),
                  
                  // Sort direction
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      const Text('Sort direction:'),
                      const SizedBox(width: 16),
                      ChoiceChip(
                        label: const Text('Ascending'),
                        selected: _sortAscending,
                        onSelected: (selected) {
                          if (selected) {
                            setModalState(() {
                              _sortAscending = true;
                            });
                          }
                        },
                      ),
                      const SizedBox(width: 8),
                      ChoiceChip(
                        label: const Text('Descending'),
                        selected: !_sortAscending,
                        onSelected: (selected) {
                          if (selected) {
                            setModalState(() {
                              _sortAscending = false;
                            });
                          }
                        },
                      ),
                    ],
                  ),
                  
                  // Status filter
                  const SizedBox(height: 16),
                  Text(
                    'Filter by Status',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const SizedBox(height: 8),
                  Wrap(
                    spacing: 8.0,
                    children: [
                      ChoiceChip(
                        label: const Text('All'),
                        selected: _statusFilter == null,
                        onSelected: (selected) {
                          if (selected) {
                            setModalState(() {
                              _statusFilter = null;
                            });
                          }
                        },
                      ),
                      ...TenantStatus.values.map((status) {
                        return ChoiceChip(
                          label: Text(_getTenantStatusName(status)),
                          selected: _statusFilter == status,
                          onSelected: (selected) {
                            setModalState(() {
                              _statusFilter = selected ? status : null;
                            });
                          },
                        );
                      }),
                    ],
                  ),
                  
                  // Apply button
                  const SizedBox(height: 24),
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: () {
                        Navigator.pop(context);
                        _applyFiltersAndSort();
                      },
                      child: const Text('Apply'),
                    ),
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }

  String _getTenantStatusName(TenantStatus status) {
    switch (status) {
      case TenantStatus.active:
        return 'Active';
      case TenantStatus.pending:
        return 'Pending';
      case TenantStatus.movedOut:
        return 'Moved Out';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Select Tenant'),
        actions: [
          // Show loading indicator in the app bar
          if (_isLoading)
            Container(
              padding: const EdgeInsets.all(10),
              child: const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                ),
              ),
            ),
          IconButton(
            icon: const Icon(Icons.refresh),
            tooltip: 'Refresh',
            onPressed: _loadTenants,
          ),
          IconButton(
            icon: const Icon(Icons.filter_list),
            tooltip: 'Sort & Filter',
            onPressed: _showSortFilterBottomSheet,
          ),
        ],
      ),
      body: Column(
        children: [
          // Top info bar
          Container(
            color: Theme.of(context).colorScheme.surfaceContainerHighest,
            padding: const EdgeInsets.all(16.0),
            child: Text(
              'Select a tenant to record payment for their bills',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ),
          
          // Search bar
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: TextField(
              onChanged: _filterTenants,
              decoration: InputDecoration(
                labelText: 'Search tenants',
                hintText: 'Enter name or email',
                prefixIcon: const Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
                suffixIcon: _searchQuery.isNotEmpty
                    ? IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () {
                          _filterTenants('');
                        },
                      )
                    : null,
              ),
            ),
          ),
          
          // Active filters chips
          if (_statusFilter != null)
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: Wrap(
                spacing: 8.0,
                children: [
                  Chip(
                    label: Text('Status: ${_getTenantStatusName(_statusFilter!)}'),
                    onDeleted: () {
                      setState(() {
                        _statusFilter = null;
                        _applyFiltersAndSort();
                      });
                    },
                  ),
                ],
              ),
            ),
          
          // Tenants with outstanding bills label
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
            child: Row(
              children: [
                const Icon(Icons.info_outline, size: 16),
                const SizedBox(width: 8),
                Text(
                  _filteredTenants.isNotEmpty
                      ? 'Found ${_filteredTenants.length} tenant(s)'
                      : 'No tenants found',
                  style: Theme.of(context).textTheme.bodySmall,
                ),
                const Spacer(),
                Text(
                  'Sorted by: ${_sortBy} (${_sortAscending ? 'asc' : 'desc'})',
                  style: Theme.of(context).textTheme.bodySmall,
                ),
              ],
            ),
          ),
          
          // Tenants list
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _hasError
                    ? Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            const Icon(
                              Icons.error_outline,
                              size: 64,
                              color: Colors.red,
                            ),
                            const SizedBox(height: 16),
                            Text(
                              'Error loading tenants',
                              style: Theme.of(context).textTheme.titleMedium,
                            ),
                            const SizedBox(height: 8),
                            Text(_errorMessage),
                            const SizedBox(height: 16),
                            ElevatedButton(
                              onPressed: _loadTenants,
                              child: const Text('Retry'),
                            ),
                          ],
                        ),
                      )
                    : _filteredTenants.isEmpty
                        ? Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                const Icon(
                                  Icons.person_off,
                                  size: 64,
                                  color: Colors.grey,
                                ),
                                const SizedBox(height: 16),
                                Text(
                                  _searchQuery.isEmpty && _statusFilter == null
                                      ? 'No tenants found'
                                      : 'No tenants match your filters',
                                  style: Theme.of(context).textTheme.titleMedium,
                                ),
                                const SizedBox(height: 16),
                                ElevatedButton(
                                  onPressed: () {
                                    setState(() {
                                      _searchQuery = '';
                                      _statusFilter = null;
                                      _applyFiltersAndSort();
                                    });
                                  },
                                  child: const Text('Clear Filters'),
                                ),
                              ],
                            ),
                          )
                        : ListView.builder(
                            itemCount: _filteredTenants.length,
                            itemBuilder: (context, index) {
                              final tenant = _filteredTenants[index];
                              return ListTile(
                                leading: CircleAvatar(
                                  backgroundColor: _getTenantStatusColor(tenant.status),
                                  child: Text(
                                    tenant.firstName[0] + tenant.lastName[0],
                                    style: const TextStyle(color: Colors.white),
                                  ),
                                ),
                                title: Text('${tenant.firstName} ${tenant.lastName}'),
                                subtitle: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(tenant.email),
                                    Text(
                                      'Status: ${_getTenantStatusName(tenant.status)}',
                                      style: TextStyle(
                                        color: _getTenantStatusColor(tenant.status),
                                        fontSize: 12,
                                      ),
                                    ),
                                  ],
                                ),
                                isThreeLine: true,
                                trailing: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    if (_tenantBillCounts.containsKey(tenant.id) && 
                                        _tenantBillCounts[tenant.id]! > 0)
                                      Container(
                                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                        decoration: BoxDecoration(
                                          color: Colors.red,
                                          borderRadius: BorderRadius.circular(12),
                                        ),
                                        child: Text(
                                          '${_tenantBillCounts[tenant.id]} bills',
                                          style: const TextStyle(
                                            color: Colors.white,
                                            fontSize: 12,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                      ),
                                    const SizedBox(width: 8),
                                    const Icon(Icons.arrow_forward_ios, size: 16),
                                  ],
                                ),
                                onTap: () => _selectTenant(tenant),
                              );
                            },
                          ),
          ),
        ],
      ),
    );
  }
  
  Color _getTenantStatusColor(TenantStatus status) {
    switch (status) {
      case TenantStatus.active:
        return Colors.green;
      case TenantStatus.pending:
        return Colors.orange;
      case TenantStatus.movedOut:
        return Colors.grey;
    }
  }
} 