-- Create a view for properties with room counts
CREATE OR REPLACE VIEW public.properties_with_stats AS
SELECT 
    p.*,
    COUNT(r.id) AS room_count,
    SUM(CASE WHEN r.occupancy_status = 'vacant' THEN 1 ELSE 0 END) AS vacant_rooms,
    SUM(CASE WHEN r.occupancy_status = 'occupied' THEN 1 ELSE 0 END) AS occupied_rooms,
    SUM(CASE WHEN r.occupancy_status = 'reserved' THEN 1 ELSE 0 END) AS reserved_rooms,
    SUM(CASE WHEN r.occupancy_status = 'maintenance' THEN 1 ELSE 0 END) AS maintenance_rooms,
    COALESCE(SUM(r.rental_price), 0) AS total_potential_income,
    COALESCE(SUM(CASE WHEN r.occupancy_status = 'occupied' THEN r.rental_price ELSE 0 END), 0) AS current_income
FROM 
    public.properties p
LEFT JOIN 
    public.rooms r ON p.id = r.property_id
GROUP BY 
    p.id;

-- Create a view for rooms with amenities
CREATE OR REPLACE VIEW public.rooms_with_amenities AS
SELECT 
    r.*,
    p.name AS property_name,
    p.address AS property_address,
    p.city AS property_city,
    p.state AS property_state,
    p.zip_code AS property_zip_code,
    p.user_id,
    COALESCE(
        json_agg(
            json_build_object(
                'id', COALESCE(a.id::text, ra.id::text),
                'name', COALESCE(a.name, ra.custom_amenity_name),
                'is_custom', CASE WHEN a.id IS NULL THEN TRUE ELSE FALSE END
            )
        ) FILTER (WHERE a.id IS NOT NULL OR ra.custom_amenity_name IS NOT NULL),
        '[]'
    ) AS amenities
FROM 
    public.rooms r
JOIN 
    public.properties p ON r.property_id = p.id
LEFT JOIN 
    public.room_amenities ra ON r.id = ra.room_id
LEFT JOIN 
    public.amenities a ON ra.amenity_id = a.id
GROUP BY 
    r.id, p.id;

-- Create view for active tenants
CREATE OR REPLACE VIEW public.active_tenants AS
SELECT 
    t.*,
    r.name AS room_name,
    r.rental_price,
    r.property_id,  -- Use property_id from rooms table instead of adding p.id
    p.name AS property_name
FROM 
    public.tenants t
LEFT JOIN
    public.rooms r ON t.room_id = r.id
LEFT JOIN
    public.properties p ON r.property_id = p.id
WHERE
    t.status = 'active';

-- Create view for tenants with lease expiring soon (within next 30 days)
CREATE OR REPLACE VIEW public.expiring_leases AS
SELECT 
    t.*,
    r.name AS room_name,
    p.name AS property_name,
    -- Get property_id from the join with properties table
    COALESCE(r.property_id, NULL) AS property_id,
    (t.lease_end_date - CURRENT_DATE) AS days_remaining
FROM 
    public.tenants t
LEFT JOIN
    public.rooms r ON t.room_id = r.id
LEFT JOIN
    public.properties p ON r.property_id = p.id
WHERE
    t.status = 'active'
    AND t.lease_end_date IS NOT NULL
    AND t.lease_end_date BETWEEN CURRENT_DATE AND (CURRENT_DATE + INTERVAL '30 days');

-- Create view for vacant rooms (rooms without active tenants)
CREATE OR REPLACE VIEW public.vacant_rooms AS
SELECT 
    r.id,
    r.name,
    r.property_id,
    r.occupancy_status,
    r.rental_price,
    r.is_furnished,
    r.description,
    p.name AS property_name,
    p.address AS property_address
FROM 
    public.rooms r
LEFT JOIN
    public.properties p ON r.property_id = p.id
LEFT JOIN
    public.tenants t ON r.id = t.room_id AND t.status = 'active'
WHERE
    t.id IS NULL
    OR r.occupancy_status = 'vacant';

-- Create view for tenant occupancy by property
CREATE OR REPLACE VIEW public.property_occupancy AS
SELECT 
    p.id,  -- Use p.id directly without aliasing it as property_id
    p.name AS property_name,
    COUNT(r.id) AS total_rooms,
    COUNT(t.id) AS occupied_rooms,
    (COUNT(r.id) - COUNT(t.id)) AS vacant_rooms,
    CASE 
        WHEN COUNT(r.id) > 0 THEN 
            ROUND((COUNT(t.id)::numeric / COUNT(r.id)::numeric) * 100, 2)
        ELSE 0
    END AS occupancy_rate
FROM 
    public.properties p
LEFT JOIN
    public.rooms r ON p.id = r.property_id
LEFT JOIN
    public.tenants t ON r.id = t.room_id AND t.status = 'active'
GROUP BY 
    p.id, p.name
ORDER BY 
    p.name;

-- Add RLS policies to views
ALTER VIEW public.properties_with_stats SET (security_barrier = true);
ALTER VIEW public.rooms_with_amenities SET (security_barrier = true);

-- Security for tenant-related views - note that we don't use SECURITY INVOKER directly
-- Instead, create secure access functions for these views

-- Create a function to get active tenants for the current user
CREATE OR REPLACE FUNCTION public.get_user_active_tenants()
RETURNS SETOF public.active_tenants
LANGUAGE sql
SECURITY DEFINER
SET search_path = public
AS $$
    -- Allow authenticated users to access all data
    SELECT * FROM public.active_tenants;
$$;

-- Create a function to get expiring leases for the current user
CREATE OR REPLACE FUNCTION public.get_user_expiring_leases()
RETURNS SETOF public.expiring_leases
LANGUAGE sql
SECURITY DEFINER
SET search_path = public
AS $$
    -- Allow authenticated users to access all data
    SELECT * FROM public.expiring_leases;
$$;

-- Create a function to get vacant rooms for the current user
CREATE OR REPLACE FUNCTION public.get_user_vacant_rooms()
RETURNS SETOF public.vacant_rooms
LANGUAGE sql
SECURITY DEFINER
SET search_path = public
AS $$
    -- Allow authenticated users to access all data
    SELECT * FROM public.vacant_rooms;
$$;

-- Create a function to get property occupancy for the current user
CREATE OR REPLACE FUNCTION public.get_user_property_occupancy()
RETURNS SETOF public.property_occupancy
LANGUAGE sql
SECURITY DEFINER
SET search_path = public
AS $$
    -- Allow authenticated users to access all data
    SELECT * FROM public.property_occupancy;
$$;

-- Create a function to get all rooms for a user
CREATE OR REPLACE FUNCTION public.get_user_rooms()
RETURNS SETOF public.rooms_with_amenities
LANGUAGE sql
SECURITY DEFINER
SET search_path = public
AS $$
    -- Allow authenticated users to access all data
    SELECT * FROM public.rooms_with_amenities;
$$;

-- Create a function to get all properties for a user
CREATE OR REPLACE FUNCTION public.get_user_properties()
RETURNS SETOF public.properties_with_stats
LANGUAGE sql
SECURITY DEFINER
SET search_path = public
AS $$
    -- Allow authenticated users to access all data
    SELECT * FROM public.properties_with_stats;
$$;

-- Create a function to update the updated_at column
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$;

-- Properties table policies
DROP POLICY IF EXISTS "Users can view their own properties" ON public.properties;
DROP POLICY IF EXISTS "Users can insert their own properties" ON public.properties;
DROP POLICY IF EXISTS "Users can update their own properties" ON public.properties;
DROP POLICY IF EXISTS "Users can delete their own properties" ON public.properties;

CREATE POLICY "Authenticated users can access all properties"
    ON public.properties
    FOR ALL
    TO authenticated
    USING (true)
    WITH CHECK (true);

-- Utility bills table policies
DROP POLICY IF EXISTS "Users can view utility bills for their own properties" ON public.utility_bills;
DROP POLICY IF EXISTS "Users can insert utility bills for their own properties" ON public.utility_bills;
DROP POLICY IF EXISTS "Users can update utility bills for their own properties" ON public.utility_bills;
DROP POLICY IF EXISTS "Users can delete utility bills for their own properties" ON public.utility_bills;

CREATE POLICY "Authenticated users can access all utility bills"
    ON public.utility_bills
    FOR ALL
    TO authenticated
    USING (true)
    WITH CHECK (true);

-- Rooms table policies
DROP POLICY IF EXISTS "Users can view their own rooms" ON public.rooms;
DROP POLICY IF EXISTS "Users can insert rooms for their own properties" ON public.rooms;
DROP POLICY IF EXISTS "Users can update rooms for their own properties" ON public.rooms;
DROP POLICY IF EXISTS "Users can delete rooms for their own properties" ON public.rooms;

CREATE POLICY "Authenticated users can access all rooms"
    ON public.rooms
    FOR ALL
    TO authenticated
    USING (true)
    WITH CHECK (true);

-- Room amenities table policies
DROP POLICY IF EXISTS "Users can view amenities for their own rooms" ON public.room_amenities;
DROP POLICY IF EXISTS "Users can insert amenities for their own rooms" ON public.room_amenities;
DROP POLICY IF EXISTS "Users can update amenities for their own rooms" ON public.room_amenities;
DROP POLICY IF EXISTS "Users can delete amenities for their own rooms" ON public.room_amenities;

CREATE POLICY "Authenticated users can access all room amenities"
    ON public.room_amenities
    FOR ALL
    TO authenticated
    USING (true)
    WITH CHECK (true);

-- Amenities table policies
DROP POLICY IF EXISTS "Authenticated users can view amenities" ON public.amenities;
DROP POLICY IF EXISTS "Only admins can insert amenities" ON public.amenities;
DROP POLICY IF EXISTS "Only admins can update amenities" ON public.amenities;
DROP POLICY IF EXISTS "Only admins can delete amenities" ON public.amenities;

CREATE POLICY "Authenticated users can access all amenities"
    ON public.amenities
    FOR ALL
    TO authenticated
    USING (true)
    WITH CHECK (true);

-- Room types table policies
DROP POLICY IF EXISTS "Authenticated users can view room types" ON public.room_types;
DROP POLICY IF EXISTS "Only admins can insert room types" ON public.room_types;
DROP POLICY IF EXISTS "Only admins can update room types" ON public.room_types;
DROP POLICY IF EXISTS "Only admins can delete room types" ON public.room_types;

CREATE POLICY "Authenticated users can access all room types"
    ON public.room_types
    FOR ALL
    TO authenticated
    USING (true)
    WITH CHECK (true); 