import 'package:flutter/material.dart';

class ExpenseCategoryModel {
  final String? id;
  final String name;
  final String? description;
  final Color color;
  final String? parentCategoryId;
  final String userId;
  final bool isDefault;
  final DateTime createdAt;
  final DateTime? updatedAt;

  ExpenseCategoryModel({
    this.id,
    required this.name,
    this.description,
    required this.color,
    this.parentCategoryId,
    required this.userId,
    this.isDefault = false,
    required this.createdAt,
    this.updatedAt,
  });

  factory ExpenseCategoryModel.fromJson(Map<String, dynamic> json) {
    return ExpenseCategoryModel(
      id: json['id'],
      name: json['name'],
      description: json['description'],
      color: Color(int.parse(json['color'], radix: 16)),
      parentCategoryId: json['parent_category_id'],
      userId: json['user_id'],
      isDefault: json['is_default'] ?? false,
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: json['updated_at'] != null
          ? DateTime.parse(json['updated_at'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'color': color.toHex(),
      'parent_category_id': parentCategoryId,
      'user_id': userId,
      'is_default': isDefault,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  ExpenseCategoryModel copyWith({
    String? id,
    String? name,
    String? description,
    Color? color,
    String? parentCategoryId,
    String? userId,
    bool? isDefault,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return ExpenseCategoryModel(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      color: color ?? this.color,
      parentCategoryId: parentCategoryId ?? this.parentCategoryId,
      userId: userId ?? this.userId,
      isDefault: isDefault ?? this.isDefault,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

// Extension to convert Color to hex string
extension ColorExtension on Color {
  String toHex() {
    // Helper function to convert component to hex
    String componentToHex(int value) {
      return value.toRadixString(16).padLeft(2, '0');
    }
    
    final alphaValue = (a * 255.0).round() & 0xFF;
    final redValue = (r * 255.0).round() & 0xFF;
    final greenValue = (g * 255.0).round() & 0xFF;
    final blueValue = (b * 255.0).round() & 0xFF;
    
    return componentToHex(alphaValue) +
           componentToHex(redValue) +
           componentToHex(greenValue) +
           componentToHex(blueValue);
  }
} 