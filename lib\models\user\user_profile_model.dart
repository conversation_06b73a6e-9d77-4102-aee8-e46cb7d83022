class UserProfile {
  final String id;
  final String? email;
  final String? fullName;
  final String? phoneNumber;
  final String? address;
  final DateTime? dateOfBirth;
  final String? profileImageUrl;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String? role;
  final String? firstName;
  final String? lastName;
  final String? displayName;

  UserProfile({
    required this.id,
    this.email,
    this.fullName,
    this.phoneNumber,
    this.address,
    this.dateOfBirth,
    this.profileImageUrl,
    required this.createdAt,
    required this.updatedAt,
    this.role,
    this.firstName,
    this.lastName,
    this.displayName,
  });

  // Create from JSON (from database)
  factory UserProfile.fromJson(Map<String, dynamic> json) {
    return UserProfile(
      id: json['id'] as String,
      email: json['email'] as String?,
      fullName: json['full_name'] as String?,
      phoneNumber: json['phone_number'] as String?,
      address: json['address'] as String?,
      dateOfBirth: json['date_of_birth'] != null
          ? DateTime.parse(json['date_of_birth'] as String)
          : null,
      profileImageUrl: json['profile_image_url'] as String?,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      role: json['role'] as String?,
      firstName: json['first_name'] as String?,
      lastName: json['last_name'] as String?,
      displayName: json['display_name'] as String?,
    );
  }

  // Create from Supabase user metadata
  factory UserProfile.fromUserMetadata(String id, Map<String, dynamic>? metadata, String? email) {
    final now = DateTime.now();
    return UserProfile(
      id: id,
      email: email,
      fullName: metadata?['full_name'] as String?,
      phoneNumber: metadata?['phone_number'] as String?,
      address: metadata?['address'] as String?,
      dateOfBirth: metadata?['date_of_birth'] != null
          ? DateTime.parse(metadata?['date_of_birth'] as String)
          : null,
      profileImageUrl: metadata?['profile_image_url'] as String?,
      createdAt: now,
      updatedAt: now,
      role: metadata?['role'] as String? ?? 'tenant',
      firstName: metadata?['first_name'] as String?,
      lastName: metadata?['last_name'] as String?,
      displayName: metadata?['display_name'] as String?,
    );
  }

  // Convert to JSON (for database)
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'email': email,
      'full_name': fullName,
      'phone_number': phoneNumber,
      'address': address,
      'date_of_birth': dateOfBirth?.toIso8601String(),
      'profile_image_url': profileImageUrl,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'role': role,
      'first_name': firstName,
      'last_name': lastName,
      'display_name': displayName,
    };
  }

  // Convert to user metadata (for Supabase auth)
  Map<String, dynamic> toUserMetadata() {
    return {
      'full_name': fullName,
      'phone_number': phoneNumber,
      'address': address,
      'date_of_birth': dateOfBirth?.toIso8601String(),
      'profile_image_url': profileImageUrl,
      'role': role,
      'first_name': firstName,
      'last_name': lastName,
      'display_name': displayName,
    };
  }

  // Create a copy with modified fields
  UserProfile copyWith({
    String? fullName,
    String? phoneNumber,
    String? address,
    DateTime? dateOfBirth,
    String? profileImageUrl,
    String? role,
    String? firstName,
    String? lastName,
    String? displayName,
  }) {
    return UserProfile(
      id: id,
      email: email,
      fullName: fullName ?? this.fullName,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      address: address ?? this.address,
      dateOfBirth: dateOfBirth ?? this.dateOfBirth,
      profileImageUrl: profileImageUrl ?? this.profileImageUrl,
      createdAt: createdAt,
      updatedAt: DateTime.now(),
      role: role ?? this.role,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      displayName: displayName ?? this.displayName,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    
    return other is UserProfile &&
      other.id == id &&
      other.email == email &&
      other.fullName == fullName &&
      other.phoneNumber == phoneNumber &&
      other.address == address &&
      other.dateOfBirth == dateOfBirth &&
      other.profileImageUrl == profileImageUrl &&
      other.role == role &&
      other.firstName == firstName &&
      other.lastName == lastName &&
      other.displayName == displayName;
  }

  @override
  int get hashCode => Object.hash(
    id,
    email,
    fullName,
    phoneNumber,
    address,
    dateOfBirth,
    profileImageUrl,
    role,
    firstName,
    lastName,
    displayName,
  );
} 