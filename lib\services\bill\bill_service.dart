import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:synchronized/synchronized.dart';
import '../../models/bill/bill.dart';
import '../../utils/logger.dart';

class BillService {
  final SupabaseClient _client = Supabase.instance.client;
  final String _tableName = 'bills';
  final String _metadataTable = 'billing_metadata';

  // Initialize or update the last used bill number
  Future<int> _getAndUpdateLastBillNumber() async {
    try {
      final metadata = await _client
          .from(_metadataTable)
          .select('last_bill_number')
          .eq('id', 'bill_number_tracker')
          .single();

      int lastNumber = metadata['last_bill_number'] ?? 1000000;
      int nextNumber = lastNumber + 1;

      await _client.from(_metadataTable).update({
        'last_bill_number': nextNumber,
        'updated_at': DateTime.now().toIso8601String(),
      }).eq('id', 'bill_number_tracker');

      return nextNumber;
    } catch (e) {
      AppLogger.error('Failed to retrieve or update the last bill number: $e');
      // Try to initialize the metadata table if it doesn't exist
      try {
        await _client.from(_metadataTable).insert({
          'id': 'bill_number_tracker',
          'last_bill_number': 1000001,
          'last_group_bill_number': 1000000,
        });
        return 1000001;
      } catch (initError) {
        AppLogger.error('Failed to initialize metadata table: $initError');
        return 1000001;
      }
    }
  }
  
  // Initialize or update the last used group bill number
  Future<int> _getAndUpdateLastGroupBillNumber() async {
    try {
      final metadata = await _client
          .from(_metadataTable)
          .select('last_group_bill_number')
          .eq('id', 'bill_number_tracker')
          .single();

      int lastNumber = metadata['last_group_bill_number'] ?? 1000000;
      int nextNumber = lastNumber + 1;

      await _client.from(_metadataTable).update({
        'last_group_bill_number': nextNumber,
        'updated_at': DateTime.now().toIso8601String(),
      }).eq('id', 'bill_number_tracker');

      return nextNumber;
    } catch (e) {
      AppLogger.error('Failed to retrieve or update the last group bill number: $e');
      // Try to initialize the metadata table if it doesn't exist
      try {
        await _client.from(_metadataTable).insert({
          'id': 'bill_number_tracker',
          'last_bill_number': 1000000,
          'last_group_bill_number': 1000001,
        });
        return 1000001;
      } catch (initError) {
        AppLogger.error('Failed to initialize metadata table: $initError');
        return 1000001;
      }
    }
  }
  
  // Locks for synchronizing bill number generation to prevent duplicates
  static final Lock _billNumberLock = Lock();
  static final Lock _groupBillNumberLock = Lock();

  // Create a new bill
  Future<Bill> createBill(Bill bill) async {
    try {
      // Prepare bill data
      final billData = bill.toJson();
      
      // Generate a bill number if one isn't provided
      if (billData['bill_number'] == null) {
        billData['bill_number'] = await generateBillNumber();
      }
      
      // Validate bill number uniqueness before inserting
      await _ensureBillNumberUnique(billData['bill_number']);
      
      // Handle utility-specific data
      if (bill.type != BillType.utility) {
        // Remove utility-specific fields if not a utility bill
        billData.remove('utility_type');
        billData.remove('previous_meter_reading');
        billData.remove('current_meter_reading');
        billData.remove('unit_consumed');
        billData.remove('rate_per_unit');
      }
      
      final response =
          await _client
              .from(_tableName)
              .insert(billData)
              .select()
              .single();

      return Bill.fromJson(response);
    } catch (e) {
      throw Exception('Failed to create bill: $e');
    }
  }

  // Get a bill by ID
  Future<Bill> getBill(String id) async {
    try {
      final response = await _client.from(_tableName).select().eq('id', id);

      if (response.isEmpty) {
        throw Exception('Bill not found with ID: $id');
      }

      return Bill.fromJson(response[0]);
    } catch (e) {
      AppLogger.error('Failed to get bill: $e');
      throw Exception('Failed to get bill: $e');
    }
  }

  // Get a bill by ID, returns null if not found (safer version)
  Future<Bill?> getBillSafe(String id) async {
    try {
      final response = await _client.from(_tableName).select().eq('id', id);

      if (response.isEmpty) {
        AppLogger.warning('Bill not found with ID: $id');
        return null;
      }

      return Bill.fromJson(response[0]);
    } catch (e) {
      AppLogger.error('Failed to get bill: $e');
      return null;
    }
  }

  // Get all bills
  Future<List<Bill>> getAllBills() async {
    try {
      final response = await _client
          .from(_tableName)
          .select()
          .order('due_date', ascending: true);

      return response.map<Bill>((data) => Bill.fromJson(data)).toList();
    } catch (e) {
      throw Exception('Failed to get bills: $e');
    }
  }

  // Get bills by tenant ID
  Future<List<Bill>> getBillsByTenant(String tenantId) async {
    try {
      final response = await _client
          .from(_tableName)
          .select()
          .eq('tenant_id', tenantId)
          .order('due_date', ascending: true);

      return response.map<Bill>((data) => Bill.fromJson(data)).toList();
    } catch (e) {
      throw Exception('Failed to get tenant bills: $e');
    }
  }

  // Get bills by property ID
  Future<List<Bill>> getBillsByProperty(String propertyId) async {
    try {
      final response = await _client
          .from(_tableName)
          .select()
          .eq('property_id', propertyId)
          .order('due_date', ascending: true);

      return response.map<Bill>((data) => Bill.fromJson(data)).toList();
    } catch (e) {
      throw Exception('Failed to get property bills: $e');
    }
  }

  // Get bills by room ID
  Future<List<Bill>> getBillsByRoom(String roomId) async {
    try {
      final response = await _client
          .from(_tableName)
          .select()
          .eq('room_id', roomId)
          .order('due_date', ascending: true);

      return response.map<Bill>((data) => Bill.fromJson(data)).toList();
    } catch (e) {
      throw Exception('Failed to get room bills: $e');
    }
  }

  // Get bills by status
  Future<List<Bill>> getBillsByStatus(BillStatus status) async {
    try {
      final response = await _client
          .from(_tableName)
          .select()
          .eq('status', status.name)
          .order('due_date', ascending: true);

      return response.map<Bill>((data) => Bill.fromJson(data)).toList();
    } catch (e) {
      throw Exception('Failed to get bills by status: $e');
    }
  }

  // Get overdue bills
  Future<List<Bill>> getOverdueBills() async {
    final now = DateTime.now().toIso8601String();

    try {
      final response = await _client
          .from(_tableName)
          .select()
          .eq('status', BillStatus.pending.name)
          .lt('due_date', now)
          .order('due_date', ascending: true);

      return response.map<Bill>((data) => Bill.fromJson(data)).toList();
    } catch (e) {
      throw Exception('Failed to get overdue bills: $e');
    }
  }

  // Update a bill
  Future<Bill> updateBill(Bill bill) async {
    try {
      // Prepare bill data
      final billData = bill.toJson();
      
      // Handle utility-specific data
      if (bill.type != BillType.utility) {
        // Remove utility-specific fields if not a utility bill
        billData.remove('utility_type');
        billData.remove('previous_meter_reading');
        billData.remove('current_meter_reading');
        billData.remove('unit_consumed');
        billData.remove('rate_per_unit');
      }
      
      final response =
          await _client
              .from(_tableName)
              .update(billData)
              .eq('id', bill.id)
              .select()
              .single();

      return Bill.fromJson(response);
    } catch (e) {
      throw Exception('Failed to update bill: $e');
    }
  }

  // Mark bill as paid
  Future<Bill> markBillAsPaid(
    String id, {
    double? paidAmount,
    DateTime? paidAt,
  }) async {
    final paymentDate = paidAt ?? DateTime.now();

    try {
      final bill = await getBill(id);
      final updatedBill = bill.copyWith(
        status: BillStatus.paid,
        paidAt: paymentDate,
        paidAmount: paidAmount ?? bill.amount,
        updatedAt: DateTime.now(),
      );

      return await updateBill(updatedBill);
    } catch (e) {
      throw Exception('Failed to mark bill as paid: $e');
    }
  }

  // Record partial payment
  Future<Bill> recordPartialPayment(String id, double amount) async {
    try {
      final bill = await getBill(id);
      final currentPaid = bill.paidAmount ?? 0;
      final totalPaid = currentPaid + amount;

      final updatedBill = bill.copyWith(
        paidAmount: totalPaid,
        status: totalPaid >= bill.amount ? BillStatus.paid : BillStatus.pending,
        paidAt: totalPaid >= bill.amount ? DateTime.now() : bill.paidAt,
        updatedAt: DateTime.now(),
      );

      return await updateBill(updatedBill);
    } catch (e) {
      throw Exception('Failed to record partial payment: $e');
    }
  }

  // Record a payment (handles both full and partial payments)
  Future<Bill> recordPayment({
    required String billId,
    required double amount,
    String? notes,
  }) async {
    try {
      final bill = await getBill(billId);
      
      // Check if bill is already fully paid
      if (bill.isFullyPaid()) {
        throw Exception('Bill ${bill.billNumber ?? billId} is already fully paid. Cannot record duplicate payment.');
      }
      
      final currentPaid = bill.paidAmount ?? 0;
      final totalPaid = currentPaid + amount;
      final isPaid = totalPaid >= bill.amount;

      final updatedBill = bill.copyWith(
        paidAmount: totalPaid,
        status: isPaid ? BillStatus.paid : BillStatus.pending,
        paidAt: isPaid ? DateTime.now() : bill.paidAt,
        notes:
            notes != null && notes.isNotEmpty
                ? (bill.notes != null && bill.notes!.isNotEmpty
                    ? '${bill.notes}\n${DateTime.now().toIso8601String()}: $notes'
                    : '${DateTime.now().toIso8601String()}: $notes')
                : bill.notes,
        updatedAt: DateTime.now(),
      );

      return await updateBill(updatedBill);
    } catch (e) {
      throw Exception('Failed to record payment: $e');
    }
  }

  // Get bills by date range and optionally by type
  Future<List<Bill>> getBillsByDateRange({
    required DateTime startDate,
    required DateTime endDate,
    BillType? type,
  }) async {
    try {
      var query = _client
          .from(_tableName)
          .select()
          .gte('due_date', startDate.toIso8601String())
          .lte('due_date', endDate.toIso8601String());
      
      // Apply type filter if provided
      if (type != null) {
        query = query.eq('type', type.name);
      }
      
      final response = await query.order('due_date', ascending: true);

      return response.map<Bill>((data) => Bill.fromJson(data)).toList();
    } catch (e) {
      throw Exception('Failed to get bills by date range: $e');
    }
  }

  // Delete a bill
  Future<void> deleteBill(String id) async {
    try {
      await _client.from(_tableName).delete().eq('id', id);
    } catch (e) {
      throw Exception('Failed to delete bill: $e');
    }
  }

  // Generate recurring bills based on a template bill
  Future<List<Bill>> generateRecurringBills(
    Bill templateBill,
    int count,
  ) async {
    try {
      List<Bill> generatedBills = [];

      for (int i = 0; i < count; i++) {
        // Calculate the next due date based on recurrence type
        final nextDueDate = _calculateNextDueDate(
          templateBill.dueDate,
          templateBill.recurrence,
          i + 1,
        );

        // Create a new bill based on the template
        final newBill = Bill(
          title: templateBill.title,
          description: templateBill.description,
          amount: templateBill.amount,
          dueDate: nextDueDate,
          type: templateBill.type,
          recurrence: templateBill.recurrence,
          tenantId: templateBill.tenantId,
          propertyId: templateBill.propertyId,
          roomId: templateBill.roomId,
          notes: templateBill.notes,
          includeInRent: templateBill.includeInRent,
          utilityType: templateBill.utilityType,
          // Do not copy meter readings to recurring bills
          ratePerUnit: templateBill.ratePerUnit,
          // Generate a new bill number for each recurring bill
          billNumber: await generateBillNumber(),
        );

        // Save the bill
        final createdBill = await createBill(newBill);
        generatedBills.add(createdBill);
      }

      return generatedBills;
    } catch (e) {
      throw Exception('Failed to generate recurring bills: $e');
    }
  }

  // Calculate the next due date based on recurrence type
  DateTime _calculateNextDueDate(
    DateTime baseDueDate,
    RecurrenceType recurrenceType,
    int occurrenceNumber,
  ) {
    switch (recurrenceType) {
      case RecurrenceType.monthly:
        return DateTime(
          baseDueDate.year,
          baseDueDate.month + occurrenceNumber,
          baseDueDate.day,
        );
      case RecurrenceType.quarterly:
        return DateTime(
          baseDueDate.year,
          baseDueDate.month + (occurrenceNumber * 3),
          baseDueDate.day,
        );
      case RecurrenceType.yearly:
        return DateTime(
          baseDueDate.year + occurrenceNumber,
          baseDueDate.month,
          baseDueDate.day,
        );
      case RecurrenceType.oneTime:
        return baseDueDate;
    }
  }
  
  // Check if a rent bill exists for a tenant in a specific month
  Future<Bill?> checkExistingRentBill(String tenantId, DateTime date, {String? excludeBillId}) async {
    try {
      // Get the first and last day of the month
      final firstDayOfMonth = DateTime(date.year, date.month, 1);
      final lastDayOfMonth = DateTime(date.year, date.month + 1, 0);
      
      // Query for rent bills in that month for the tenant
      final response = await _client
          .from(_tableName)
          .select()
          .eq('tenant_id', tenantId)
          .eq('type', BillType.rent.name)
          .gte('due_date', firstDayOfMonth.toIso8601String())
          .lte('due_date', lastDayOfMonth.toIso8601String())
          .order('due_date', ascending: true);
      
      final bills = response.map<Bill>((data) => Bill.fromJson(data)).toList();
      
      // Filter out the bill being edited if excludeBillId is provided
      if (excludeBillId != null) {
        bills.removeWhere((bill) => bill.id == excludeBillId);
      }
      
      // Return the first rent bill found, or null if none exists
      return bills.isNotEmpty ? bills.first : null;
    } catch (e) {
      AppLogger.error('Failed to check existing rent bill: $e');
      return null;
    }
  }
  
  // Generate unique group bill numbers with absolute duplicate prevention
  Future<List<String>> generateGroupBillNumbers(int count) async {
    return await _groupBillNumberLock.synchronized(() async {
      try {
        // Generate sequential bill numbers using metadata table
        List<String> billNumbers = [];
        
        for (int i = 0; i < count; i++) {
          int nextNumber = await _getAndUpdateLastGroupBillNumber();
          billNumbers.add('Bill_G-$nextNumber');
        }

        return billNumbers;
      } catch (e) {
        AppLogger.error('Failed to generate group bill numbers: $e');
        // Emergency fallback with timestamp
        final timestamp = DateTime.now().millisecondsSinceEpoch;
        return List.generate(count, (i) => 'Bill_G-${timestamp}_$i');
      }
    });
  }
  
  // Generate a unique bill number with absolute duplicate prevention
  Future<String> generateBillNumber() async {
    return await _billNumberLock.synchronized(() async {
      try {
        // Retrieve and update the last used bill number
        int nextNumber = await _getAndUpdateLastBillNumber();
        return nextNumber.toString();
      } catch (e) {
        AppLogger.error('Failed to generate bill number: $e');
        // Emergency fallback with timestamp to ensure uniqueness
        return DateTime.now().millisecondsSinceEpoch.toString();
      }
    });
  }
  
  // Ensure bill number is unique before creating a bill
  Future<void> _ensureBillNumberUnique(String billNumber) async {
    try {
      final existingBill = await _client
          .from(_tableName)
          .select('id')
          .eq('bill_number', billNumber)
          .limit(1);
      
      if (existingBill.isNotEmpty) {
        throw Exception('Bill number $billNumber already exists. This should not happen with proper generation.');
      }
    } catch (e) {
      AppLogger.error('Bill number uniqueness check failed: $e');
      throw Exception('Failed to verify bill number uniqueness: $e');
    }
  }
}
