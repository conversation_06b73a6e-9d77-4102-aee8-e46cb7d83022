# Community Notices System Documentation

## Overview

The Community Notices System is a comprehensive feature for the Tenanta property management app that allows property owners/managers to create, manage, and distribute notices to tenants. The system includes role-based permissions, filtering capabilities, and automated expiration handling.

## Features

### Core Functionality
- **CRUD Operations**: Create, read, update, and delete community notices
- **Multiple Property Support**: Create notices for multiple properties simultaneously
- **Role-Based Access**: Property managers have full access, tenants have read-only access
- **Notice Types**: General, Maintenance, Emergency, Event, Policy
- **Priority Levels**: Low, Medium, High, Urgent
- **Expiration Management**: Automatic archiving of expired notices
- **Search & Filtering**: Filter by type, priority, multiple properties, and search content
- **Responsive Design**: Mobile-optimized interface

### User Roles & Permissions

#### Property Owners/Managers
- Create new notices for their properties
- Edit and delete their own notices
- View all notices for their properties
- Access to urgent notices dashboard widget

#### Tenants
- View notices for properties where they have active leases
- Read-only access to notice details
- Access to urgent notices relevant to their properties

## Database Schema

### Main Table: `community_notices`

```sql
CREATE TABLE community_notices (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    title VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    type VARCHAR(50) NOT NULL CHECK (type IN ('general', 'maintenance', 'emergency', 'event', 'policy')),
    priority VARCHAR(20) NOT NULL CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
    property_id UUID NOT NULL REFERENCES properties(id) ON DELETE CASCADE,
    author_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    author_name VARCHAR(255) NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ,
    expires_at TIMESTAMPTZ,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    attachment_urls TEXT[],
    metadata JSONB
);
```

### Row Level Security (RLS) Policies

1. **Property managers can manage community notices**
   - Allows full CRUD access for property owners/managers
   - Scoped to properties they own

2. **Tenants can view community notices for their properties**
   - Read-only access for tenants
   - Scoped to properties where they have active leases

### Database Views

#### `active_community_notices`
- Combines notice data with property information
- Filters out inactive and expired notices
- Orders by priority and creation date

### Database Functions

1. **`get_property_notices(property_uuid)`**: Returns active notices for a specific property
2. **`get_urgent_notices()`**: Returns urgent/high priority notices for current user
3. **`archive_expired_notices()`**: Archives notices that have passed their expiration date
4. **`update_community_notice_updated_at()`**: Trigger function to update timestamps

## Flutter Implementation

### File Structure

```
lib/
├── models/community_notice/
│   └── community_notice.dart          # Data model with enums
├── services/community_notice/
│   └── community_notice_service.dart  # Service layer for API calls
├── screens/community_notices/
│   ├── community_notices_screen.dart  # Main list screen
│   ├── create_notice_screen.dart      # Create/edit form
│   └── notice_detail_screen.dart      # Detail view
└── widgets/
    ├── community_notices/
    │   └── property_notices_widget.dart # Property-specific widget
    └── dashboard/
        └── urgent_notices_widget.dart   # Dashboard widget
```

### Key Components

#### 1. CommunityNotice Model
- Comprehensive data model with type-safe enums
- JSON serialization/deserialization
- Helper methods for expiration and urgency checks

#### 2. CommunityNoticeService
- Complete CRUD operations
- Advanced filtering and search capabilities
- Statistics and analytics methods
- Error handling and logging

#### 3. Screens

**Community Notices List Screen**
- Displays all notices with filtering options
- Search functionality
- Property-based filtering
- Pull-to-refresh support

**Notice Detail Screen**
- Full notice content display
- Edit/delete options for authors
- Share functionality
- Attachment support (future enhancement)

**Create/Edit Notice Screen**
- Comprehensive form with validation
- Property selection
- Type and priority selection
- Optional expiration date/time picker

#### 4. Widgets

**Property Notices Widget**
- Embeddable widget for property detail pages
- Shows recent notices for specific property
- Quick create notice functionality

**Urgent Notices Widget**
- Dashboard widget for high-priority notices
- Displays urgent and high priority notices
- Quick navigation to full notice list

## Navigation Integration

### Routes Added
- `/community-notices` - Main notices list
- `/community-notices/create` - Create new notice
- `/community-notices/edit` - Edit existing notice
- `/community-notices/detail` - Notice detail view

### Navigation Menu
- Added "Community Notices" item to app drawer
- Icon: `Icons.campaign`
- Positioned between Expenses and Reports

## Multiple Property Support

### Creating Notices for Multiple Properties

The system supports creating notices for multiple properties simultaneously:

1. **Property Selection Dialog**: Users can select multiple properties from a searchable list
2. **Batch Creation**: One notice is created for each selected property
3. **Efficient Management**: Reduces time needed to distribute notices across multiple properties
4. **Visual Feedback**: Shows count of selected properties and created notices

### Filtering by Multiple Properties

The list screen allows filtering by multiple properties:

1. **Property Filter Button**: Shows count of selected properties
2. **Multi-Select Dialog**: Search and select multiple properties
3. **Combined Results**: Shows notices from all selected properties
4. **Clear Filters**: Easy reset to view all notices

## Usage Examples

### Creating a Notice

```dart
// Navigate to create screen
Navigator.pushNamed(context, '/community-notices/create');

// Or with property pre-selected
Navigator.pushNamed(
  context,
  '/community-notices/create',
  arguments: propertyId,
);
```

### Creating Notice for Multiple Properties

```dart
// User selects multiple properties in the create screen
// System automatically creates one notice per selected property
// Example: Selecting 3 properties creates 3 identical notices
```

### Displaying Property Notices

```dart
// Add to property detail screen
PropertyNoticesWidget(
  propertyId: property.id,
  propertyName: property.name,
  showCreateButton: true,
  maxNotices: 3,
)
```

### Dashboard Integration

```dart
// Add to dashboard
UrgentNoticesWidget()
```

## Service Integration

### Service Locator Registration

```dart
// In service_locator.dart
late CommunityNoticeService communityNoticeService;

// Initialize in initialize() method
communityNoticeService = CommunityNoticeService();
```

### Usage in Components

```dart
// Access service
final noticeService = serviceLocator.communityNoticeService;

// Get notices for property
final notices = await noticeService.getNoticesForProperty(propertyId);

// Create notice
final newNotice = CommunityNotice.create(
  title: 'Maintenance Notice',
  content: 'Scheduled maintenance on...',
  type: NoticeType.maintenance,
  priority: NoticePriority.high,
  propertyId: propertyId,
  authorId: currentUser.id,
  authorName: authorName,
);

await noticeService.createNotice(newNotice);
```

## Customization Options

### Notice Types
Modify the `NoticeType` enum to add new types:

```dart
enum NoticeType {
  general,
  maintenance,
  emergency,
  event,
  policy,
  // Add new types here
}
```

### Priority Levels
Extend the `NoticePriority` enum:

```dart
enum NoticePriority {
  low,
  medium,
  high,
  urgent,
  // Add new priorities here
}
```

### Styling
- Modify color schemes in `_buildTypeChip()` and `_buildPriorityChip()` methods
- Customize card layouts in notice list items
- Adjust widget sizing and spacing

## Security Considerations

1. **Row Level Security**: All database access is protected by RLS policies
2. **Authentication**: All operations require authenticated users
3. **Authorization**: Users can only access notices for their properties/leases
4. **Input Validation**: Form validation prevents malicious input
5. **SQL Injection Protection**: Parameterized queries via Supabase client

## Performance Optimizations

1. **Pagination**: Limit queries with configurable limits
2. **Indexing**: Database indexes on frequently queried columns
3. **Caching**: Service-level caching for frequently accessed data
4. **Lazy Loading**: Widgets load data only when needed
5. **Efficient Queries**: Use database views for complex joins

## Troubleshooting

### Common Issues

1. **Permission Denied Errors**
   - Verify RLS policies are correctly applied
   - Check user authentication status
   - Ensure user has access to the property

2. **Notice Not Appearing**
   - Check if notice is active (`is_active = true`)
   - Verify expiration date hasn't passed
   - Confirm property association

3. **Create/Edit Failures**
   - Validate all required fields are filled
   - Check property selection
   - Verify user permissions

### Debug Mode
Enable detailed logging by setting log level in `AppLogger`:

```dart
AppLogger.setLevel(LogLevel.debug);
```

## Future Enhancements

1. **File Attachments**: Support for document and image attachments
2. **Push Notifications**: Real-time notifications for urgent notices
3. **Email Integration**: Automatic email distribution
4. **Templates**: Pre-defined notice templates
5. **Analytics**: Notice engagement and read statistics
6. **Bulk Operations**: Mass create/update/delete operations
7. **Scheduling**: Schedule notices for future publication

## Migration Guide

To apply the community notices system to your database:

1. Run the migration script: `migrations/20_create_community_notices_table.sql`
2. Verify RLS policies are active
3. Test with sample data
4. Update your Flutter app with the new code
5. Test all functionality thoroughly

## Support

For issues or questions regarding the Community Notices system:

1. Check this documentation first
2. Review error logs in the app
3. Verify database permissions and policies
4. Test with minimal data to isolate issues

## Changelog

### Version 1.0.0
- Initial implementation
- Basic CRUD operations
- RLS policies
- Flutter UI components
- Navigation integration
- Documentation
