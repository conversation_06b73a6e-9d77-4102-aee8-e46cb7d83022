import 'package:flutter/material.dart';
import '../../models/bill/bill.dart';
import '../../models/payment/payment_model.dart';
import '../../services/service_locator.dart';
import '../../utils/currency_formatter.dart';
import '../../utils/logger.dart';

enum IncomeType {
  rent,
  utility,
  service,
  other
}

class IncomeTypeView extends StatelessWidget {
  final List<Payment> payments;
  final DateTimeRange selectedDateRange;
  final Function(double, String)? onAmountTap;

  const IncomeTypeView({
    super.key,
    required this.payments,
    required this.selectedDateRange,
    this.onAmountTap,
  });

  Future<Map<IncomeType, List<Payment>>> _categorizePaymentsByType() async {
    final paymentsByType = <IncomeType, List<Payment>>{};
    
    // Initialize the map with empty lists
    for (final type in IncomeType.values) {
      paymentsByType[type] = [];
    }
    
    AppLogger.info('Starting payment categorization for ${payments.length} payments');
    AppLogger.info('Date range: ${selectedDateRange.start} to ${selectedDateRange.end}');
    
    for (final payment in payments) {
      AppLogger.info('Processing payment: ID=${payment.id}, Amount=${payment.amount}, Date=${payment.paymentDate}');
      
      if (payment.billIds.isEmpty) {
        AppLogger.info('Payment ${payment.id} has no associated bills, categorizing as Other');
        paymentsByType[IncomeType.other]!.add(payment);
        continue;
      }

      Map<String, double> billAmounts = {};
      Map<String, BillType> billTypes = {};

      // First pass: Get all bill amounts and types
      for (final billId in payment.billIds) {
        try {
          final bill = await serviceLocator.billService.getBill(billId);
          billAmounts[billId] = bill.amount;
          billTypes[billId] = bill.type;
          AppLogger.info('Found bill: ID=$billId, Type=${bill.type.name}, Amount=${bill.amount}');
        } catch (e) {
          AppLogger.error('Error getting bill details for ID $billId: $e');
        }
      }

      // Calculate total bill amounts for proportion
      final totalBillAmount = billAmounts.values.fold<double>(0, (sum, amount) => sum + amount);
      AppLogger.info('Total bill amount for payment ${payment.id}: $totalBillAmount');

      // Second pass: Categorize payments with proportional amounts
      for (final billId in payment.billIds) {
        try {
          if (!billAmounts.containsKey(billId)) {
            AppLogger.warning('Skipping bill $billId as it was not found in first pass');
            continue;
          }

          // Calculate proportional amount for this bill
          double billProportion = totalBillAmount > 0 ? billAmounts[billId]! / totalBillAmount : 1.0 / payment.billIds.length;
          double adjustedAmount = payment.amount * billProportion;
          
          AppLogger.info('Bill $billId proportion: $billProportion, adjusted amount: $adjustedAmount');
          
          // Create a copy of the payment with adjusted amount
          final adjustedPayment = Payment(
            id: payment.id,
            amount: adjustedAmount,
            paymentDate: payment.paymentDate,
            method: payment.method,
            receiptReference: payment.receiptReference,
            notes: payment.notes,
            tenantId: payment.tenantId,
            billIds: [billId],
            createdAt: payment.createdAt,
            updatedAt: payment.updatedAt,
          );
          
          // Categorize based on bill type
          final billType = billTypes[billId];
          AppLogger.info('Categorizing bill $billId of type ${billType?.name}');
          
          switch (billType) {
            case BillType.rent:
              AppLogger.info('Adding payment ${payment.id} to Rent category');
              paymentsByType[IncomeType.rent]!.add(adjustedPayment);
              break;
            case BillType.utility:
              AppLogger.info('Adding payment ${payment.id} to Utility category');
              paymentsByType[IncomeType.utility]!.add(adjustedPayment);
              break;
            case BillType.service:
              AppLogger.info('Adding payment ${payment.id} to Service category');
              paymentsByType[IncomeType.service]!.add(adjustedPayment);
              break;
            default:
              AppLogger.info('Adding payment ${payment.id} to Other category (type: ${billType?.name})');
              paymentsByType[IncomeType.other]!.add(adjustedPayment);
          }
        } catch (e) {
          AppLogger.error('Error processing bill $billId: $e');
          
          // For payments with missing bills, add a proportional amount to other category
          final adjustedAmount = payment.amount / payment.billIds.length;
          final adjustedPayment = Payment(
            id: payment.id,
            amount: adjustedAmount,
            paymentDate: payment.paymentDate,
            method: payment.method,
            receiptReference: payment.receiptReference,
            notes: '${payment.notes ?? ''} [Bill not found]',
            tenantId: payment.tenantId,
            billIds: [billId],
            createdAt: payment.createdAt,
            updatedAt: payment.updatedAt,
          );
          
          paymentsByType[IncomeType.other]!.add(adjustedPayment);
          AppLogger.info('Added payment ${payment.id} to Other category due to error');
        }
      }
    }
    
    // Log final categorization results
    for (final type in IncomeType.values) {
      final typePayments = paymentsByType[type]!;
      final totalAmount = typePayments.fold<double>(0, (sum, p) => sum + p.amount);
      AppLogger.info('${_getIncomeTypeName(type)}: ${typePayments.length} payments, total amount: $totalAmount');
    }
    
    return paymentsByType;
  }

  String _getIncomeTypeName(IncomeType type) {
    switch (type) {
      case IncomeType.rent:
        return 'Rent Income';
      case IncomeType.utility:
        return 'Utility Payments';
      case IncomeType.service:
        return 'Service Charges';
      case IncomeType.other:
        return 'Other Income';
    }
  }

  IconData _getIncomeTypeIcon(IncomeType type) {
    switch (type) {
      case IncomeType.rent:
        return Icons.home;
      case IncomeType.utility:
        return Icons.electric_bolt;
      case IncomeType.service:
        return Icons.build;
      case IncomeType.other:
        return Icons.attach_money;
    }
  }

  Color _getIncomeTypeColor(IncomeType type) {
    switch (type) {
      case IncomeType.rent:
        return Colors.blue;
      case IncomeType.utility:
        return Colors.orange;
      case IncomeType.service:
        return Colors.purple;
      case IncomeType.other:
        return Colors.green;
    }
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<Map<IncomeType, List<Payment>>>(
      future: _categorizePaymentsByType(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }

        if (snapshot.hasError) {
          return Center(
            child: Text(
              'Error loading income types: ${snapshot.error}',
              style: TextStyle(color: Colors.red[700]),
            ),
          );
        }

        final paymentsByType = snapshot.data ?? {};
        if (paymentsByType.isEmpty) {
          return Center(
            child: Text(
              'No income data available',
              style: TextStyle(color: Colors.grey[600]),
            ),
          );
        }

        // Calculate totals and sort by amount
        final incomeData = paymentsByType.entries.map((entry) {
          final type = entry.key;
          final typePayments = entry.value;
          final amount = typePayments.fold<double>(0, (sum, payment) => sum + payment.amount);
          final transactionCount = typePayments.length;
          final averageAmount = transactionCount > 0 ? (amount / transactionCount).toDouble() : 0.0;
          
          return IncomeData(
            type: type,
            amount: amount,
            transactionCount: transactionCount,
            averageAmount: averageAmount,
            payments: typePayments,
          );
        }).toList()
          ..sort((a, b) => b.amount.compareTo(a.amount));

        final totalIncome = incomeData.fold<double>(0, (sum, data) => sum + data.amount);

        return ListView.builder(
          padding: const EdgeInsets.all(16.0),
          itemCount: incomeData.length + 1, // +1 for the summary card
          itemBuilder: (context, index) {
            if (index == 0) {
              // Summary card at the top
                return Card(
                  elevation: 2,
                margin: const EdgeInsets.only(bottom: 16.0),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                      const Text(
                        'Income Summary',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 18,
                        ),
                        ),
                        const SizedBox(height: 8),
                        GestureDetector(
                          onTap: onAmountTap != null 
                            ? () => onAmountTap!(totalIncome, 'Total Income')
                            : null,
                          child: Text(
                            CurrencyFormatter.formatCompactAmount(totalIncome),
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 20,
                              decoration: onAmountTap != null ? TextDecoration.underline : null,
                            ),
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                        'Total Transactions: ${incomeData.fold<int>(0, (sum, data) => sum + data.transactionCount)}',
                        style: TextStyle(
                          color: Colors.grey[700],
                        ),
                      ),
                      const SizedBox(height: 16),
                      const Text(
                        'Income Breakdown',
                          style: TextStyle(
                          fontWeight: FontWeight.w500,
                            fontSize: 16,
                          ),
                        ),
                      const SizedBox(height: 8),
                      ...incomeData.map((data) {
                        final percentage = totalIncome > 0 
                            ? (data.amount / totalIncome * 100).toStringAsFixed(1)
                            : '0.0';
                            
                        return Padding(
                          padding: const EdgeInsets.only(bottom: 8.0),
                          child: Row(
                            children: [
                              Container(
                                width: 12,
                                height: 12,
                                decoration: BoxDecoration(
                                  color: _getIncomeTypeColor(data.type),
                                  shape: BoxShape.circle,
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: Text(_getIncomeTypeName(data.type)),
                              ),
                              GestureDetector(
                                onTap: onAmountTap != null 
                                  ? () => onAmountTap!(data.amount, _getIncomeTypeName(data.type))
                                  : null,
                                child: Text(
                                  '$percentage% (${CurrencyFormatter.formatCompactAmount(data.amount)})',
                                  style: TextStyle(
                                    decoration: onAmountTap != null ? TextDecoration.underline : null,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        );
                      }),
                      ],
                    ),
                  ),
                );
            }
            
            // Detailed income type cards
            final data = incomeData[index - 1];
              final percentage = totalIncome > 0 
                ? (data.amount / totalIncome * 100).toStringAsFixed(1)
                  : '0.0';

              return Card(
              elevation: 1,
              margin: const EdgeInsets.only(bottom: 16.0),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                        Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: _getIncomeTypeColor(data.type).withAlpha(30),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Icon(
                            _getIncomeTypeIcon(data.type),
                            color: _getIncomeTypeColor(data.type),
                            size: 24,
                          ),
                        ),
                        const SizedBox(width: 16),
                          Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                _getIncomeTypeName(data.type),
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                  fontSize: 18,
                            ),
                          ),
                          Text(
                                '$percentage% of total income',
                            style: TextStyle(
                                  color: Colors.grey[600],
                                  fontSize: 14,
                            ),
                          ),
                        ],
                      ),
                        ),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                            GestureDetector(
                              onTap: onAmountTap != null 
                                ? () => onAmountTap!(data.amount, _getIncomeTypeName(data.type))
                                : null,
                              child: Text(
                                CurrencyFormatter.formatCompactAmount(data.amount),
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 18,
                                  color: _getIncomeTypeColor(data.type),
                                  decoration: onAmountTap != null ? TextDecoration.underline : null,
                                ),
                              ),
                            ),
                          Text(
                              '${data.transactionCount} transactions',
                            style: TextStyle(
                                color: Colors.grey[600],
                                fontSize: 14,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        _buildInfoItem(
                          'Avg. Transaction',
                          CurrencyFormatter.formatCompactAmount(data.averageAmount),
                          amount: data.averageAmount,
                          title: 'Average ${_getIncomeTypeName(data.type)} Transaction',
                        ),
                        _buildInfoItem(
                          'First Payment',
                          data.payments.isNotEmpty 
                              ? _formatDate(data.payments.map((p) => p.paymentDate).reduce((a, b) => a.isBefore(b) ? a : b))
                              : 'N/A',
                        ),
                        _buildInfoItem(
                          'Last Payment',
                          data.payments.isNotEmpty 
                              ? _formatDate(data.payments.map((p) => p.paymentDate).reduce((a, b) => a.isAfter(b) ? a : b))
                              : 'N/A',
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    LinearProgressIndicator(
                      value: totalIncome > 0 ? data.amount / totalIncome : 0,
                      backgroundColor: Colors.grey.shade200,
                      color: _getIncomeTypeColor(data.type),
                      minHeight: 8,
                      borderRadius: BorderRadius.circular(4),
                      ),
                    ],
                  ),
                ),
              );
          },
        );
      },
    );
  }
  
  Widget _buildInfoItem(String label, String value, {double? amount, String? title}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
          ),
        ),
        const SizedBox(height: 4),
        if (amount != null && onAmountTap != null)
          GestureDetector(
            onTap: () => onAmountTap!(amount, title ?? label),
            child: Text(
              value,
              style: const TextStyle(
                fontWeight: FontWeight.w500,
                decoration: TextDecoration.underline,
              ),
            ),
          )
        else
          Text(
            value,
            style: const TextStyle(
              fontWeight: FontWeight.w500,
            ),
          ),
      ],
    );
  }
  
  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}

class IncomeData {
  final IncomeType type;
  final double amount;
  final int transactionCount;
  final double averageAmount;
  final List<Payment> payments;
  
  IncomeData({
    required this.type,
    required this.amount,
    required this.transactionCount,
    required this.averageAmount,
    required this.payments,
  });
} 