-- Create a view for properties with room counts
CREATE OR REPLACE VIEW public.properties_with_stats AS
SELECT 
    p.*,
    COUNT(r.id) AS room_count,
    SUM(CASE WHEN r.occupancy_status = 'vacant' THEN 1 ELSE 0 END) AS vacant_rooms,
    SUM(CASE WHEN r.occupancy_status = 'occupied' THEN 1 ELSE 0 END) AS occupied_rooms,
    SUM(CASE WHEN r.occupancy_status = 'reserved' THEN 1 ELSE 0 END) AS reserved_rooms,
    SUM(CASE WHEN r.occupancy_status = 'maintenance' THEN 1 ELSE 0 END) AS maintenance_rooms,
    COALESCE(SUM(r.rental_price), 0) AS total_potential_income,
    COALESCE(SUM(CASE WHEN r.occupancy_status = 'occupied' THEN r.rental_price ELSE 0 END), 0) AS current_income
FROM 
    public.properties p
LEFT JOIN 
    public.rooms r ON p.id = r.property_id
GROUP BY 
    p.id;

-- Create a view for rooms with amenities
CREATE OR REPLACE VIEW public.rooms_with_amenities AS
SELECT 
    r.*,
    p.name AS property_name,
    p.address AS property_address,
    p.city AS property_city,
    p.state AS property_state,
    p.zip_code AS property_zip_code,
    p.user_id,
    COALESCE(
        json_agg(
            json_build_object(
                'id', COALESCE(a.id::text, ra.id::text),
                'name', COALESCE(a.name, ra.custom_amenity_name),
                'is_custom', CASE WHEN a.id IS NULL THEN TRUE ELSE FALSE END
            )
        ) FILTER (WHERE a.id IS NOT NULL OR ra.custom_amenity_name IS NOT NULL),
        '[]'
    ) AS amenities
FROM 
    public.rooms r
JOIN 
    public.properties p ON r.property_id = p.id
LEFT JOIN 
    public.room_amenities ra ON r.id = ra.room_id
LEFT JOIN 
    public.amenities a ON ra.amenity_id = a.id
GROUP BY 
    r.id, p.id;

-- Add RLS policies to views
ALTER VIEW public.properties_with_stats SET (security_barrier = true);
ALTER VIEW public.rooms_with_amenities SET (security_barrier = true);

-- Create a function to get all rooms for a user
CREATE OR REPLACE FUNCTION public.get_user_rooms()
RETURNS SETOF public.rooms_with_amenities
LANGUAGE sql
SECURITY DEFINER
SET search_path = public
AS $$
    SELECT * FROM public.rooms_with_amenities
    WHERE user_id = (SELECT auth.uid());
$$;

-- Create a function to get all properties for a user
CREATE OR REPLACE FUNCTION public.get_user_properties()
RETURNS SETOF public.properties_with_stats
LANGUAGE sql
SECURITY DEFINER
SET search_path = public
AS $$
    SELECT * FROM public.properties_with_stats
    WHERE user_id = (SELECT auth.uid());
$$;

-- Create a function to update the updated_at column
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$;

-- Properties table policies
ALTER POLICY "Users can view their own properties" ON public.properties 
USING (user_id = (SELECT auth.uid()));

ALTER POLICY "Users can insert their own properties" ON public.properties 
WITH CHECK (user_id = (SELECT auth.uid()));

ALTER POLICY "Users can update their own properties" ON public.properties 
USING (user_id = (SELECT auth.uid()));

ALTER POLICY "Users can delete their own properties" ON public.properties 
USING (user_id = (SELECT auth.uid()));

-- Utility bills table policies
ALTER POLICY "Users can view utility bills for their own properties" ON public.utility_bills 
USING ((SELECT auth.uid()) = (SELECT user_id FROM public.properties WHERE id = property_id));

ALTER POLICY "Users can insert utility bills for their own properties" ON public.utility_bills 
WITH CHECK ((SELECT auth.uid()) = (SELECT user_id FROM public.properties WHERE id = property_id));

ALTER POLICY "Users can update utility bills for their own properties" ON public.utility_bills 
USING ((SELECT auth.uid()) = (SELECT user_id FROM public.properties WHERE id = property_id));

ALTER POLICY "Users can delete utility bills for their own properties" ON public.utility_bills 
USING ((SELECT auth.uid()) = (SELECT user_id FROM public.properties WHERE id = property_id));

-- Rooms table policies
ALTER POLICY "Users can view their own rooms" ON public.rooms 
USING ((SELECT auth.uid()) = (SELECT user_id FROM public.properties WHERE id = property_id));

ALTER POLICY "Users can insert rooms for their own properties" ON public.rooms 
WITH CHECK ((SELECT auth.uid()) = (SELECT user_id FROM public.properties WHERE id = property_id));

ALTER POLICY "Users can update rooms for their own properties" ON public.rooms 
USING ((SELECT auth.uid()) = (SELECT user_id FROM public.properties WHERE id = property_id));

ALTER POLICY "Users can delete rooms for their own properties" ON public.rooms 
USING ((SELECT auth.uid()) = (SELECT user_id FROM public.properties WHERE id = property_id));

-- Room amenities table policies
ALTER POLICY "Users can view amenities for their own rooms" ON public.room_amenities 
USING ((SELECT auth.uid()) = (SELECT user_id FROM public.properties WHERE id = 
        (SELECT property_id FROM public.rooms WHERE id = room_id)));

ALTER POLICY "Users can insert amenities for their own rooms" ON public.room_amenities 
WITH CHECK ((SELECT auth.uid()) = (SELECT user_id FROM public.properties WHERE id = 
        (SELECT property_id FROM public.rooms WHERE id = room_id)));

ALTER POLICY "Users can update amenities for their own rooms" ON public.room_amenities 
USING ((SELECT auth.uid()) = (SELECT user_id FROM public.properties WHERE id = 
        (SELECT property_id FROM public.rooms WHERE id = room_id)));

ALTER POLICY "Users can delete amenities for their own rooms" ON public.room_amenities 
USING ((SELECT auth.uid()) = (SELECT user_id FROM public.properties WHERE id = 
        (SELECT property_id FROM public.rooms WHERE id = room_id)));

-- Amenities table policies only
ALTER POLICY "Authenticated users can view amenities" ON public.amenities 
USING ((SELECT auth.role()) = 'authenticated');

-- Assuming you want any authenticated user to modify amenities for now
-- Later you can implement proper admin checks
ALTER POLICY "Only admins can insert amenities" ON public.amenities 
WITH CHECK ((SELECT auth.role()) = 'authenticated');

ALTER POLICY "Only admins can update amenities" ON public.amenities 
USING ((SELECT auth.role()) = 'authenticated');

ALTER POLICY "Only admins can delete amenities" ON public.amenities 
USING ((SELECT auth.role()) = 'authenticated');

-- Room types table policies
ALTER POLICY "Authenticated users can view room types" ON public.room_types 
USING ((SELECT auth.role()) = 'authenticated');

ALTER POLICY "Only admins can insert room types" ON public.room_types 
WITH CHECK ((SELECT auth.role()) = 'authenticated');

ALTER POLICY "Only admins can update room types" ON public.room_types 
USING ((SELECT auth.role()) = 'authenticated');

ALTER POLICY "Only admins can delete room types" ON public.room_types 
USING ((SELECT auth.role()) = 'authenticated');