import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import '../../models/bill/bill.dart';
import '../../models/payment/payment_model.dart';
import '../../services/service_locator.dart';
import '../../utils/currency_formatter.dart';

class RecordPaymentPage extends StatefulWidget {
  final Bill bill;

  const RecordPaymentPage({super.key, required this.bill});

  @override
  State<RecordPaymentPage> createState() => _RecordPaymentPageState();
}

class _RecordPaymentPageState extends State<RecordPaymentPage> {
  final _formKey = GlobalKey<FormState>();
  final _amountController = TextEditingController();
  final _notesController = TextEditingController();
  final _transactionCodeController = TextEditingController();
  bool _isFullPayment = true;
  late double _remainingAmount;
  late double _suggestedAmount;
  PaymentMethod _selectedPaymentMethod = PaymentMethod.cash;
  DateTime _paymentDate = DateTime.now();
  String? _generatedReceiptNumber;
  bool _isSubmitting = false;
  bool _paymentRecorded = false;
  
  // Tenant details
  String? _tenantName;
  String? _propertyName;
  String? _roomName;
  String? _phoneNumber;

  @override
  void initState() {
    super.initState();
    _remainingAmount = widget.bill.amount - (widget.bill.paidAmount ?? 0);
    _suggestedAmount = _remainingAmount;
    _amountController.text = _suggestedAmount.toStringAsFixed(2);
    _loadGeneratedReceiptNumber();
    _loadTenantDetails();
  }

  Future<void> _loadTenantDetails() async {
    try {
      if (widget.bill.tenantId != null) {
        final tenant = await serviceLocator.tenantService.getTenantById(widget.bill.tenantId!);
        if (tenant != null && mounted) {
          setState(() {
            _tenantName = "${tenant.firstName} ${tenant.lastName}";
            _phoneNumber = tenant.phoneNumber;
          });
          
          if (tenant.roomId != null) {
            final room = await serviceLocator.roomService.getRoomById(tenant.roomId!);
            if (room != null && mounted) {
              setState(() {
                _roomName = room.name;
              });
              
              final property = await serviceLocator.propertyService.getPropertyById(room.propertyId);
              if (property != null && mounted) {
                setState(() {
                  _propertyName = property.name;
                });
              }
            }
          }
        }
      }
    } catch (e) {
      // Handle error silently
    }
  }

  Future<void> _loadGeneratedReceiptNumber() async {
    try {
      // We'll just display a placeholder until the actual payment is created
      setState(() {
        _generatedReceiptNumber = "Auto-generated on save";
      });
    } catch (e) {
      // Handle error silently
    }
  }

  Future<void> _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _paymentDate,
      firstDate: DateTime(2000),
      lastDate: DateTime.now().add(const Duration(days: 1)),
    );
    if (picked != null && picked != _paymentDate) {
      setState(() {
        _paymentDate = picked;
      });
    }
  }

  Future<void> _recordPayment() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isSubmitting = true;
    });

    try {
      final amount = double.parse(_amountController.text);
      
      // Check if bill is already fully paid
      if (_remainingAmount <= 0) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('This bill is already fully paid. Cannot record duplicate payment.'),
              backgroundColor: Colors.orange,
            ),
          );
        }
        return;
      }

      // Create payment
      final payment = Payment(
        tenantId: widget.bill.tenantId,
        billIds: [widget.bill.id],
        amount: amount,
        method: _selectedPaymentMethod,
        paymentDate: _paymentDate,
        notes: _notesController.text.trim().isEmpty ? null : _notesController.text.trim(),
        receiptReference: _selectedPaymentMethod == PaymentMethod.mobileMoney 
            ? _transactionCodeController.text.trim()
            : null,
      );

      // Record the payment
      await serviceLocator.paymentService.createPayment(payment);
      
      // Update the remaining amount and payment status
      setState(() {
        _remainingAmount -= amount;
        _paymentRecorded = true;
      });

      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              _remainingAmount <= 0 
                  ? 'Full payment recorded successfully!' 
                  : 'Partial payment recorded successfully!',
            ),
            backgroundColor: Colors.green,
          ),
        );
      }

    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error recording payment: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isSubmitting = false;
      });
    }
  }

  @override
  void dispose() {
    _amountController.dispose();
    _notesController.dispose();
    _transactionCodeController.dispose();
    super.dispose();
  }


  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Record Payment')),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Tenant info card
              if (_tenantName != null)
                Card(
                  margin: const EdgeInsets.only(bottom: 16.0),
                  elevation: 0,
                  color: Colors.grey[50],
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Tenant: $_tenantName',
                          style: const TextStyle(
                            fontSize: 16, 
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        const SizedBox(height: 8),
                        if (_roomName != null)
                          Row(
                            children: [
                              Icon(Icons.home, size: 14, color: Colors.grey[600]),
                              const SizedBox(width: 4),
                              Text(
                                'House: $_roomName',
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Colors.grey[800],
                                ),
                              ),
                            ],
                          ),
                        if (_propertyName != null)
                          Padding(
                            padding: const EdgeInsets.only(top: 4.0),
                            child: Row(
                              children: [
                                Icon(Icons.location_on, size: 14, color: Colors.grey[600]),
                                const SizedBox(width: 4),
                                Text(
                                  'Property: $_propertyName',
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: Colors.grey[800],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        if (_phoneNumber != null && _phoneNumber!.isNotEmpty)
                          Padding(
                            padding: const EdgeInsets.only(top: 4.0),
                            child: Row(
                              children: [
                                Icon(Icons.phone, size: 14, color: Colors.grey[600]),
                                const SizedBox(width: 4),
                                Text(
                                  _phoneNumber!,
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: Colors.grey[800],
                                  ),
                                ),
                              ],
                            ),
                          ),
                      ],
                    ),
                  ),
                ),
              
              // Bill details card
              Card(
                margin: const EdgeInsets.only(bottom: 24.0),
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        widget.bill.title,
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        widget.bill.description,
                        style: Theme.of(context).textTheme.bodyMedium,
                      ),
                      const SizedBox(height: 16),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Total Amount',
                                style: Theme.of(context).textTheme.bodySmall,
                              ),
                              Text(
                                CurrencyFormatter.formatCurrency(widget.bill.amount),
                                style: Theme.of(context).textTheme.titleMedium,
                              ),
                            ],
                          ),
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.end,
                            children: [
                              Text(
                                'Due Date',
                                style: Theme.of(context).textTheme.bodySmall,
                              ),
                              Text(
                                DateFormat.yMMMd().format(widget.bill.dueDate),
                                style: Theme.of(
                                  context,
                                ).textTheme.titleMedium?.copyWith(
                                  color:
                                      widget.bill.isOverdue()
                                          ? Colors.red
                                          : null,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                      if (widget.bill.paidAmount != null &&
                          widget.bill.paidAmount! > 0)
                        Padding(
                          padding: const EdgeInsets.only(top: 16.0),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'Already Paid',
                                    style:
                                        Theme.of(context).textTheme.bodySmall,
                                  ),
                                  Text(
                                    CurrencyFormatter.formatCurrency(
                                      widget.bill.paidAmount!,
                                    ),
                                    style: Theme.of(context)
                                        .textTheme
                                        .titleMedium
                                        ?.copyWith(color: Colors.green),
                                  ),
                                ],
                              ),
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.end,
                                children: [
                                  Text(
                                    'Remaining',
                                    style:
                                        Theme.of(context).textTheme.bodySmall,
                                  ),
                                  Text(
                                    CurrencyFormatter.formatCurrency(_remainingAmount),
                                    style: Theme.of(context)
                                        .textTheme
                                        .titleMedium
                                        ?.copyWith(fontWeight: FontWeight.bold),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                    ],
                  ),
                ),
              ),

              // Payment options
              Text(
                'Payment Details',
                style: Theme.of(context).textTheme.titleMedium,
              ),
              const SizedBox(height: 16),

              // Payment method
              DropdownButtonFormField<PaymentMethod>(
                decoration: const InputDecoration(
                  labelText: 'Payment Method',
                  border: OutlineInputBorder(),
                ),
                value: _selectedPaymentMethod,
                items: PaymentMethod.values.map((method) {
                  return DropdownMenuItem<PaymentMethod>(
                    value: method,
                    child: Text(_getPaymentMethodName(method)),
                  );
                }).toList(),
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _selectedPaymentMethod = value;
                    });
                  }
                },
              ),

              // Payment date
              const SizedBox(height: 16),
              InkWell(
                onTap: () => _selectDate(context),
                child: InputDecorator(
                  decoration: const InputDecoration(
                    labelText: 'Payment Date',
                    border: OutlineInputBorder(),
                    suffixIcon: Icon(Icons.calendar_today),
                  ),
                  child: Text(
                    '${_paymentDate.day}/${_paymentDate.month}/${_paymentDate.year}',
                  ),
                ),
              ),

              // Full or partial payment selector
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: RadioListTile<bool>(
                      title: const Text('Full Payment'),
                      value: true,
                      groupValue: _isFullPayment,
                      onChanged: (value) {
                        setState(() {
                          _isFullPayment = value!;
                          if (_isFullPayment) {
                            _amountController.text = _remainingAmount
                                .toStringAsFixed(2);
                          }
                        });
                      },
                    ),
                  ),
                  Expanded(
                    child: RadioListTile<bool>(
                      title: const Text('Partial Payment'),
                      value: false,
                      groupValue: _isFullPayment,
                      onChanged: (value) {
                        setState(() {
                          _isFullPayment = value!;
                        });
                      },
                    ),
                  ),
                ],
              ),

              // Amount field
              const SizedBox(height: 16),
              TextFormField(
                controller: _amountController,
                decoration: InputDecoration(
                  labelText: 'Payment Amount',
                  prefixText: '${CurrencyFormatter.getCurrencySymbol()} ',
                  border: const OutlineInputBorder(),
                  enabled: !_isFullPayment,
                ),
                keyboardType: const TextInputType.numberWithOptions(
                  decimal: true,
                ),
                inputFormatters: [
                  FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d{0,2}')),
                ],
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter an amount';
                  }

                  final amount = double.tryParse(value);
                  if (amount == null) {
                    return 'Please enter a valid amount';
                  }

                  if (amount <= 0) {
                    return 'Amount must be greater than zero';
                  }

                  if (amount > _remainingAmount) {
                    return 'Amount cannot exceed the remaining balance';
                  }

                  return null;
                },
              ),

              // Receipt number field
              const SizedBox(height: 16),
              InputDecorator(
                decoration: InputDecoration(
                  labelText: 'Receipt Number',
                  border: const OutlineInputBorder(),
                  contentPadding: const EdgeInsets.symmetric(horizontal: 10, vertical: 8),
                ),
                child: Row(
                  children: [
                    Icon(Icons.receipt, color: Colors.grey[600], size: 18),
                    const SizedBox(width: 8),
                    Text(
                      _generatedReceiptNumber ?? "Loading...",
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey[800],
                      ),
                    ),
                  ],
                ),
              ),
              
              // Transaction code field (visible only for mobile money)
              if (_selectedPaymentMethod == PaymentMethod.mobileMoney)
                Padding(
                  padding: const EdgeInsets.only(top: 16.0),
                  child: TextFormField(
                    controller: _transactionCodeController,
                    decoration: const InputDecoration(
                      labelText: 'Transaction Code',
                      hintText: 'Enter mobile money transaction code',
                      border: OutlineInputBorder(),
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Transaction code is required for mobile money payments';
                      }
                      return null;
                    },
                  ),
                ),

              // Notes field
              const SizedBox(height: 16),
              TextFormField(
                controller: _notesController,
                decoration: const InputDecoration(
                  labelText: 'Notes (optional)',
                  hintText: 'Add any relevant payment details',
                  border: OutlineInputBorder(),
                ),
                maxLines: 3,
              ),

              // Submit button
              const SizedBox(height: 32),
              SizedBox(
                width: double.infinity,
                height: 50,
                child: ElevatedButton(
                  onPressed: _isSubmitting
                      ? null
                      : _paymentRecorded
                          ? () {
                              Navigator.of(context).pushNamed('/payment-history');
                            }
                          : _recordPayment,
                  child: Text(
                    _paymentRecorded ? 'Go to Payment History' : 'Record Payment',
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
  
  String _getPaymentMethodName(PaymentMethod method) {
    switch (method) {
      case PaymentMethod.cash:
        return 'Cash';
      case PaymentMethod.bankTransfer:
        return 'Bank Transfer';
      case PaymentMethod.mobileMoney:
        return 'Mobile Money';
      case PaymentMethod.check:
        return 'Check';
      case PaymentMethod.other:
        return 'Other';
    }
  }
}
