import 'package:uuid/uuid.dart';

/// Enum for different types of community notices
enum NoticeType {
  general,
  maintenance,
  emergency,
  event,
  policy;

  String get displayName {
    switch (this) {
      case NoticeType.general:
        return 'General';
      case NoticeType.maintenance:
        return 'Maintenance';
      case NoticeType.emergency:
        return 'Emergency';
      case NoticeType.event:
        return 'Event';
      case NoticeType.policy:
        return 'Policy';
    }
  }
}

/// Enum for notice priority levels
enum NoticePriority {
  low,
  medium,
  high,
  urgent;

  String get displayName {
    switch (this) {
      case NoticePriority.low:
        return 'Low';
      case NoticePriority.medium:
        return 'Medium';
      case NoticePriority.high:
        return 'High';
      case NoticePriority.urgent:
        return 'Urgent';
    }
  }
}

/// Extension to convert NoticeType enum to string and vice versa
extension NoticeTypeExtension on NoticeType {
  String get name {
    switch (this) {
      case NoticeType.general:
        return 'general';
      case NoticeType.maintenance:
        return 'maintenance';
      case NoticeType.emergency:
        return 'emergency';
      case NoticeType.event:
        return 'event';
      case NoticeType.policy:
        return 'policy';
    }
  }

  String get displayName {
    switch (this) {
      case NoticeType.general:
        return 'General';
      case NoticeType.maintenance:
        return 'Maintenance';
      case NoticeType.emergency:
        return 'Emergency';
      case NoticeType.event:
        return 'Event';
      case NoticeType.policy:
        return 'Policy';
    }
  }

  static NoticeType fromString(String value) {
    switch (value.toLowerCase()) {
      case 'general':
        return NoticeType.general;
      case 'maintenance':
        return NoticeType.maintenance;
      case 'emergency':
        return NoticeType.emergency;
      case 'event':
        return NoticeType.event;
      case 'policy':
        return NoticeType.policy;
      default:
        return NoticeType.general;
    }
  }
}

/// Extension to convert NoticePriority enum to string and vice versa
extension NoticePriorityExtension on NoticePriority {
  String get name {
    switch (this) {
      case NoticePriority.low:
        return 'low';
      case NoticePriority.medium:
        return 'medium';
      case NoticePriority.high:
        return 'high';
      case NoticePriority.urgent:
        return 'urgent';
    }
  }

  String get displayName {
    switch (this) {
      case NoticePriority.low:
        return 'Low';
      case NoticePriority.medium:
        return 'Medium';
      case NoticePriority.high:
        return 'High';
      case NoticePriority.urgent:
        return 'Urgent';
    }
  }

  static NoticePriority fromString(String value) {
    switch (value.toLowerCase()) {
      case 'low':
        return NoticePriority.low;
      case 'medium':
        return NoticePriority.medium;
      case 'high':
        return NoticePriority.high;
      case 'urgent':
        return NoticePriority.urgent;
      default:
        return NoticePriority.medium;
    }
  }
}

/// Model class for community notices
class CommunityNotice {
  final String id;
  final String title;
  final String content;
  final NoticeType type;
  final NoticePriority priority;
  final String propertyId;
  final String authorId;
  final String authorName;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final DateTime? expiresAt;
  final bool isActive;
  final List<String>? attachmentUrls;
  final Map<String, dynamic>? metadata;
  
  // Additional fields from the view
  final String? propertyName;
  final String? propertyAddress;
  final String? propertyCity;
  final String? propertyState;

  const CommunityNotice({
    required this.id,
    required this.title,
    required this.content,
    required this.type,
    required this.priority,
    required this.propertyId,
    required this.authorId,
    required this.authorName,
    required this.createdAt,
    this.updatedAt,
    this.expiresAt,
    this.isActive = true,
    this.attachmentUrls,
    this.metadata,
    this.propertyName,
    this.propertyAddress,
    this.propertyCity,
    this.propertyState,
  });

  /// Create a new notice with generated ID
  factory CommunityNotice.create({
    required String title,
    required String content,
    required NoticeType type,
    required NoticePriority priority,
    required String propertyId,
    required String authorId,
    required String authorName,
    DateTime? expiresAt,
    List<String>? attachmentUrls,
    Map<String, dynamic>? metadata,
  }) {
    return CommunityNotice(
      id: const Uuid().v4(),
      title: title,
      content: content,
      type: type,
      priority: priority,
      propertyId: propertyId,
      authorId: authorId,
      authorName: authorName,
      createdAt: DateTime.now(),
      expiresAt: expiresAt,
      attachmentUrls: attachmentUrls,
      metadata: metadata,
    );
  }

  /// Create from JSON (from database)
  factory CommunityNotice.fromJson(Map<String, dynamic> json) {
    return CommunityNotice(
      id: json['id'] as String,
      title: json['title'] as String,
      content: json['content'] as String,
      type: NoticeTypeExtension.fromString(json['type'] as String),
      priority: NoticePriorityExtension.fromString(json['priority'] as String),
      propertyId: json['property_id'] as String,
      authorId: json['author_id'] as String,
      authorName: json['author_name'] as String,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: json['updated_at'] != null 
          ? DateTime.parse(json['updated_at'] as String) 
          : null,
      expiresAt: json['expires_at'] != null 
          ? DateTime.parse(json['expires_at'] as String) 
          : null,
      isActive: json['is_active'] as bool? ?? true,
      attachmentUrls: json['attachment_urls'] != null 
          ? List<String>.from(json['attachment_urls'] as List) 
          : null,
      metadata: json['metadata'] as Map<String, dynamic>?,
      propertyName: json['property_name'] as String?,
      propertyAddress: json['property_address'] as String?,
      propertyCity: json['property_city'] as String?,
      propertyState: json['property_state'] as String?,
    );
  }

  /// Convert to JSON (for database)
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'content': content,
      'type': type.name,
      'priority': priority.name,
      'property_id': propertyId,
      'author_id': authorId,
      'author_name': authorName,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'expires_at': expiresAt?.toIso8601String(),
      'is_active': isActive,
      'attachment_urls': attachmentUrls,
      'metadata': metadata,
    };
  }

  /// Create a copy with updated fields
  CommunityNotice copyWith({
    String? id,
    String? title,
    String? content,
    NoticeType? type,
    NoticePriority? priority,
    String? propertyId,
    String? authorId,
    String? authorName,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? expiresAt,
    bool? isActive,
    List<String>? attachmentUrls,
    Map<String, dynamic>? metadata,
    String? propertyName,
    String? propertyAddress,
    String? propertyCity,
    String? propertyState,
  }) {
    return CommunityNotice(
      id: id ?? this.id,
      title: title ?? this.title,
      content: content ?? this.content,
      type: type ?? this.type,
      priority: priority ?? this.priority,
      propertyId: propertyId ?? this.propertyId,
      authorId: authorId ?? this.authorId,
      authorName: authorName ?? this.authorName,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      expiresAt: expiresAt ?? this.expiresAt,
      isActive: isActive ?? this.isActive,
      attachmentUrls: attachmentUrls ?? this.attachmentUrls,
      metadata: metadata ?? this.metadata,
      propertyName: propertyName ?? this.propertyName,
      propertyAddress: propertyAddress ?? this.propertyAddress,
      propertyCity: propertyCity ?? this.propertyCity,
      propertyState: propertyState ?? this.propertyState,
    );
  }

  /// Check if the notice is expired
  bool get isExpired {
    if (expiresAt == null) return false;
    return DateTime.now().isAfter(expiresAt!);
  }

  /// Check if the notice is urgent (high or urgent priority)
  bool get isUrgent {
    return priority == NoticePriority.urgent || priority == NoticePriority.high;
  }

  /// Check if this is a combined property notice (affects multiple properties)
  bool get isCombinedPropertyNotice {
    return metadata != null &&
           metadata!.containsKey('combined_properties') &&
           metadata!['combined_properties'] is List &&
           (metadata!['combined_properties'] as List).isNotEmpty;
  }

  /// Get all property IDs associated with this notice (including the primary property)
  List<String> get allPropertyIds {
    if (!isCombinedPropertyNotice) {
      return [propertyId];
    }

    final combinedProperties = metadata!['combined_properties'] as List;
    final allIds = <String>[propertyId]; // Start with primary property

    for (final prop in combinedProperties) {
      if (prop is Map<String, dynamic> && prop.containsKey('id')) {
        final id = prop['id'] as String;
        if (!allIds.contains(id)) {
          allIds.add(id);
        }
      }
    }

    return allIds;
  }

  /// Get all property names associated with this notice
  List<String> get allPropertyNames {
    if (!isCombinedPropertyNotice) {
      return [propertyName ?? 'Unknown Property'];
    }

    final combinedProperties = metadata!['combined_properties'] as List;
    final allNames = <String>[propertyName ?? 'Unknown Property']; // Start with primary property

    for (final prop in combinedProperties) {
      if (prop is Map<String, dynamic> && prop.containsKey('name')) {
        final name = prop['name'] as String;
        if (!allNames.contains(name)) {
          allNames.add(name);
        }
      }
    }

    return allNames;
  }

  /// Get the count of properties this notice affects
  int get propertyCount {
    return allPropertyIds.length;
  }

  /// Create a combined property notice with multiple properties
  factory CommunityNotice.createCombined({
    required String title,
    required String content,
    required NoticeType type,
    required NoticePriority priority,
    required String primaryPropertyId,
    required List<Map<String, dynamic>> additionalProperties, // List of {id, name} maps
    required String authorId,
    required String authorName,
    DateTime? expiresAt,
    List<String>? attachmentUrls,
    Map<String, dynamic>? additionalMetadata,
  }) {
    final metadata = <String, dynamic>{
      'combined_properties': additionalProperties,
      'is_combined_notice': true,
      'created_as_combined': true,
      ...?additionalMetadata,
    };

    return CommunityNotice(
      id: const Uuid().v4(),
      title: title,
      content: content,
      type: type,
      priority: priority,
      propertyId: primaryPropertyId,
      authorId: authorId,
      authorName: authorName,
      createdAt: DateTime.now(),
      expiresAt: expiresAt,
      attachmentUrls: attachmentUrls,
      metadata: metadata,
    );
  }

  /// Convert this notice to a combined property notice by adding additional properties
  CommunityNotice toCombinedNotice({
    required List<Map<String, dynamic>> additionalProperties,
    Map<String, dynamic>? additionalMetadata,
  }) {
    final existingMetadata = metadata ?? <String, dynamic>{};
    final newMetadata = <String, dynamic>{
      ...existingMetadata,
      'combined_properties': additionalProperties,
      'is_combined_notice': true,
      'converted_to_combined': true,
      'original_property_only': false,
      ...?additionalMetadata,
    };

    return copyWith(metadata: newMetadata);
  }

  /// Get a display string for all properties affected by this notice
  String get propertyDisplayText {
    if (!isCombinedPropertyNotice) {
      return propertyName ?? 'Unknown Property';
    }

    final names = allPropertyNames;
    if (names.length <= 2) {
      return names.join(' & ');
    } else {
      return '${names.first} & ${names.length - 1} others';
    }
  }

  /// Get formatted property location
  String get propertyLocation {
    if (propertyAddress == null) return propertyName ?? 'Unknown Property';

    final parts = <String>[];
    if (propertyAddress!.isNotEmpty) parts.add(propertyAddress!);
    if (propertyCity != null && propertyCity!.isNotEmpty) parts.add(propertyCity!);
    if (propertyState != null && propertyState!.isNotEmpty) parts.add(propertyState!);

    return parts.isEmpty ? (propertyName ?? 'Unknown Property') : parts.join(', ');
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CommunityNotice && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'CommunityNotice(id: $id, title: $title, type: ${type.displayName}, priority: ${priority.displayName})';
  }
}
