import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../models/bill/bill.dart';
import '../../models/tenant/tenant.dart';
import '../../services/service_locator.dart';
import '../../utils/currency_formatter.dart';
import 'record_payment_page.dart';

class SelectBillPage extends StatefulWidget {
  final String tenantId;

  const SelectBillPage({super.key, required this.tenantId});

  @override
  State<SelectBillPage> createState() => _SelectBillPageState();
}

class _SelectBillPageState extends State<SelectBillPage> {
  bool _isLoading = true;
  List<Bill> _bills = [];
  Tenant? _tenant;
  String _filterStatus = 'pending'; // Default to pending bills

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Load tenant details
      final tenant = await serviceLocator.tenantService.getTenantById(
        widget.tenantId,
      );

      // Load tenant's bills
      final bills = await serviceLocator.billService.getBillsByTenant(
        widget.tenantId,
      );

      // Apply filters
      final filteredBills = _applyFilters(bills);

      if (!mounted) return;

      setState(() {
        _tenant = tenant;
        _bills = filteredBills;
        _isLoading = false;
      });
    } catch (e) {
      if (!mounted) return;

      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('Error loading bills: $e')));
      setState(() {
        _isLoading = false;
      });
    }
  }

  List<Bill> _applyFilters(List<Bill> bills) {
    if (_filterStatus == 'all') {
      return bills;
    } else if (_filterStatus == 'pending') {
      return bills.where((bill) => bill.status == BillStatus.pending).toList();
    } else if (_filterStatus == 'paid') {
      return bills.where((bill) => bill.status == BillStatus.paid).toList();
    } else if (_filterStatus == 'overdue') {
      return bills.where((bill) => bill.isOverdue()).toList();
    }
    return bills;
  }

  void _onBillSelected(Bill bill) async {
    final result = await Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => RecordPaymentPage(bill: bill)),
    );

    if (result == true && mounted) {
      // Payment was recorded successfully, return to previous screen
      Navigator.of(context).pop(true);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          _tenant != null
              ? 'Bills for ${_tenant!.firstName} ${_tenant!.lastName}'
              : 'Select Bill for Payment',
        ),
      ),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : Column(
                children: [
                  // Filter bar
                  Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Row(
                      children: [
                        Expanded(
                          child: Text(
                            'Bills',
                            style: Theme.of(context).textTheme.titleMedium,
                          ),
                        ),
                        DropdownButton<String>(
                          value: _filterStatus,
                          items: const [
                            DropdownMenuItem(value: 'all', child: Text('All')),
                            DropdownMenuItem(
                              value: 'pending',
                              child: Text('Pending'),
                            ),
                            DropdownMenuItem(
                              value: 'paid',
                              child: Text('Paid'),
                            ),
                            DropdownMenuItem(
                              value: 'overdue',
                              child: Text('Overdue'),
                            ),
                          ],
                          onChanged: (value) {
                            if (value != null) {
                              setState(() {
                                _filterStatus = value;
                              });
                              _loadData();
                            }
                          },
                        ),
                        IconButton(
                          icon: const Icon(Icons.refresh),
                          onPressed: _loadData,
                        ),
                      ],
                    ),
                  ),

                  // Bills list
                  Expanded(
                    child:
                        _bills.isEmpty
                            ? Center(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  const Icon(
                                    Icons.receipt_long,
                                    size: 64,
                                    color: Colors.grey,
                                  ),
                                  const SizedBox(height: 16),
                                  Text(
                                    'No ${_filterStatus == 'all' ? '' : _filterStatus} bills found',
                                    style: const TextStyle(fontSize: 16),
                                  ),
                                  const SizedBox(height: 24),
                                  ElevatedButton.icon(
                                    onPressed: () {
                                      Navigator.of(context).pop();
                                    },
                                    icon: const Icon(Icons.arrow_back),
                                    label: const Text('Go Back'),
                                  ),
                                ],
                              ),
                            )
                            : ListView.builder(
                              itemCount: _bills.length,
                              padding: const EdgeInsets.symmetric(
                                horizontal: 16,
                              ),
                              itemBuilder: (context, index) {
                                final bill = _bills[index];
                                return _buildBillCard(bill);
                              },
                            ),
                  ),
                ],
              ),
    );
  }

  Widget _buildBillCard(Bill bill) {
    final isOverdue = bill.isOverdue();

    Color statusColor;
    switch (bill.status) {
      case BillStatus.paid:
        statusColor = Colors.green;
        break;
      case BillStatus.pending:
        statusColor = isOverdue ? Colors.red : Colors.orange;
        break;
      case BillStatus.overdue:
        statusColor = Colors.red;
        break;
    }

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: InkWell(
        onTap: () => _onBillSelected(bill),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Text(
                      bill.title,
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: statusColor.withAlpha(51),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      isOverdue && bill.status != BillStatus.paid
                          ? 'OVERDUE'
                          : bill.status.name.toUpperCase(),
                      style: TextStyle(
                        color: statusColor,
                        fontWeight: FontWeight.bold,
                        fontSize: 12,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Text(
                bill.description,
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 14,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Amount',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey,
                        ),
                      ),
                      Text(
                        CurrencyFormatter.formatCurrency(bill.amount),
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                    ],
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      const Text(
                        'Due Date',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey,
                        ),
                      ),
                      Text(
                        DateFormat.yMMMd().format(bill.dueDate),
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                          color: isOverdue && bill.status != BillStatus.paid
                              ? Colors.red
                              : null,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              if (bill.paidAmount != null && bill.paidAmount! > 0)
                Padding(
                  padding: const EdgeInsets.only(top: 16.0),
                  child: Row(
                    children: [
                      const Text(
                        'Partially paid: ',
                        style: TextStyle(fontSize: 14),
                      ),
                      Text(
                        CurrencyFormatter.formatCurrency(bill.paidAmount!),
                        style: const TextStyle(
                          color: Colors.green,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        ' of ${CurrencyFormatter.formatCurrency(bill.amount)}',
                        style: const TextStyle(fontSize: 14),
                      ),
                    ],
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }
}
