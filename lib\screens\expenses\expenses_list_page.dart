import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../models/expense/expense_model.dart';
import '../../models/expense/expense_category_model.dart';
import '../../models/property/property_model.dart';
import '../../services/expense/expense_service.dart';
import '../../services/expense/expense_category_service.dart';
import '../../services/property/property_service.dart';
import '../../services/service_locator.dart';
import '../../utils/currency_formatter.dart';
import '../../widgets/app_loading_indicator.dart';
import '../../widgets/common/app_error_widget.dart';

class ExpensesListPage extends StatefulWidget {
  const ExpensesListPage({super.key});

  @override
  State<ExpensesListPage> createState() => _ExpensesListPageState();
}

class _ExpensesListPageState extends State<ExpensesListPage> {
  final ExpenseService _expenseService = serviceLocator.expenseService;
  final ExpenseCategoryService _categoryService = serviceLocator.expenseCategoryService;
  final PropertyService _propertyService = serviceLocator.propertyService;
  
  Future<List<ExpenseModel>>? _expensesFuture;
  String? _selectedCategoryId;
  String? _selectedPropertyId;
  DateTime? _startDate;
  DateTime? _endDate;
  String _searchQuery = '';

  List<ExpenseCategoryModel> _categories = [];
  List<Property> _properties = [];

  final TextEditingController _searchController = TextEditingController();

  // Pagination
  static const int _itemsPerPage = 12;
  int _currentPage = 1;
  
  @override
  void initState() {
    super.initState();
    _loadInitialData();
  }
  
  Future<void> _loadInitialData() async {
    try {
      final categoriesFuture = _categoryService.getCategories(includeDefault: true);
      final propertiesFuture = _propertyService.getAllProperties();
      
      final results = await Future.wait([
        categoriesFuture,
        propertiesFuture,
      ]);
      
      if (!mounted) return;
      
      setState(() {
        _categories = results[0] as List<ExpenseCategoryModel>;
        _properties = results[1] as List<Property>;
        _loadExpenses();
      });
    } catch (e) {
      if (!mounted) return;
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error loading data: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }
  
  void _loadExpenses() {
    _expensesFuture = _expenseService.getExpenses(
      categoryId: _selectedCategoryId,
      propertyId: _selectedPropertyId,
      startDate: _startDate,
      endDate: _endDate,
    );
  }
  
  void _applyFilters() {
    setState(() {
      _currentPage = 1; // Reset to first page when applying filters
      _loadExpenses();
    });
  }

  void _resetFilters() {
    setState(() {
      _selectedCategoryId = null;
      _selectedPropertyId = null;
      _startDate = null;
      _endDate = null;
      _searchQuery = '';
      _searchController.clear();
      _currentPage = 1; // Reset to first page when resetting filters
      _loadExpenses();
    });
  }

  List<ExpenseModel> _filterExpenses(List<ExpenseModel> expenses) {
    if (_searchQuery.isEmpty) return expenses;

    final query = _searchQuery.toLowerCase();
    return expenses.where((expense) {
      return expense.title.toLowerCase().contains(query) ||
             expense.formattedExpenseNumber.toLowerCase().contains(query) ||
             (expense.propertyName?.toLowerCase().contains(query) ?? false) ||
             (expense.roomName?.toLowerCase().contains(query) ?? false) ||
             (expense.vendorName?.toLowerCase().contains(query) ?? false) ||
             expense.description.toLowerCase().contains(query);
    }).toList();
  }

  // Get paginated expenses
  List<ExpenseModel> _getPaginatedExpenses(List<ExpenseModel> expenses) {
    final startIndex = (_currentPage - 1) * _itemsPerPage;
    final endIndex = startIndex + _itemsPerPage;

    if (startIndex >= expenses.length) return [];

    return expenses.sublist(
      startIndex,
      endIndex > expenses.length ? expenses.length : endIndex,
    );
  }

  int _getTotalPages(List<ExpenseModel> expenses) {
    return (expenses.length / _itemsPerPage).ceil();
  }

  Widget _buildPaginationControls(int totalPages) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          top: BorderSide(color: Colors.grey.shade200),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            'Page $_currentPage of $totalPages',
            style: TextStyle(
              color: Colors.grey.shade700,
              fontWeight: FontWeight.w500,
            ),
          ),
          Row(
            children: [
              // Previous button
              IconButton(
                onPressed: _currentPage > 1 ? () {
                  setState(() {
                    _currentPage--;
                  });
                } : null,
                icon: const Icon(Icons.chevron_left),
                tooltip: 'Previous page',
              ),

              // Page numbers
              ...List.generate(
                totalPages > 5 ? 5 : totalPages,
                (index) {
                  int pageNumber;
                  if (totalPages <= 5) {
                    pageNumber = index + 1;
                  } else {
                    // Show pages around current page
                    int start = _currentPage - 2;
                    if (start < 1) start = 1;
                    if (start + 4 > totalPages) start = totalPages - 4;
                    pageNumber = start + index;
                  }

                  return Container(
                    margin: const EdgeInsets.symmetric(horizontal: 2),
                    child: TextButton(
                      onPressed: pageNumber != _currentPage ? () {
                        setState(() {
                          _currentPage = pageNumber;
                        });
                      } : null,
                      style: TextButton.styleFrom(
                        backgroundColor: pageNumber == _currentPage
                            ? Theme.of(context).primaryColor
                            : null,
                        foregroundColor: pageNumber == _currentPage
                            ? Colors.white
                            : Theme.of(context).primaryColor,
                        minimumSize: const Size(40, 40),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: Text('$pageNumber'),
                    ),
                  );
                },
              ),

              // Next button
              IconButton(
                onPressed: _currentPage < totalPages ? () {
                  setState(() {
                    _currentPage++;
                  });
                } : null,
                icon: const Icon(Icons.chevron_right),
                tooltip: 'Next page',
              ),
            ],
          ),
        ],
      ),
    );
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Expenses'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Theme.of(context).colorScheme.onPrimary,
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: () {
              _showFilterBottomSheet(context);
            },
          ),
        ],
      ),
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Search expenses...',
                prefixIcon: const Icon(Icons.search),
                suffixIcon: _searchQuery.isNotEmpty
                    ? IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () {
                          setState(() {
                            _searchQuery = '';
                            _searchController.clear();
                            _currentPage = 1; // Reset to first page when clearing search
                          });
                        },
                      )
                    : null,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
                filled: true,
                fillColor: Colors.grey[50],
              ),
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                  _currentPage = 1; // Reset to first page when searching
                });
              },
            ),
          ),
          if (_selectedCategoryId != null || _selectedPropertyId != null || _startDate != null || _endDate != null)
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: _buildActiveFilters(),
            ),
          Expanded(
            child: RefreshIndicator(
              onRefresh: () async {
                setState(() {
                  _loadExpenses();
                });
              },
              child: FutureBuilder<List<ExpenseModel>>(
                future: _expensesFuture ?? (_expensesFuture = _expenseService.getExpenses()),
                builder: (context, snapshot) {
                  if (snapshot.connectionState == ConnectionState.waiting) {
                    return const AppLoadingIndicator();
                  } else if (snapshot.hasError) {
                    return AppErrorWidget(
                      message: snapshot.error.toString(),
                      onRetry: () {
                        setState(() {
                          _loadExpenses();
                        });
                      },
                    );
                  } else if (!snapshot.hasData || snapshot.data!.isEmpty) {
                    return _buildEmptyState();
                  }

                  final filteredExpenses = _filterExpenses(snapshot.data!);
                  final paginatedExpenses = _getPaginatedExpenses(filteredExpenses);
                  final totalPages = _getTotalPages(filteredExpenses);

                  return Column(
                    children: [
                      Expanded(
                        child: ListView.builder(
                          padding: const EdgeInsets.all(16.0),
                          itemCount: paginatedExpenses.length + 1, // +1 for the header
                          itemBuilder: (context, index) {
                            if (index == 0) {
                              return Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  _buildExpenseSummary(filteredExpenses),
                                  const SizedBox(height: 24),
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                    children: [
                                      Column(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            'Showing ${paginatedExpenses.length} of ${filteredExpenses.length} expenses',
                                            style: TextStyle(
                                              color: Colors.grey[600],
                                              fontWeight: FontWeight.w500,
                                            ),
                                          ),
                                          if (totalPages > 1) ...[
                                            const SizedBox(height: 2),
                                            Text(
                                              'Page $_currentPage of $totalPages',
                                              style: TextStyle(
                                                color: Colors.grey[500],
                                                fontSize: 12,
                                              ),
                                            ),
                                          ],
                                        ],
                                      ),
                                      TextButton.icon(
                                        onPressed: _resetFilters,
                                        icon: const Icon(Icons.refresh, size: 18),
                                        label: const Text('Reset'),
                                      ),
                                    ],
                                  ),
                                  const SizedBox(height: 8),
                                ],
                              );
                            }

                            final expense = paginatedExpenses[index - 1];
                            return _buildExpenseCard(expense);
                          },
                        ),
                      ),

                      // Pagination controls
                      if (totalPages > 1)
                        _buildPaginationControls(totalPages),
                    ],
                  );
                },
              ),
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () {
          Navigator.pushNamed(context, '/expenses/add')
              .then((_) => setState(() {
                    _loadExpenses();
                  }));
        },
        icon: const Icon(Icons.add),
        label: const Text('Add Expense'),
      ),
    );
  }

  Widget _buildActiveFilters() {
    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: [
        if (_selectedCategoryId != null)
          _buildFilterChip(
            label: _categories
                .firstWhere((c) => c.id == _selectedCategoryId)
                .name,
            onDeleted: () {
              setState(() {
                _selectedCategoryId = null;
                _loadExpenses();
              });
            },
          ),
        if (_selectedPropertyId != null)
          _buildFilterChip(
            label: _properties
                .firstWhere((p) => p.id == _selectedPropertyId)
                .name,
            onDeleted: () {
              setState(() {
                _selectedPropertyId = null;
                _loadExpenses();
              });
            },
          ),
        if (_startDate != null)
          _buildFilterChip(
            label: 'From: ${DateFormat('MMM d, y').format(_startDate!)}',
            onDeleted: () {
              setState(() {
                _startDate = null;
                _loadExpenses();
              });
            },
          ),
        if (_endDate != null)
          _buildFilterChip(
            label: 'To: ${DateFormat('MMM d, y').format(_endDate!)}',
            onDeleted: () {
              setState(() {
                _endDate = null;
                _loadExpenses();
              });
            },
          ),
      ],
    );
  }

  Widget _buildFilterChip({
    required String label,
    required VoidCallback onDeleted,
  }) {
    return Chip(
      label: Text(label),
      deleteIcon: const Icon(Icons.close, size: 18),
      onDeleted: onDeleted,
    );
  }

  Widget _buildExpenseSummary(List<ExpenseModel> expenses) {
    final totalAmount = expenses.fold<double>(
        0, (sum, expense) => sum + expense.amount);
    
    final recurringExpenses = expenses.where((e) => e.isRecurring).length;
    final oneTimeExpenses = expenses.length - recurringExpenses;

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'Summary',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  CurrencyFormatter.formatCurrency(totalAmount),
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.green,
                  ),
                ),
              ],
            ),
            const Divider(height: 24),
            _buildSummaryRow(
              icon: Icons.receipt,
              label: 'Total Expenses',
              value: expenses.length.toString(),
            ),
            const SizedBox(height: 8),
            _buildSummaryRow(
              icon: Icons.repeat,
              label: 'Recurring Expenses',
              value: recurringExpenses.toString(),
              color: Colors.blue,
            ),
            const SizedBox(height: 8),
            _buildSummaryRow(
              icon: Icons.payment,
              label: 'One-time Expenses',
              value: oneTimeExpenses.toString(),
              color: Colors.orange,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryRow({
    required IconData icon,
    required String label,
    required String value,
    Color? color,
  }) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Row(
          children: [
            Icon(icon, size: 20, color: color ?? Colors.grey[600]),
            const SizedBox(width: 8),
            Text(
              label,
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 14,
              ),
            ),
          ],
        ),
        Text(
          value,
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 16,
            color: color ?? Colors.grey[800],
          ),
        ),
      ],
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.receipt_long,
            size: 80,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            _searchQuery.isNotEmpty
                ? 'No matching expenses found'
                : 'No expenses found',
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _searchQuery.isNotEmpty
                ? 'Try adjusting your search or filters'
                : 'Add your first expense to get started',
            style: TextStyle(color: Colors.grey[600]),
          ),
          const SizedBox(height: 24),
          if (_searchQuery.isEmpty)
            ElevatedButton.icon(
              onPressed: () {
                Navigator.pushNamed(context, '/expenses/add')
                    .then((_) => setState(() {
                          _loadExpenses();
                        }));
              },
              icon: const Icon(Icons.add),
              label: const Text('Add Expense'),
            ),
        ],
      ),
    );
  }

  void _showFilterBottomSheet(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return Container(
              padding: EdgeInsets.only(
                bottom: MediaQuery.of(context).viewInsets.bottom + 16,
                left: 16,
                right: 16,
                top: 16,
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text(
                        'Filter Expenses',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      IconButton(
                        icon: const Icon(Icons.close),
                        onPressed: () => Navigator.pop(context),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  DropdownButtonFormField<String>(
                    decoration: const InputDecoration(
                      labelText: 'Category',
                      border: OutlineInputBorder(),
                    ),
                    value: _selectedCategoryId,
                    items: [
                      const DropdownMenuItem(
                        value: null,
                        child: Text('All Categories'),
                      ),
                      ..._categories.map((category) => DropdownMenuItem(
                        value: category.id,
                        child: Row(
                          children: [
                            Container(
                              width: 12,
                              height: 12,
                              decoration: BoxDecoration(
                                color: category.color,
                                shape: BoxShape.circle,
                              ),
                            ),
                            const SizedBox(width: 8),
                            Text(category.name),
                          ],
                        ),
                      )),
                    ],
                    onChanged: (value) {
                      setState(() {
                        _selectedCategoryId = value;
                      });
                    },
                  ),
                  const SizedBox(height: 16),
                  DropdownButtonFormField<String>(
                    decoration: const InputDecoration(
                      labelText: 'Property',
                      border: OutlineInputBorder(),
                    ),
                    value: _selectedPropertyId,
                    items: [
                      const DropdownMenuItem(
                        value: null,
                        child: Text('All Properties'),
                      ),
                      ..._properties.map((property) => DropdownMenuItem(
                        value: property.id,
                        child: Text(property.name),
                      )),
                    ],
                    onChanged: (value) {
                      setState(() {
                        _selectedPropertyId = value;
                      });
                    },
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Expanded(
                        child: InkWell(
                          onTap: () async {
                            final date = await showDatePicker(
                              context: context,
                              initialDate: _startDate ?? DateTime.now(),
                              firstDate: DateTime(2020),
                              lastDate: DateTime(2030),
                            );
                            if (date != null) {
                              setState(() {
                                _startDate = date;
                              });
                            }
                          },
                          child: InputDecorator(
                            decoration: const InputDecoration(
                              labelText: 'Start Date',
                              border: OutlineInputBorder(),
                            ),
                            child: Text(
                              _startDate != null
                                  ? DateFormat('MMM d, y').format(_startDate!)
                                  : 'Select Date',
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: InkWell(
                          onTap: () async {
                            final date = await showDatePicker(
                              context: context,
                              initialDate: _endDate ?? DateTime.now(),
                              firstDate: DateTime(2020),
                              lastDate: DateTime(2030),
                            );
                            if (date != null) {
                              setState(() {
                                _endDate = date;
                              });
                            }
                          },
                          child: InputDecorator(
                            decoration: const InputDecoration(
                              labelText: 'End Date',
                              border: OutlineInputBorder(),
                            ),
                            child: Text(
                              _endDate != null
                                  ? DateFormat('MMM d, y').format(_endDate!)
                                  : 'Select Date',
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 24),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      TextButton(
                        onPressed: () {
                          Navigator.pop(context);
                          _resetFilters();
                        },
                        child: const Text('Reset'),
                      ),
                      const SizedBox(width: 16),
                      ElevatedButton(
                        onPressed: () {
                          Navigator.pop(context);
                          _applyFilters();
                        },
                        child: const Text('Apply'),
                      ),
                    ],
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildExpenseCard(ExpenseModel expense) {
    final dateFormat = DateFormat('MMM d, yyyy');
    
    Color categoryColor = expense.categoryColor ?? Colors.grey;
    
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: InkWell(
        onTap: () {
          Navigator.pushNamed(
            context,
            '/expenses/detail',
            arguments: expense.id,
          ).then((_) => setState(() {
                _loadExpenses();
              }));
        },
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          expense.title,
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                        ),
                        const SizedBox(height: 2),
                        Text(
                          expense.formattedExpenseNumber,
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey.shade600,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Row(
                    children: [
                      Text(
                        CurrencyFormatter.formatCurrency(expense.amount),
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      PopupMenuButton<String>(
                        icon: const Icon(Icons.more_vert, size: 20),
                        onSelected: (value) => _handleExpenseAction(value, expense),
                        itemBuilder: (context) => [
                          const PopupMenuItem(
                            value: 'edit',
                            child: Row(
                              children: [
                                Icon(Icons.edit, size: 18),
                                SizedBox(width: 8),
                                Text('Edit'),
                              ],
                            ),
                          ),
                          const PopupMenuItem(
                            value: 'delete',
                            child: Row(
                              children: [
                                Icon(Icons.delete, color: Colors.red, size: 18),
                                SizedBox(width: 8),
                                Text('Delete', style: TextStyle(color: Colors.red)),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      Container(
                        width: 12,
                        height: 12,
                        decoration: BoxDecoration(
                          color: categoryColor,
                          shape: BoxShape.circle,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Text(expense.categoryName ?? 'Uncategorized'),
                    ],
                  ),
                  Text(dateFormat.format(expense.date)),
                ],
              ),
              if (expense.propertyId != null) ...[
                const SizedBox(height: 8),
                Row(
                  children: [
                    const Icon(Icons.home, size: 14, color: Colors.grey),
                    const SizedBox(width: 8),
                    Text(
                      expense.propertyName ?? 'Unknown Property',
                      style: const TextStyle(color: Colors.grey),
                    ),
                  ],
                ),
              ],
              if (expense.vendorId != null) ...[
                const SizedBox(height: 4),
                Row(
                  children: [
                    const Icon(Icons.business, size: 14, color: Colors.grey),
                    const SizedBox(width: 8),
                    Text(
                      expense.vendorName ?? 'No vendor',
                      style: const TextStyle(color: Colors.grey),
                    ),
                  ],
                ),
              ],
              if (expense.isRecurring) ...[
                const SizedBox(height: 8),
                Row(
                  children: [
                    const Icon(Icons.repeat, size: 14, color: Colors.blue),
                    const SizedBox(width: 8),
                    Text(
                      'Recurring (${expense.frequency.toString().split('.').last})',
                      style: const TextStyle(color: Colors.blue),
                    ),
                  ],
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
  
  void _handleExpenseAction(String action, ExpenseModel expense) async {
    if (action == 'edit') {
      await Navigator.pushNamed(
        context,
        '/expenses/edit',
        arguments: expense.id,
      );
    } else if (action == 'delete') {
      _deleteExpense(expense.id!);
    }
    
    // Refresh the list after any action
    setState(() {
      _loadExpenses();
    });
  }
  
  Future<void> _deleteExpense(String id) async {
    // Capture navigator before any async operations to avoid context issues
    final scaffoldMessenger = ScaffoldMessenger.of(context);
    
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (dialogContext) => AlertDialog(
        title: const Text('Delete Expense'),
        content: const Text('Are you sure you want to delete this expense?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(dialogContext).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(dialogContext).pop(true),
            child: const Text('Delete', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );

    // If not confirmed or widget unmounted, exit early
    if (confirmed != true || !mounted) return;

    try {
      await _expenseService.deleteExpense(id);
      
      // Check if widget is still mounted after async operation
      if (!mounted) return;
      
      scaffoldMessenger.showSnackBar(
        const SnackBar(
          content: Text('Expense deleted successfully'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      // Check if widget is still mounted after async operation
      if (!mounted) return;
      
      scaffoldMessenger.showSnackBar(
        SnackBar(
          content: Text('Failed to delete expense: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }
} 