import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../models/bill/bill.dart';
import '../../services/service_locator.dart';
import 'add_bill_page.dart';
import 'bill_detail_page.dart';
import 'group_bill_create_page.dart';
import 'bill_templates_page.dart';

class BillsListPage extends StatefulWidget {
  const BillsListPage({super.key}) : super();

  @override
  State<BillsListPage> createState() => _BillsListPageState();
}

class _BillsListPageState extends State<BillsListPage> {
  List<Bill> _bills = [];
  bool _isLoading = true;
  String _filterStatus = 'all';
  String? _filterType;
  DateTime? _startDate;
  DateTime? _endDate;
  
  // Cache for tenant, room, and property details
  final Map<String, String> _tenantNames = {};
  final Map<String, String> _roomNames = {};
  final Map<String, String> _propertyNames = {};

  @override
  void initState() {
    super.initState();
    _loadBills();
  }

  Future<void> _loadBills() async {
    setState(() {
      _isLoading = true;
    });

    try {
      List<Bill> bills = await serviceLocator.billService.getAllBills();

      // Apply filters
      bills = _applyFilters(bills);
      
      // Load tenant, room, and property details for each bill
      await _loadBillDetails(bills);

      if (!mounted) return;

      setState(() {
        _bills = bills;
        _isLoading = false;
      });
    } catch (e) {
      if (!mounted) return;
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('Error loading bills: $e')));
      setState(() {
        _isLoading = false;
      });
    }
  }
  
  Future<void> _loadBillDetails(List<Bill> bills) async {
    // Load tenant, room, and property details for each bill
    for (final bill in bills) {
      if (bill.tenantId != null && !_tenantNames.containsKey(bill.tenantId)) {
        try {
          final tenant = await serviceLocator.tenantService.getTenantById(bill.tenantId!);
          if (tenant != null) {
            _tenantNames[bill.tenantId!] = '${tenant.firstName} ${tenant.lastName}';
          }
        } catch (e) {
          // Handle error silently
        }
      }
      
      if (bill.roomId != null && !_roomNames.containsKey(bill.roomId)) {
        try {
          final room = await serviceLocator.roomService.getRoomById(bill.roomId!);
          if (room != null) {
            _roomNames[bill.roomId!] = room.name;
            
            if (!_propertyNames.containsKey(room.propertyId)) {
              final property = await serviceLocator.propertyService.getPropertyById(room.propertyId);
              if (property != null) {
                _propertyNames[room.propertyId] = property.name;
              }
            }
          }
        } catch (e) {
          // Handle error silently
        }
      }
    }
  }

  List<Bill> _applyFilters(List<Bill> bills) {
    return bills.where((bill) {
      // Filter by status
      if (_filterStatus != 'all') {
        if (_filterStatus == 'overdue') {
          if (!bill.isOverdue()) return false;
        } else {
          if (bill.status.name != _filterStatus) return false;
        }
      }

      // Filter by type
      if (_filterType != null && bill.type.name != _filterType) {
        return false;
      }

      // Filter by date range
      if (_startDate != null && bill.dueDate.isBefore(_startDate!)) {
        return false;
      }
      if (_endDate != null) {
        // Add 1 day to include the end date fully
        final endDatePlusOne = _endDate!.add(const Duration(days: 1));
        if (bill.dueDate.isAfter(endDatePlusOne)) {
          return false;
        }
      }

      return true;
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Bills'),
        actions: [
          IconButton(
            icon: const Icon(Icons.auto_awesome),
            tooltip: 'Bill Templates',
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const BillTemplatesPage(),
                ),
              ).then((_) => _loadBills()); // Reload bills when returning from templates
            },
          ),
          IconButton(
            icon: const Icon(Icons.filter_list),
            tooltip: 'Filter',
            onPressed: _showFilterDialog,
          ),
        ],
      ),
      body: Column(
        children: [
          _buildFilterBar(),
          Expanded(
            child:
                _isLoading
                    ? const Center(child: CircularProgressIndicator())
                    : _bills.isEmpty
                    ? const Center(child: Text('No bills found'))
                    : _buildBillsList(),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _showAddBillOptions,
        child: const Icon(Icons.add),
      ),
    );
  }

  void _showAddBillOptions() {
    showModalBottomSheet(
      context: context,
      builder: (context) {
        return SafeArea(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                leading: const Icon(Icons.receipt),
                title: const Text('Add Individual Bill'),
                onTap: () {
                  Navigator.pop(context);
                  _navigateToAddBill();
                },
              ),
              ListTile(
                leading: const Icon(Icons.group),
                title: const Text('Create Group Bill'),
                onTap: () {
                  Navigator.pop(context);
                  _navigateToGroupBill();
                },
              ),
              ListTile(
                leading: const Icon(Icons.auto_awesome),
                title: const Text('Manage Bill Templates'),
                onTap: () {
                  Navigator.pop(context);
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const BillTemplatesPage(),
                    ),
                  ).then((_) => _loadBills());
                },
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildFilterBar() {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Card(
        child: ExpansionTile(
          title: const Text('Filters'),
          children: [
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  DropdownButtonFormField<String>(
                    decoration: const InputDecoration(labelText: 'Status'),
                    value: _filterStatus,
                    items: [
                      const DropdownMenuItem(value: 'all', child: Text('All')),
                      const DropdownMenuItem(
                        value: 'pending',
                        child: Text('Pending'),
                      ),
                      const DropdownMenuItem(
                        value: 'paid',
                        child: Text('Paid'),
                      ),
                      const DropdownMenuItem(
                        value: 'overdue',
                        child: Text('Overdue'),
                      ),
                    ],
                    onChanged: (value) {
                      if (value != null) {
                        setState(() {
                          _filterStatus = value;
                        });
                        _loadBills();
                      }
                    },
                  ),
                  const SizedBox(height: 16),
                  DropdownButtonFormField<String?>(
                    decoration: const InputDecoration(labelText: 'Bill Type'),
                    value: _filterType,
                    items: [
                      const DropdownMenuItem(value: null, child: Text('All')),
                      ...BillType.values.map(
                        (type) => DropdownMenuItem(
                          value: type.name,
                          child: Text(type.name.toUpperCase()),
                        ),
                      ),
                    ],
                    onChanged: (value) {
                      setState(() {
                        _filterType = value;
                      });
                      _loadBills();
                    },
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Expanded(
                        child: TextButton.icon(
                          icon: const Icon(Icons.date_range),
                          label: Text(
                            _startDate == null
                                ? 'Start Date'
                                : DateFormat.yMd().format(_startDate!),
                          ),
                          onPressed: () async {
                            final date = await showDatePicker(
                              context: context,
                              initialDate: _startDate ?? DateTime.now(),
                              firstDate: DateTime(2000),
                              lastDate: DateTime(2100),
                            );
                            if (date != null) {
                              setState(() {
                                _startDate = date;
                              });
                              _loadBills();
                            }
                          },
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: TextButton.icon(
                          icon: const Icon(Icons.date_range),
                          label: Text(
                            _endDate == null
                                ? 'End Date'
                                : DateFormat.yMd().format(_endDate!),
                          ),
                          onPressed: () async {
                            final date = await showDatePicker(
                              context: context,
                              initialDate: _endDate ?? DateTime.now(),
                              firstDate: DateTime(2000),
                              lastDate: DateTime(2100),
                            );
                            if (date != null) {
                              setState(() {
                                _endDate = date;
                              });
                              _loadBills();
                            }
                          },
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () {
                      setState(() {
                        _filterStatus = 'all';
                        _filterType = null;
                        _startDate = null;
                        _endDate = null;
                      });
                      _loadBills();
                    },
                    child: const Text('Clear Filters'),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBillsList() {
    return ListView.builder(
      itemCount: _bills.length,
      itemBuilder: (context, index) {
        final bill = _bills[index];
        return _buildBillCard(bill);
      },
    );
  }

  Widget _buildBillCard(Bill bill) {
    final isOverdue = bill.isOverdue();
    final formatter = NumberFormat.currency(symbol: 'KSh');

    // Get tenant, room, and property names from cache
    final tenantName = bill.tenantId != null ? _tenantNames[bill.tenantId] : null;
    final roomName = bill.roomId != null ? _roomNames[bill.roomId] : null;
    final propertyName = bill.roomId != null && _roomNames.containsKey(bill.roomId) ? 
        _propertyNames.entries.firstWhere(
          (entry) => bill.propertyId == entry.key,
          orElse: () => const MapEntry('', ''),
        ).value : null;

    Color statusColor;
    switch (bill.status) {
      case BillStatus.paid:
        statusColor = Colors.green;
        break;
      case BillStatus.pending:
        statusColor = isOverdue ? Colors.red : Colors.orange;
        break;
      case BillStatus.overdue:
        statusColor = Colors.red;
        break;
    }

    return Card(
      margin: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
      child: ListTile(
        title: Text(
          bill.title,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 4),
            if (tenantName != null)
              Text('Tenant: $tenantName',
                style: TextStyle(color: Colors.grey[800], fontSize: 13),
              ),
            if (roomName != null)
              Text('House: $roomName',
                style: TextStyle(color: Colors.grey[800], fontSize: 13),
              ),
            if (propertyName != null)
              Text('Property: $propertyName',
                style: TextStyle(color: Colors.grey[800], fontSize: 13),
              ),
            const SizedBox(height: 4),
            Text('Due: ${DateFormat.yMMMd().format(bill.dueDate)}',
              style: TextStyle(color: Colors.grey[700], fontSize: 13),
            ),
            Text('Amount: ${formatter.format(bill.amount)}',
              style: TextStyle(fontWeight: FontWeight.w500, fontSize: 13),
            ),
            if (bill.isPartiallyPaid())
              Text(
                'Paid: ${formatter.format(bill.paidAmount)} (${((bill.paidAmount! / bill.amount) * 100).toStringAsFixed(0)}%)',
                style: TextStyle(color: Colors.green[700], fontSize: 13),
              ),
          ],
        ),
        trailing: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: statusColor.withAlpha(51), // 0.2 * 255 = 51
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: statusColor),
              ),
              child: Text(
                isOverdue && bill.status != BillStatus.paid
                    ? 'OVERDUE'
                    : bill.status.name.toUpperCase(),
                style: TextStyle(
                  color: statusColor,
                  fontWeight: FontWeight.bold,
                  fontSize: 12,
                ),
              ),
            ),
            const SizedBox(height: 4),
            Text(
              bill.type.name.toUpperCase(),
              style: TextStyle(fontSize: 11, color: Colors.grey[600]),
            ),
          ],
        ),
        onTap: () async {
          final result = await Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) => BillDetailPage(billId: bill.id),
            ),
          );
          if (result == true) {
            _loadBills();
          }
        },
      ),
    );
  }

  void _navigateToAddBill() {
    Navigator.of(context)
        .push(
          MaterialPageRoute(builder: (context) => const AddBillPage()),
        )
        .then((result) {
          if (result == true) {
            _loadBills();
          }
        });
  }

  void _navigateToGroupBill() {
    Navigator.of(context)
        .push(
          MaterialPageRoute(
            builder: (context) => const GroupBillCreatePage(),
          ),
        )
        .then((result) {
          if (result != null) {
            _loadBills();
          }
        });
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('Filter Bills'),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text('Status'),
                const SizedBox(height: 8),
                DropdownButton<String>(
                  isExpanded: true,
                  value: _filterStatus,
                  items: [
                    const DropdownMenuItem(
                      value: 'all',
                      child: Text('All'),
                    ),
                    ...BillStatus.values.map((status) => DropdownMenuItem(
                          value: status.name,
                          child: Text(_getStatusDisplayName(status)),
                        )),
                  ],
                  onChanged: (value) {
                    setState(() {
                      _filterStatus = value!;
                    });
                    Navigator.pop(context);
                    _loadBills();
                  },
                ),
                const SizedBox(height: 16),
                const Text('Type'),
                const SizedBox(height: 8),
                DropdownButton<String?>(
                  isExpanded: true,
                  value: _filterType,
                  items: [
                    const DropdownMenuItem(
                      value: null,
                      child: Text('All'),
                    ),
                    ...BillType.values.map((type) => DropdownMenuItem(
                          value: type.name,
                          child: Text(_getBillTypeDisplayName(type)),
                        )),
                  ],
                  onChanged: (value) {
                    setState(() {
                      _filterType = value;
                    });
                    Navigator.pop(context);
                    _loadBills();
                  },
                ),
                const SizedBox(height: 16),
                const Text('Date Range'),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Expanded(
                      child: TextButton(
                        onPressed: () async {
                          final date = await showDatePicker(
                            context: context,
                            initialDate: _startDate ?? DateTime.now(),
                            firstDate: DateTime(2020),
                            lastDate: DateTime(2100),
                          );
                          if (date != null) {
                            setState(() {
                              _startDate = date;
                            });
                          }
                        },
                        child: Text(_startDate == null
                            ? 'Start Date'
                            : DateFormat('MMM d, y').format(_startDate!)),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: TextButton(
                        onPressed: () async {
                          final date = await showDatePicker(
                            context: context,
                            initialDate: _endDate ?? DateTime.now(),
                            firstDate: DateTime(2020),
                            lastDate: DateTime(2100),
                          );
                          if (date != null) {
                            setState(() {
                              _endDate = date;
                            });
                          }
                        },
                        child: Text(_endDate == null
                            ? 'End Date'
                            : DateFormat('MMM d, y').format(_endDate!)),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                setState(() {
                  _filterStatus = 'all';
                  _filterType = null;
                  _startDate = null;
                  _endDate = null;
                });
                Navigator.pop(context);
                _loadBills();
              },
              child: const Text('Clear Filters'),
            ),
            TextButton(
              onPressed: () {
                Navigator.pop(context);
                _loadBills();
              },
              child: const Text('Apply'),
            ),
          ],
        );
      },
    );
  }

  String _getStatusDisplayName(BillStatus status) {
    switch (status) {
      case BillStatus.paid:
        return 'Paid';
      case BillStatus.pending:
        return 'Pending';
      case BillStatus.overdue:
        return 'Overdue';
    }
  }

  String _getBillTypeDisplayName(BillType type) {
    switch (type) {
      case BillType.rent:
        return 'Rent';
      case BillType.utility:
        return 'Utility';
      case BillType.maintenance:
        return 'Maintenance';
      case BillType.service:
        return 'Service';
      case BillType.other:
        return 'Other';
    }
  }
}

// AddBillPage and BillDetailPage are implemented in separate files now
