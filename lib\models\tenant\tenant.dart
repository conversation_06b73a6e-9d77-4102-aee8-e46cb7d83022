import 'package:uuid/uuid.dart';

enum TenantStatus {
  active,
  pending,
  movedOut,
}

class Tenant {
  final String id;
  final String email;
  final String firstName;
  final String lastName;
  final String? phoneNumber;
  final DateTime? leaseStartDate;
  final DateTime? leaseEndDate;
  final TenantStatus status;
  final String? roomId;
  final String? emergencyContactName;
  final String? emergencyContactPhone;
  final String? notes;
  final String? userId;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final bool welcomeEmailSent;
  final bool passwordResetSent;

  Tenant({
    String? id,
    required this.email,
    required this.firstName,
    required this.lastName,
    this.phoneNumber,
    this.leaseStartDate,
    this.leaseEndDate,
    this.status = TenantStatus.pending,
    this.roomId,
    this.emergencyContactName,
    this.emergencyContactPhone,
    this.notes,
    this.userId,
    DateTime? createdAt,
    this.updatedAt,
    this.welcomeEmailSent = false,
    this.passwordResetSent = false,
  })  : id = id ?? const Uuid().v4(),
        createdAt = createdAt ?? DateTime.now();

  // Create a Tenant from JSON data
  factory Tenant.fromJson(Map<String, dynamic> json) {
    return Tenant(
      id: json['id'],
      email: json['email'],
      firstName: json['first_name'],
      lastName: json['last_name'],
      phoneNumber: json['phone_number'],
      leaseStartDate: json['lease_start_date'] != null
          ? DateTime.parse(json['lease_start_date'])
          : null,
      leaseEndDate: json['lease_end_date'] != null
          ? DateTime.parse(json['lease_end_date'])
          : null,
      status: _parseStatus(json['status']),
      roomId: json['room_id'],
      emergencyContactName: json['emergency_contact_name'],
      emergencyContactPhone: json['emergency_contact_phone'],
      notes: json['notes'],
      userId: json['user_id'],
      createdAt: DateTime.parse(json['created_at']),
      updatedAt:
          json['updated_at'] != null ? DateTime.parse(json['updated_at']) : null,
      welcomeEmailSent: json['welcome_email_sent'] == true,
      passwordResetSent: json['password_reset_sent'] == true,
    );
  }

  // Convert Tenant to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'email': email,
      'first_name': firstName,
      'last_name': lastName,
      'phone_number': phoneNumber,
      'lease_start_date': leaseStartDate?.toIso8601String(),
      'lease_end_date': leaseEndDate?.toIso8601String(),
      'status': status.name,
      'room_id': roomId,
      'emergency_contact_name': emergencyContactName,
      'emergency_contact_phone': emergencyContactPhone,
      'notes': notes,
      'user_id': userId,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'welcome_email_sent': welcomeEmailSent,
      'password_reset_sent': passwordResetSent,
    };
  }

  // Parse status from string
  static TenantStatus _parseStatus(String? status) {
    if (status == null) return TenantStatus.pending;
    
    switch (status) {
      case 'active':
        return TenantStatus.active;
      case 'movedOut':
        return TenantStatus.movedOut;
      case 'pending':
      default:
        return TenantStatus.pending;
    }
  }

  // Create a copy of Tenant with updated fields
  Tenant copyWith({
    String? id,
    String? email,
    String? firstName,
    String? lastName,
    String? phoneNumber,
    DateTime? leaseStartDate,
    DateTime? leaseEndDate,
    TenantStatus? status,
    String? roomId,
    String? emergencyContactName,
    String? emergencyContactPhone,
    String? notes,
    String? userId,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? welcomeEmailSent,
    bool? passwordResetSent,
  }) {
    return Tenant(
      id: id ?? this.id,
      email: email ?? this.email,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      leaseStartDate: leaseStartDate ?? this.leaseStartDate,
      leaseEndDate: leaseEndDate ?? this.leaseEndDate,
      status: status ?? this.status,
      roomId: roomId ?? this.roomId,
      emergencyContactName: emergencyContactName ?? this.emergencyContactName,
      emergencyContactPhone: emergencyContactPhone ?? this.emergencyContactPhone,
      notes: notes ?? this.notes,
      userId: userId ?? this.userId,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      welcomeEmailSent: welcomeEmailSent ?? this.welcomeEmailSent,
      passwordResetSent: passwordResetSent ?? this.passwordResetSent,
    );
  }
} 