-- This migration adds a Gas utility bill to all properties that don't already have one

-- First, create a function to check if a property already has a gas utility bill
CREATE OR REPLACE FUNCTION property_has_gas_bill(p_id UUID) 
RETURNS BOOLEAN AS $$
DECLARE
    has_gas BOOLEAN;
BEGIN
    SELECT EXISTS (
        SELECT 1 FROM public.utility_bills
        WHERE property_id = p_id 
        AND (name ILIKE '%gas%' OR name ILIKE '%natural gas%' OR name ILIKE '%lpg%')
    ) INTO has_gas;
    
    RETURN has_gas;
END;
$$ LANGUAGE plpgsql;

-- Add Gas utility bill to all properties that don't have one yet
DO $$
DECLARE
    property_rec RECORD;
BEGIN
    FOR property_rec IN SELECT id FROM public.properties
    LOOP
        -- Only add if property doesn't already have a gas bill
        IF NOT property_has_gas_bill(property_rec.id) THEN
            INSERT INTO public.utility_bills (
                property_id, name, rate, unit, notes, created_at, updated_at
            ) VALUES (
                property_rec.id, 
                'Gas', 
                0.00, 
                'per unit', 
                'Added automatically by system update', 
                NOW(), 
                NOW()
            );
        END IF;
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Clean up the temporary function
DROP FUNCTION IF EXISTS property_has_gas_bill;

-- Add a comment to the migration for documentation
COMMENT ON TABLE public.utility_bills IS 'Stores property-specific utility bill rates including water, electricity, gas and custom rates'; 