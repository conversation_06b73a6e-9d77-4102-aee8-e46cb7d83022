# Upgraded Cursor Rules for Flutter Development

This document outlines the enhanced cursor rules, incorporating a crucial mechanism for file refactoring when code files exceed a certain line limit. This ensures better maintainability in your Flutter projects without sacrificing existing features.

---

## Cursor Rules

* **1. Manages cursor style changes on user interactions.**
    This rule ensures that the cursor's appearance (e.g., pointer, text, default) dynamically adjusts based on how the user interacts with your Flutter application's UI.

* **2. Handles basic cursor behavior (e.g., pointer, text, default).**
    This covers the fundamental actions and visual cues of the cursor within your Flutter app, such as when it should appear as a pointer for clickable widgets, a text cursor for input fields, or the default arrow.

* **3. Provides clean separation of concerns: no unrelated features added.**
    We keep the cursor's responsibilities focused. Any new feature, like the file refactoring trigger, will be carefully integrated to avoid cluttering the cursor's core functionality or adding irrelevant logic.

* **4. Never add unrequested features or behavior; only implement what's explicitly required.**
    This rule emphasizes precise implementation. The file refactoring feature is explicitly requested, so it will be added, but no other unasked-for functionalities will be introduced.

* **5. Always explain every feature added, its purpose, and how it fits the current app structure.**
    For the new file refactoring mechanism, a clear explanation will be provided detailing its purpose (improving maintainability for large files) and how it integrates into your existing Flutter project structure.

* **6. Ensure maintainability: no file should exceed 1000 lines.**
    When any Flutter `.dart` file exceeds 1000 lines of code, initiate a refactoring process to split large features, widgets, or logical components into new, appropriately named files/modules. This is a critical upgrade designed to enhance readability and long-term maintenance. This process will **not** remove any existing features; instead, it will reorganize them for better structure. Always avoid creating unnecessary files; refactor only when truly distinct features or concerns can be logically separated.

* **7. Avoid creating `Fix.sql`. Update the existing file.**
    While less common in pure Flutter app development (which typically doesn't directly manage `.sql` files), if your project interacts with a backend or database layer that involves SQL scripts, this rule dictates that any necessary database updates or fixes should be incorporated directly into existing database migration or schema files, rather than creating a standalone `Fix.sql` file.

---

## Why File Refactoring is Crucial for Flutter Maintainability

Adding **Rule 6** directly addresses the challenge of managing large codebases, which is especially important in Flutter development as projects grow. When a `.dart` file exceeds 1000 lines, it can become difficult to navigate, understand, and debug.

This proactive refactoring process involves:

* **Identifying Logical Units:** Breaking down an oversized `.dart` file into smaller, more manageable components. This could mean extracting individual widgets, utility functions, state management logic, or specific features into their own `.dart` files.
* **Creating New Files/Modules:** Moving these logical units into new, dedicated `.dart` files or subdirectories with descriptive names (e.g., `widgets/my_custom_button.dart`, `utils/date_formatters.dart`, `blocs/auth_bloc.dart`). Remember, the goal is reorganization, not removal of functionality.
* **Maintaining Connectivity:** Ensuring that the newly separated files or modules are properly imported and integrated back into the main application flow using Flutter's `import` statements, so all features continue to function seamlessly.

By adhering to this rule, your Flutter codebase will remain modular, readable, and significantly easier to maintain and scale over time.