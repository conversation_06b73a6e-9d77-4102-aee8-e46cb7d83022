class RoomType {
  final String id;
  final String name;
  final String? description;
  final bool isPredefined;

  RoomType({
    required this.id,
    required this.name,
    this.description,
    required this.isPredefined,
  });

  // Create RoomType from JSON
  factory RoomType.fromJson(Map<String, dynamic> json) {
    return RoomType(
      id: json['id'],
      name: json['name'],
      description: json['description'],
      isPredefined: json['is_predefined'] ?? true,
    );
  }

  // Convert RoomType to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'is_predefined': isPredefined,
    };
  }

  @override
  String toString() {
    return name;
  }
}
