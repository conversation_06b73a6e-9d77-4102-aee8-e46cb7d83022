import 'package:flutter/material.dart';
import '../../models/property/property_model.dart';
import '../../services/service_locator.dart';

class PropertyFormPage extends StatefulWidget {
  final Property? property;

  const PropertyFormPage({super.key, this.property});

  @override
  State<PropertyFormPage> createState() => _PropertyFormPageState();
}

class _PropertyFormPageState extends State<PropertyFormPage> {
  final _formKey = GlobalKey<FormState>();
  
  // Form controllers
  final _nameController = TextEditingController();
  final _addressController = TextEditingController();
  final _cityController = TextEditingController();
  final _stateController = TextEditingController();
  final _zipCodeController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _imageUrlController = TextEditingController();
  
  // Multiple payment details
  List<PaymentDetails> _paymentDetailsList = [];
  
  // Mobile payment types
  final List<String> _mobilePaymentTypes = ['Till', 'Paybill', 'Poshi La <PERSON>iashara', 'Mobile Number'];
  
  // Utility bills
  List<UtilityBill> _utilityBills = [];
  
  bool _isLoading = false;
  bool _isEditing = false;

  @override
  void initState() {
    super.initState();
    _isEditing = widget.property != null;
    
    if (_isEditing) {
      _nameController.text = widget.property!.name;
      _addressController.text = widget.property!.address;
      _cityController.text = widget.property!.city;
      _stateController.text = widget.property!.state;
      _zipCodeController.text = widget.property!.zipCode;
      _descriptionController.text = widget.property!.description ?? '';
      _imageUrlController.text = widget.property!.imageUrl ?? '';
      
      // Initialize utility bills
      _utilityBills = List.from(widget.property!.utilityBills);
      
      // Initialize payment details if they exist
      _paymentDetailsList = List.from(widget.property!.paymentDetails);
      
      // Check if Gas utility bill exists, add if missing
      bool hasGasBill = _utilityBills.any((bill) => 
          bill.name.toLowerCase() == 'gas' || 
          bill.name.toLowerCase().contains('gas'));
      
      if (!hasGasBill) {
        _utilityBills.add(UtilityBill(name: 'Gas', rate: 0.0, unit: 'per unit'));
      }
    } else {
      // Add default utility bills for new properties
      _utilityBills = [
        UtilityBill(name: 'Water', rate: 0.0, unit: 'per unit'),
        UtilityBill(name: 'Electricity', rate: 0.0, unit: 'per kWh'),
        UtilityBill(name: 'Gas', rate: 0.0, unit: 'per unit'),
      ];
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _addressController.dispose();
    _cityController.dispose();
    _stateController.dispose();
    _zipCodeController.dispose();
    _descriptionController.dispose();
    _imageUrlController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_isEditing ? 'Edit Property' : 'Add Property'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    // Property name
                    TextFormField(
                      controller: _nameController,
                      decoration: const InputDecoration(
                        labelText: 'Property Name',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.home),
                      ),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter a property name';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 16),
                    
                    // Property address
                    TextFormField(
                      controller: _addressController,
                      decoration: const InputDecoration(
                        labelText: 'Address',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.location_on),
                      ),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter an address';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 16),
                    
                    // City and State
                    Row(
                      children: [
                        Expanded(
                          flex: 3,
                          child: TextFormField(
                            controller: _cityController,
                            decoration: const InputDecoration(
                              labelText: 'City',
                              border: OutlineInputBorder(),
                            ),
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'Please enter a city';
                              }
                              return null;
                            },
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          flex: 2,
                          child: TextFormField(
                            controller: _stateController,
                            decoration: const InputDecoration(
                              labelText: 'State',
                              border: OutlineInputBorder(),
                            ),
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'Please enter a state';
                              }
                              return null;
                            },
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    
                    // Zip Code
                    TextFormField(
                      controller: _zipCodeController,
                      decoration: const InputDecoration(
                        labelText: 'Zip Code',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.pin),
                      ),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter a zip code';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 16),
                    
                    // Description
                    TextFormField(
                      controller: _descriptionController,
                      decoration: const InputDecoration(
                        labelText: 'Description (optional)',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.description),
                      ),
                      maxLines: 3,
                    ),
                    const SizedBox(height: 16),
                    
                    // Image URL
                    TextFormField(
                      controller: _imageUrlController,
                      decoration: const InputDecoration(
                        labelText: 'Image URL (optional)',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.image),
                      ),
                    ),
                    const SizedBox(height: 24),
                    
                    // Payment Details Section
                    Card(
                      margin: const EdgeInsets.only(bottom: 24),
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                const Text(
                                  'Payment Details',
                                  style: TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                TextButton.icon(
                                  onPressed: _addPaymentDetails,
                                  icon: const Icon(Icons.add),
                                  label: const Text('Add Payment Method'),
                                ),
                              ],
                            ),
                            const SizedBox(height: 8),
                            const Text(
                              'Configure payment information for this property. You can add multiple payment methods to give tenants flexibility.',
                              style: TextStyle(color: Colors.grey),
                            ),
                            const SizedBox(height: 16),
                            ..._buildPaymentDetailsWidgets(),
                          ],
                        ),
                      ),
                    ),
                    
                    // Utility Bills Section
                    Card(
                      margin: const EdgeInsets.only(bottom: 24),
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                const Text(
                                  'Utility Bills',
                                  style: TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                TextButton.icon(
                                  onPressed: _addUtilityBill,
                                  icon: const Icon(Icons.add),
                                  label: const Text('Add Custom Bill'),
                                ),
                              ],
                            ),
                            const SizedBox(height: 8),
                            const Text(
                              'Configure utility bill rates specific to this property. These rates will be used for automatic bill calculations.',
                              style: TextStyle(color: Colors.grey),
                            ),
                            const SizedBox(height: 16),
                            ..._buildUtilityBillsWidgets(),
                          ],
                        ),
                      ),
                    ),
                    
                    // Submit button
                    ElevatedButton(
                      onPressed: _saveProperty,
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 16),
                      ),
                      child: Text(
                        _isEditing ? 'UPDATE PROPERTY' : 'ADD PROPERTY',
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                    ),
                  ],
                ),
              ),
            ),
    );
  }

  List<Widget> _buildPaymentDetailsWidgets() {
    if (_paymentDetailsList.isEmpty) {
      return [
        const Center(
          child: Padding(
            padding: EdgeInsets.symmetric(vertical: 16),
            child: Text('No payment methods configured'),
          ),
        ),
      ];
    }

    return _paymentDetailsList.asMap().entries.map((entry) {
      final index = entry.key;
      final paymentDetail = entry.value;
      
      return Column(
        children: [
          if (index > 0) const Divider(height: 32),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Row(
                      children: [
                        Icon(
                          _getPaymentMethodIcon(paymentDetail.method),
                          color: Theme.of(context).primaryColor,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          paymentDetail.method.displayName,
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                        ),
                      ],
                    ),
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        IconButton(
                          icon: const Icon(Icons.edit, color: Colors.blue),
                          onPressed: () => _editPaymentDetails(index),
                          tooltip: 'Edit payment method',
                        ),
                        IconButton(
                          icon: const Icon(Icons.delete, color: Colors.red),
                          onPressed: () {
                            setState(() {
                              _paymentDetailsList.removeAt(index);
                            });
                          },
                          tooltip: 'Delete payment method',
                        ),
                      ],
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                Text(
                  paymentDetail.formattedDetails,
                  style: const TextStyle(fontSize: 14),
                ),
                if (paymentDetail.notes != null && paymentDetail.notes!.isNotEmpty) ...[
                  const SizedBox(height: 8),
                  Text(
                    'Notes: ${paymentDetail.notes}',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey.shade600,
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                ],
              ],
            ),
          ),
        ],
      );
    }).toList();
  }

  IconData _getPaymentMethodIcon(PaymentMethod method) {
    switch (method) {
      case PaymentMethod.cash:
        return Icons.money;
      case PaymentMethod.mobilePayment:
        return Icons.phone_android;
      case PaymentMethod.bank:
        return Icons.account_balance;
    }
  }

  List<Widget> _buildUtilityBillsWidgets() {
    if (_utilityBills.isEmpty) {
      return [
        const Center(
          child: Padding(
            padding: EdgeInsets.symmetric(vertical: 16),
            child: Text('No utility bills configured'),
          ),
        ),
      ];
    }

    return _utilityBills.asMap().entries.map((entry) {
      final index = entry.key;
      final bill = entry.value;
      
      return Column(
        children: [
          if (index > 0) const Divider(height: 32),
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    TextFormField(
                      initialValue: bill.name,
                      decoration: const InputDecoration(
                        labelText: 'Bill Name',
                        border: OutlineInputBorder(),
                      ),
                      onChanged: (value) {
                        setState(() {
                          _utilityBills[index].name = value;
                        });
                      },
                    ),
                    const SizedBox(height: 12),
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Expanded(
                          child: TextFormField(
                            initialValue: bill.rate.toString(),
                            decoration: const InputDecoration(
                              labelText: 'Rate',
                              border: OutlineInputBorder(),
                              prefixIcon: Icon(Icons.attach_money),
                            ),
                            keyboardType: TextInputType.number,
                            onChanged: (value) {
                              setState(() {
                                _utilityBills[index].rate = double.tryParse(value) ?? 0.0;
                              });
                            },
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: TextFormField(
                            initialValue: bill.unit,
                            decoration: const InputDecoration(
                              labelText: 'Unit',
                              border: OutlineInputBorder(),
                              hintText: 'e.g., per kWh',
                            ),
                            onChanged: (value) {
                              setState(() {
                                _utilityBills[index].unit = value;
                              });
                            },
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    TextFormField(
                      initialValue: bill.notes,
                      decoration: const InputDecoration(
                        labelText: 'Notes (optional)',
                        border: OutlineInputBorder(),
                        hintText: 'Additional information about this bill',
                      ),
                      onChanged: (value) {
                        setState(() {
                          _utilityBills[index].notes = value;
                        });
                      },
                    ),
                  ],
                ),
              ),
              IconButton(
                icon: const Icon(Icons.delete),
                color: Colors.red,
                onPressed: () {
                  setState(() {
                    _utilityBills.removeAt(index);
                  });
                },
              ),
            ],
          ),
        ],
      );
    }).toList();
  }

  void _addUtilityBill() {
    showDialog(
      context: context,
      builder: (context) {
        final nameController = TextEditingController();
        final rateController = TextEditingController(text: '0.0');
        final unitController = TextEditingController();
        
        return AlertDialog(
          title: const Text('Add Utility Bill'),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextField(
                  controller: nameController,
                  decoration: const InputDecoration(
                    labelText: 'Bill Name',
                    hintText: 'e.g., Gas, Internet, Cable TV, Security',
                  ),
                ),
                const SizedBox(height: 12),
                TextField(
                  controller: rateController,
                  decoration: const InputDecoration(
                    labelText: 'Rate',
                    prefixIcon: Icon(Icons.attach_money),
                    hintText: 'Cost per unit of measurement',
                  ),
                  keyboardType: TextInputType.number,
                ),
                const SizedBox(height: 12),
                TextField(
                  controller: unitController,
                  decoration: const InputDecoration(
                    labelText: 'Unit (optional)',
                    hintText: 'e.g., per month, per unit, per kWh, per cubic meter, per therm',
                  ),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () {
                if (nameController.text.isNotEmpty) {
                  setState(() {
                    _utilityBills.add(
                      UtilityBill(
                        name: nameController.text,
                        rate: double.tryParse(rateController.text) ?? 0.0,
                        unit: unitController.text.isNotEmpty ? unitController.text : null,
                      ),
                    );
                  });
                  Navigator.pop(context);
                }
              },
              child: const Text('Add'),
            ),
          ],
        );
      },
    );
  }

  void _addPaymentDetails() {
    showDialog(
      context: context,
      builder: (context) {
        PaymentMethod selectedMethod = PaymentMethod.cash;
        String selectedMobileType = 'Till';
        final paybillController = TextEditingController();
        final tillController = TextEditingController();
        final mobileController = TextEditingController();
        final bankNameController = TextEditingController();
        final accountController = TextEditingController();
        final notesController = TextEditingController();
        
        return StatefulBuilder(
          builder: (context, setDialogState) {
            return AlertDialog(
              title: const Text('Add Payment Method'),
              content: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    DropdownButtonFormField<PaymentMethod>(
                      decoration: const InputDecoration(
                        labelText: 'Payment Method',
                        border: OutlineInputBorder(),
                      ),
                      value: selectedMethod,
                      items: PaymentMethod.values.map((method) {
                        return DropdownMenuItem<PaymentMethod>(
                          value: method,
                          child: Text(method.displayName),
                        );
                      }).toList(),
                      onChanged: (value) {
                        if (value != null) {
                          setDialogState(() {
                            selectedMethod = value;
                          });
                        }
                      },
                    ),
                    const SizedBox(height: 16),
                    
                    if (selectedMethod == PaymentMethod.mobilePayment) ...[
                      DropdownButtonFormField<String>(
                        decoration: const InputDecoration(
                          labelText: 'Mobile Payment Type',
                          border: OutlineInputBorder(),
                        ),
                        value: selectedMobileType,
                        items: _mobilePaymentTypes.map((type) {
                          return DropdownMenuItem<String>(
                            value: type,
                            child: Text(type),
                          );
                        }).toList(),
                        onChanged: (value) {
                          if (value != null) {
                            setDialogState(() {
                              selectedMobileType = value;
                            });
                          }
                        },
                      ),
                      const SizedBox(height: 16),
                      
                      if (selectedMobileType == 'Till') 
                        TextField(
                          controller: tillController,
                          decoration: const InputDecoration(
                            labelText: 'Till Number',
                            hintText: 'e.g., 123456',
                          ),
                          keyboardType: TextInputType.number,
                        )
                      else if (selectedMobileType == 'Paybill') 
                        TextField(
                          controller: paybillController,
                          decoration: const InputDecoration(
                            labelText: 'Paybill Number',
                            hintText: 'e.g., 400200',
                          ),
                          keyboardType: TextInputType.number,
                        )
                      else if (selectedMobileType == 'Poshi La Biashara') 
                        TextField(
                          controller: tillController,
                          decoration: const InputDecoration(
                            labelText: 'Poshi La Biashara Number',
                            hintText: 'e.g., 654321',
                          ),
                          keyboardType: TextInputType.number,
                        )
                      else if (selectedMobileType == 'Mobile Number') 
                        TextField(
                          controller: mobileController,
                          decoration: const InputDecoration(
                            labelText: 'Mobile Number',
                            hintText: 'e.g., +************',
                          ),
                          keyboardType: TextInputType.phone,
                        ),
                    ],
                    
                    if (selectedMethod == PaymentMethod.bank) ...[
                      TextField(
                        controller: bankNameController,
                        decoration: const InputDecoration(
                          labelText: 'Bank Name',
                          hintText: 'e.g., Equity Bank',
                        ),
                      ),
                      const SizedBox(height: 16),
                      TextField(
                        controller: accountController,
                        decoration: const InputDecoration(
                          labelText: 'Account Number',
                          hintText: 'Bank account number',
                        ),
                        keyboardType: TextInputType.number,
                      ),
                    ],
                    
                    const SizedBox(height: 16),
                    TextField(
                      controller: notesController,
                      decoration: const InputDecoration(
                        labelText: 'Notes (optional)',
                        hintText: 'Additional payment instructions',
                      ),
                      maxLines: 2,
                    ),
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.pop(context);
                  },
                  child: const Text('Cancel'),
                ),
                TextButton(
                  onPressed: () {
                    // Create payment details based on form input
                    PaymentDetails newPaymentDetails;
                    
                    switch (selectedMethod) {
                      case PaymentMethod.cash:
                        newPaymentDetails = PaymentDetails(
                          method: PaymentMethod.cash,
                          notes: notesController.text.isNotEmpty ? notesController.text : null,
                        );
                        break;
                        
                      case PaymentMethod.mobilePayment:
                        String? paybillNumber;
                        String? tillNumber;
                        String? notes = notesController.text.isNotEmpty ? notesController.text : null;
                        
                        switch (selectedMobileType) {
                          case 'Paybill':
                            paybillNumber = paybillController.text.isNotEmpty ? paybillController.text : null;
                            break;
                          case 'Till':
                          case 'Poshi La Biashara':
                            tillNumber = tillController.text.isNotEmpty ? tillController.text : null;
                            break;
                          case 'Mobile Number':
                            // Store mobile number in notes
                            if (mobileController.text.isNotEmpty) {
                              notes = notes != null 
                                  ? '$notes\nMobile: ${mobileController.text}' 
                                  : 'Mobile: ${mobileController.text}';
                            }
                            break;
                        }
                        
                        newPaymentDetails = PaymentDetails(
                          method: PaymentMethod.mobilePayment,
                          paybillNumber: paybillNumber,
                          tillNumber: tillNumber,
                          notes: notes,
                        );
                        break;
                        
                      case PaymentMethod.bank:
                        newPaymentDetails = PaymentDetails(
                          method: PaymentMethod.bank,
                          bankName: bankNameController.text.isNotEmpty ? bankNameController.text : null,
                          accountNumber: accountController.text.isNotEmpty ? accountController.text : null,
                          notes: notesController.text.isNotEmpty ? notesController.text : null,
                        );
                        break;
                    }
                    
                    setState(() {
                      _paymentDetailsList.add(newPaymentDetails);
                    });
                    Navigator.pop(context);
                  },
                  child: const Text('Add'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  void _editPaymentDetails(int index) {
    final existingPayment = _paymentDetailsList[index];
    
    showDialog(
      context: context,
      builder: (context) {
        PaymentMethod selectedMethod = existingPayment.method;
        String selectedMobileType = 'Till';
        final paybillController = TextEditingController();
        final tillController = TextEditingController();
        final mobileController = TextEditingController();
        final bankNameController = TextEditingController();
        final accountController = TextEditingController();
        final notesController = TextEditingController();
        
        // Pre-populate fields with existing data
        switch (existingPayment.method) {
          case PaymentMethod.mobilePayment:
            if (existingPayment.paybillNumber != null && existingPayment.paybillNumber!.isNotEmpty) {
              selectedMobileType = 'Paybill';
              paybillController.text = existingPayment.paybillNumber!;
            } else if (existingPayment.tillNumber != null && existingPayment.tillNumber!.isNotEmpty) {
              selectedMobileType = 'Till';
              tillController.text = existingPayment.tillNumber!;
            }
            break;
          case PaymentMethod.bank:
            if (existingPayment.bankName != null) {
              bankNameController.text = existingPayment.bankName!;
            }
            if (existingPayment.accountNumber != null) {
              accountController.text = existingPayment.accountNumber!;
            }
            break;
          case PaymentMethod.cash:
            break;
        }
        
        if (existingPayment.notes != null) {
          notesController.text = existingPayment.notes!;
        }
        
        return StatefulBuilder(
          builder: (context, setDialogState) {
            return AlertDialog(
              title: const Text('Edit Payment Method'),
              content: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    DropdownButtonFormField<PaymentMethod>(
                      decoration: const InputDecoration(
                        labelText: 'Payment Method',
                        border: OutlineInputBorder(),
                      ),
                      value: selectedMethod,
                      items: PaymentMethod.values.map((method) {
                        return DropdownMenuItem<PaymentMethod>(
                          value: method,
                          child: Text(method.displayName),
                        );
                      }).toList(),
                      onChanged: (value) {
                        if (value != null) {
                          setDialogState(() {
                            selectedMethod = value;
                          });
                        }
                      },
                    ),
                    const SizedBox(height: 16),
                    
                    if (selectedMethod == PaymentMethod.mobilePayment) ...[
                      DropdownButtonFormField<String>(
                        decoration: const InputDecoration(
                          labelText: 'Mobile Payment Type',
                          border: OutlineInputBorder(),
                        ),
                        value: selectedMobileType,
                        items: _mobilePaymentTypes.map((type) {
                          return DropdownMenuItem<String>(
                            value: type,
                            child: Text(type),
                          );
                        }).toList(),
                        onChanged: (value) {
                          if (value != null) {
                            setDialogState(() {
                              selectedMobileType = value;
                            });
                          }
                        },
                      ),
                      const SizedBox(height: 16),
                      
                      if (selectedMobileType == 'Till') 
                        TextField(
                          controller: tillController,
                          decoration: const InputDecoration(
                            labelText: 'Till Number',
                            hintText: 'e.g., 123456',
                          ),
                          keyboardType: TextInputType.number,
                        )
                      else if (selectedMobileType == 'Paybill') 
                        TextField(
                          controller: paybillController,
                          decoration: const InputDecoration(
                            labelText: 'Paybill Number',
                            hintText: 'e.g., 400200',
                          ),
                          keyboardType: TextInputType.number,
                        )
                      else if (selectedMobileType == 'Poshi La Biashara') 
                        TextField(
                          controller: tillController,
                          decoration: const InputDecoration(
                            labelText: 'Poshi La Biashara Number',
                            hintText: 'e.g., 654321',
                          ),
                          keyboardType: TextInputType.number,
                        )
                      else if (selectedMobileType == 'Mobile Number') 
                        TextField(
                          controller: mobileController,
                          decoration: const InputDecoration(
                            labelText: 'Mobile Number',
                            hintText: 'e.g., +************',
                          ),
                          keyboardType: TextInputType.phone,
                        ),
                    ],
                    
                    if (selectedMethod == PaymentMethod.bank) ...[
                      TextField(
                        controller: bankNameController,
                        decoration: const InputDecoration(
                          labelText: 'Bank Name',
                          hintText: 'e.g., Equity Bank',
                        ),
                      ),
                      const SizedBox(height: 16),
                      TextField(
                        controller: accountController,
                        decoration: const InputDecoration(
                          labelText: 'Account Number',
                          hintText: 'Bank account number',
                        ),
                        keyboardType: TextInputType.number,
                      ),
                    ],
                    
                    const SizedBox(height: 16),
                    TextField(
                      controller: notesController,
                      decoration: const InputDecoration(
                        labelText: 'Notes (optional)',
                        hintText: 'Additional payment instructions',
                      ),
                      maxLines: 2,
                    ),
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.pop(context);
                  },
                  child: const Text('Cancel'),
                ),
                TextButton(
                  onPressed: () {
                    // Create updated payment details based on form input
                    PaymentDetails updatedPaymentDetails;
                    
                    switch (selectedMethod) {
                      case PaymentMethod.cash:
                        updatedPaymentDetails = PaymentDetails(
                          method: PaymentMethod.cash,
                          notes: notesController.text.isNotEmpty ? notesController.text : null,
                        );
                        break;
                        
                      case PaymentMethod.mobilePayment:
                        String? paybillNumber;
                        String? tillNumber;
                        String? notes = notesController.text.isNotEmpty ? notesController.text : null;
                        
                        switch (selectedMobileType) {
                          case 'Paybill':
                            paybillNumber = paybillController.text.isNotEmpty ? paybillController.text : null;
                            break;
                          case 'Till':
                          case 'Poshi La Biashara':
                            tillNumber = tillController.text.isNotEmpty ? tillController.text : null;
                            break;
                          case 'Mobile Number':
                            // Store mobile number in notes
                            if (mobileController.text.isNotEmpty) {
                              notes = notes != null 
                                  ? '$notes\nMobile: ${mobileController.text}' 
                                  : 'Mobile: ${mobileController.text}';
                            }
                            break;
                        }
                        
                        updatedPaymentDetails = PaymentDetails(
                          method: PaymentMethod.mobilePayment,
                          paybillNumber: paybillNumber,
                          tillNumber: tillNumber,
                          notes: notes,
                        );
                        break;
                        
                      case PaymentMethod.bank:
                        updatedPaymentDetails = PaymentDetails(
                          method: PaymentMethod.bank,
                          bankName: bankNameController.text.isNotEmpty ? bankNameController.text : null,
                          accountNumber: accountController.text.isNotEmpty ? accountController.text : null,
                          notes: notesController.text.isNotEmpty ? notesController.text : null,
                        );
                        break;
                    }
                    
                    setState(() {
                      _paymentDetailsList[index] = updatedPaymentDetails;
                    });
                    Navigator.pop(context);
                  },
                  child: const Text('Update'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  Future<void> _saveProperty() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }
    
    setState(() {
      _isLoading = true;
    });
    
    try {
      if (_isEditing) {
        // Update existing property
        final updatedProperty = await serviceLocator.propertyService.updateProperty(
          id: widget.property!.id,
          name: _nameController.text,
          address: _addressController.text,
          city: _cityController.text,
          state: _stateController.text,
          zipCode: _zipCodeController.text,
          description: _descriptionController.text.isNotEmpty ? _descriptionController.text : null,
          imageUrl: _imageUrlController.text.isNotEmpty ? _imageUrlController.text : null,
          utilityBills: _utilityBills,
          paymentDetails: _paymentDetailsList.isNotEmpty ? _paymentDetailsList : null,
        );
        
        if (mounted) {
          if (updatedProperty != null) {
            // Show success message
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Row(
                  children: [
                    const Icon(Icons.check_circle, color: Colors.white),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text('Property "${_nameController.text}" updated successfully'),
                    ),
                  ],
                ),
                backgroundColor: Colors.green,
                duration: const Duration(seconds: 3),
                behavior: SnackBarBehavior.floating,
              ),
            );
            Navigator.pop(context, true); // Return true to indicate success
          } else {
            // Show error if property couldn't be updated
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Row(
                  children: [
                    Icon(Icons.error, color: Colors.white),
                    SizedBox(width: 8),
                    Expanded(
                      child: Text('Failed to update property. The property may have been deleted.'),
                    ),
                  ],
                ),
                backgroundColor: Colors.red,
                duration: Duration(seconds: 5),
                behavior: SnackBarBehavior.floating,
              ),
            );
            setState(() {
              _isLoading = false;
            });
          }
        }
      } else {
        // Add new property
        await serviceLocator.propertyService.addProperty(
          name: _nameController.text,
          address: _addressController.text,
          city: _cityController.text,
          state: _stateController.text,
          zipCode: _zipCodeController.text,
          description: _descriptionController.text.isNotEmpty ? _descriptionController.text : null,
          imageUrl: _imageUrlController.text.isNotEmpty ? _imageUrlController.text : null,
          utilityBills: _utilityBills,
          paymentDetails: _paymentDetailsList.isNotEmpty ? _paymentDetailsList : null,
        );
        
        if (mounted) {
          // Show success message
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  const Icon(Icons.check_circle, color: Colors.white),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text('Property "${_nameController.text}" added successfully'),
                  ),
                ],
              ),
              backgroundColor: Colors.green,
              duration: const Duration(seconds: 3),
              behavior: SnackBarBehavior.floating,
            ),
          );
          Navigator.pop(context, true); // Return true to indicate success
        }
      }
    } catch (e) {
      if (mounted) {
        // Show detailed error message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.error, color: Colors.white),
                const SizedBox(width: 8),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(_isEditing ? 'Failed to update property' : 'Failed to add property'),
                      Text(
                        e.toString(),
                        style: const TextStyle(fontSize: 12),
                        overflow: TextOverflow.ellipsis,
                        maxLines: 2,
                      ),
                    ],
                  ),
                ),
              ],
            ),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 5),
            behavior: SnackBarBehavior.floating,
            action: SnackBarAction(
              label: 'DISMISS',
              textColor: Colors.white,
              onPressed: () {
                ScaffoldMessenger.of(context).hideCurrentSnackBar();
              },
            ),
          ),
        );
        setState(() {
          _isLoading = false;
        });
      }
    } finally {
      // Only set state if not already set in the catch block
      if (mounted && _isLoading) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
} 