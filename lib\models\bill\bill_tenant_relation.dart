import 'package:uuid/uuid.dart';

class BillTenantRelation {
  final String id;
  final String billId;
  final String tenantId;
  final double? splitAmount;
  final DateTime createdAt;
  final DateTime? updatedAt;

  BillTenantRelation({
    String? id,
    required this.billId,
    required this.tenantId,
    this.splitAmount,
    DateTime? createdAt,
    this.updatedAt,
  })  : id = id ?? const Uuid().v4(),
        createdAt = createdAt ?? DateTime.now();

  // Create a BillTenantRelation from JSON data
  factory BillTenantRelation.fromJson(Map<String, dynamic> json) {
    return BillTenantRelation(
      id: json['id'],
      billId: json['bill_id'],
      tenantId: json['tenant_id'],
      splitAmount: json['split_amount']?.toDouble(),
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: json['updated_at'] != null
          ? DateTime.parse(json['updated_at'])
          : null,
    );
  }

  // Convert BillTenantRelation to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'bill_id': billId,
      'tenant_id': tenantId,
      'split_amount': splitAmount,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  // Create a copy of BillTenantRelation with updated fields
  BillTenantRelation copyWith({
    String? id,
    String? billId,
    String? tenantId,
    double? splitAmount,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return BillTenantRelation(
      id: id ?? this.id,
      billId: billId ?? this.billId,
      tenantId: tenantId ?? this.tenantId,
      splitAmount: splitAmount ?? this.splitAmount,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
} 