import 'package:flutter/material.dart';
import '../../models/tenant/tenant.dart';
import '../../models/room/room_model.dart';
import '../../services/service_locator.dart';
import '../../utils/currency_formatter.dart';
import '../../widgets/app_loading_indicator.dart';
import 'vacate_room_page.dart';
import 'assign_room_page.dart';

class TenantRoomInfoPage extends StatefulWidget {
  final Tenant tenant;

  const TenantRoomInfoPage({super.key, required this.tenant});

  @override
  State<TenantRoomInfoPage> createState() => _TenantRoomInfoPageState();
}

class _TenantRoomInfoPageState extends State<TenantRoomInfoPage> {
  bool _isLoading = true;
  String? _errorMessage;
  Room? _room;
  String? _propertyName;

  @override
  void initState() {
    super.initState();
    _loadRoomDetails();
  }

  Future<void> _loadRoomDetails() async {
    // Don't load room details if tenant has moved out or doesn't have a room assigned
    if (widget.tenant.roomId == null ||
        widget.tenant.status == TenantStatus.movedOut) {
      setState(() {
        _isLoading = false;
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // Load room details
      final room = await serviceLocator.roomService.getRoomById(
        widget.tenant.roomId!,
      );

      if (room != null) {
        // Get property name
        final property = await serviceLocator.propertyService.getPropertyById(
          room.propertyId,
        );

        setState(() {
          _room = room;
          _propertyName = property?.name;
          _isLoading = false;
        });
      } else {
        setState(() {
          _errorMessage = 'Room not found';
          _isLoading = false;
        });

        // Show error notification
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Room not found. The room may have been deleted.'),
              backgroundColor: Colors.orange,
              duration: Duration(seconds: 4),
            ),
          );
        }
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Failed to load room details: $e';
        _isLoading = false;
      });

      // Show error notification
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading room details: ${e.toString()}'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 5),
            action: SnackBarAction(
              label: 'Retry',
              onPressed: _loadRoomDetails,
              textColor: Colors.white,
            ),
          ),
        );
      }
    }
  }

  Future<void> _navigateToVacateRoom() async {
    if (_room == null) return;

    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder:
            (context) =>
                VacateRoomPage(tenant: widget.tenant, roomName: _room!.name),
      ),
    );

    if (result != null) {
      // Refresh the tenant data and show success notification
      if (mounted) {
        Navigator.pop(context, result);
      }
    }
  }

  Future<void> _navigateToChangeRoom() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AssignRoomPage(tenant: widget.tenant),
      ),
    );

    if (result != null) {
      // Refresh the tenant data and show success notification
      if (mounted) {
        Navigator.pop(context, result);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('Room Details - ${widget.tenant.firstName}')),
      body:
          _isLoading
              ? const AppLoadingIndicator(message: 'Loading room details...')
              : _buildContent(),
    );
  }

  Widget _buildContent() {
    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, size: 64, color: Colors.red),
            const SizedBox(height: 16),
            Text(
              _errorMessage!,
              style: const TextStyle(color: Colors.red, fontSize: 16),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: _loadRoomDetails,
              icon: const Icon(Icons.refresh),
              label: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    // If tenant has moved out, show moved out message
    if (widget.tenant.status == TenantStatus.movedOut) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.logout, size: 80, color: Colors.grey),
            const SizedBox(height: 16),
            const Text(
              'Tenant Has Moved Out',
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            const Text(
              'This tenant has vacated their room.',
              style: TextStyle(fontSize: 16, color: Colors.grey),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            ElevatedButton.icon(
              onPressed: _navigateToChangeRoom,
              icon: const Icon(Icons.add_home),
              label: const Text('Assign New Room'),
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
              ),
            ),
          ],
        ),
      );
    }

    if (widget.tenant.roomId == null || _room == null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.meeting_room_outlined,
              size: 80,
              color: Colors.grey,
            ),
            const SizedBox(height: 16),
            const Text(
              'No Room Assigned',
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            const Text(
              'This tenant has not been assigned to any room yet.',
              style: TextStyle(fontSize: 16, color: Colors.grey),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            ElevatedButton.icon(
              onPressed: _navigateToChangeRoom,
              icon: const Icon(Icons.add_home),
              label: const Text('Assign Room'),
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
              ),
            ),
          ],
        ),
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Room details card
          Card(
            elevation: 2,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Room header with status
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          _room!.name,
                          style: const TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.green.withAlpha(50),
                          borderRadius: BorderRadius.circular(16),
                          border: Border.all(
                            color: Colors.green.withAlpha(127),
                          ),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            const Icon(
                              Icons.check_circle,
                              size: 14,
                              color: Colors.green,
                            ),
                            const SizedBox(width: 4),
                            const Text(
                              'Occupied',
                              style: TextStyle(
                                color: Colors.green,
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),

                  // Property
                  if (_propertyName != null) ...[
                    Row(
                      children: [
                        const Icon(
                          Icons.apartment,
                          size: 18,
                          color: Colors.blue,
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            'Property: $_propertyName',
                            style: const TextStyle(fontSize: 16),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                  ],

                  // Room type
                  Row(
                    children: [
                      const Icon(Icons.category, size: 18, color: Colors.blue),
                      const SizedBox(width: 8),
                      Text(
                        'Type: ${_room!.roomTypeName}',
                        style: const TextStyle(fontSize: 16),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),

                  // Rental price
                  Row(
                    children: [
                      const Icon(
                        Icons.attach_money,
                        size: 18,
                        color: Colors.blue,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'Rental Price: ${CurrencyFormatter.formatAmount(_room!.rentalPrice)}',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),

                  // Additional room details
                  if (_room!.size != null) ...[
                    const SizedBox(height: 12),
                    Row(
                      children: [
                        const Icon(
                          Icons.square_foot,
                          size: 18,
                          color: Colors.blue,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'Size: ${_room!.size} sq ft',
                          style: const TextStyle(fontSize: 16),
                        ),
                      ],
                    ),
                  ],

                  if (_room!.floor != null) ...[
                    const SizedBox(height: 12),
                    Row(
                      children: [
                        const Icon(Icons.stairs, size: 18, color: Colors.blue),
                        const SizedBox(width: 8),
                        Text(
                          'Floor: ${_room!.floor}',
                          style: const TextStyle(fontSize: 16),
                        ),
                      ],
                    ),
                  ],

                  const SizedBox(height: 12),
                  Row(
                    children: [
                      const Icon(Icons.chair, size: 18, color: Colors.blue),
                      const SizedBox(width: 8),
                      Text(
                        _room!.isFurnished ? 'Furnished' : 'Unfurnished',
                        style: const TextStyle(fontSize: 16),
                      ),
                    ],
                  ),

                  // Room description if available
                  if (_room!.description != null &&
                      _room!.description!.isNotEmpty) ...[
                    const SizedBox(height: 16),
                    const Text(
                      'Description:',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      _room!.description!,
                      style: const TextStyle(fontSize: 14),
                    ),
                  ],
                ],
              ),
            ),
          ),

          const SizedBox(height: 24),

          // Lease information card
          Card(
            elevation: 2,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Lease Information',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 16),

                  // Lease dates
                  if (widget.tenant.leaseStartDate != null) ...[
                    Row(
                      children: [
                        const Icon(
                          Icons.calendar_today,
                          size: 18,
                          color: Colors.blue,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'Lease Start: ${widget.tenant.leaseStartDate!.toString().split(' ')[0]}',
                          style: const TextStyle(fontSize: 16),
                        ),
                      ],
                    ),
                  ],

                  if (widget.tenant.leaseEndDate != null) ...[
                    const SizedBox(height: 12),
                    Row(
                      children: [
                        const Icon(
                          Icons.event_busy,
                          size: 18,
                          color: Colors.blue,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'Lease End: ${widget.tenant.leaseEndDate!.toString().split(' ')[0]}',
                          style: const TextStyle(fontSize: 16),
                        ),
                      ],
                    ),

                    // Calculate days remaining in lease
                    const SizedBox(height: 12),
                    Builder(
                      builder: (context) {
                        final now = DateTime.now();
                        final end = widget.tenant.leaseEndDate!;
                        final daysRemaining = end.difference(now).inDays;

                        Color textColor;
                        String message;

                        if (daysRemaining < 0) {
                          textColor = Colors.red;
                          message = 'Lease expired ${-daysRemaining} days ago';
                        } else if (daysRemaining < 30) {
                          textColor = Colors.orange;
                          message =
                              '$daysRemaining days remaining (expiring soon)';
                        } else {
                          textColor = Colors.green;
                          message = '$daysRemaining days remaining';
                        }

                        return Row(
                          children: [
                            const Icon(
                              Icons.timelapse,
                              size: 18,
                              color: Colors.blue,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              message,
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: textColor,
                              ),
                            ),
                          ],
                        );
                      },
                    ),
                  ],
                ],
              ),
            ),
          ),

          const SizedBox(height: 32),

          // Action buttons
          Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: _navigateToChangeRoom,
                  icon: const Icon(Icons.swap_horiz),
                  label: const Text('Change Room'),
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: _navigateToVacateRoom,
                  icon: const Icon(Icons.exit_to_app),
                  label: const Text('Vacate Room'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
