import '../services/service_locator.dart';
import '../utils/logger.dart';

/// Helper class for data cleanup operations
class DataCleanupHelper {
  
  /// Scan all payments and identify those with orphaned bill references
  static Future<Map<String, List<String>>> scanOrphanedBillReferences() async {
    final orphanedReferences = <String, List<String>>{};
    
    try {
      AppLogger.info('Starting scan for orphaned bill references...');
      
      // Get all payments
      final payments = await serviceLocator.paymentService.getAllPayments();
      
      for (final payment in payments) {
        final orphanedBillIds = await serviceLocator.paymentService
            .validatePaymentBillReferences(payment.id);
        
        if (orphanedBillIds.isNotEmpty) {
          orphanedReferences[payment.id] = orphanedBillIds;
          AppLogger.warning(
            'Payment ${payment.id} has ${orphanedBillIds.length} orphaned bill references: $orphanedBillIds'
          );
        }
      }
      
      AppLogger.info(
        'Scan complete. Found ${orphanedReferences.length} payments with orphaned references'
      );
      
      return orphanedReferences;
    } catch (e) {
      AppLogger.error('Error scanning for orphaned bill references: $e');
      return {};
    }
  }
  
  /// Clean up orphaned bill references from all payments
  static Future<int> cleanupAllOrphanedBillReferences() async {
    int cleanedCount = 0;
    
    try {
      AppLogger.info('Starting cleanup of orphaned bill references...');
      
      final orphanedReferences = await scanOrphanedBillReferences();
      
      for (final paymentId in orphanedReferences.keys) {
        final result = await serviceLocator.paymentService
            .cleanupOrphanedBillReferences(paymentId);
        
        if (result != null) {
          cleanedCount++;
          AppLogger.info('Cleaned up payment: $paymentId');
        }
      }
      
      AppLogger.info('Cleanup complete. Cleaned $cleanedCount payments');
      return cleanedCount;
    } catch (e) {
      AppLogger.error('Error during cleanup: $e');
      return cleanedCount;
    }
  }
  
  /// Generate a report of data integrity issues
  static Future<Map<String, dynamic>> generateDataIntegrityReport() async {
    final report = <String, dynamic>{};
    
    try {
      AppLogger.info('Generating data integrity report...');
      
      // Scan for orphaned bill references
      final orphanedReferences = await scanOrphanedBillReferences();
      
      // Get payment statistics
      final paymentStats = await serviceLocator.paymentService.getPaymentStatistics();
      
      // Get total bills count
      final allBills = await serviceLocator.billService.getAllBills();
      
      report['timestamp'] = DateTime.now().toIso8601String();
      report['orphaned_bill_references'] = {
        'affected_payments_count': orphanedReferences.length,
        'total_orphaned_references': orphanedReferences.values
            .fold<int>(0, (sum, list) => sum + list.length),
        'details': orphanedReferences,
      };
      report['payment_statistics'] = paymentStats;
      report['total_bills_count'] = allBills.length;
      
      AppLogger.info('Data integrity report generated successfully');
      return report;
    } catch (e) {
      AppLogger.error('Error generating data integrity report: $e');
      return {
        'error': e.toString(),
        'timestamp': DateTime.now().toIso8601String(),
      };
    }
  }
}
