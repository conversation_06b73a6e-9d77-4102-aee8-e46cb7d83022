import 'package:flutter/material.dart';
import '../../l10n/app_localizations.dart';
import '../../services/service_locator.dart';
import '../../main.dart';

class AppDrawer extends StatelessWidget {
  const AppDrawer({super.key});

  @override
  Widget build(BuildContext context) {
    final user = serviceLocator.authService.currentUser;
    final userName = user?.userMetadata?['full_name'] ?? user?.email ?? 'User';
    final userEmail = user?.email ?? '';
    final localizations = AppLocalizations.of(context);

    // Helper function to ensure first letter is capitalized
    String capitalize(String text) {
      if (text.isEmpty) return text;
      return text[0].toUpperCase() + text.substring(1);
    }

    return Drawer(
      child: Column(
        children: [
          // Main drawer items in a ListView
          Expanded(
            child: ListView(
              padding: EdgeInsets.zero,
              children: [
                UserAccountsDrawerHeader(
                  accountName: Text(
                    userName,
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).colorScheme.onPrimary,
                    ),
                  ),
                  accountEmail: Text(
                    userEmail,
                    style: TextStyle(
                      color: Theme.of(
                        context,
                      ).colorScheme.onPrimary.withValues(alpha: 0.9),
                    ),
                  ),
                  currentAccountPicture: CircleAvatar(
                    backgroundColor: Theme.of(
                      context,
                    ).colorScheme.onPrimary.withValues(alpha: 0.2),
                    child: Text(
                      userName.isNotEmpty ? userName[0].toUpperCase() : 'U',
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).colorScheme.onPrimary,
                      ),
                    ),
                  ),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.primary,
                  ),
                  onDetailsPressed: () {
                    Navigator.pop(context);
                    Navigator.pushNamed(context, '/profile');
                  },
                ),
                ListTile(
                  leading: Icon(
                    Icons.dashboard,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                  title: Text(
                    capitalize(
                      localizations?.translate('dashboard') ?? 'Dashboard',
                    ),
                  ),
                  onTap: () {
                    Navigator.pop(context);
                    Navigator.pushReplacementNamed(context, '/dashboard');
                  },
                ),
                ListTile(
                  leading: Icon(
                    Icons.home_work,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                  title: Text(
                    capitalize(
                      localizations?.translate('properties') ?? 'Properties',
                    ),
                  ),
                  onTap: () {
                    Navigator.pop(context);
                    Navigator.pushNamed(context, '/properties');
                  },
                ),
                ListTile(
                  leading: Icon(
                    Icons.meeting_room,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                  title: Text(
                    capitalize(localizations?.translate('rooms') ?? 'Rooms'),
                  ),
                  onTap: () {
                    Navigator.pop(context);
                    Navigator.pushNamed(context, '/rooms');
                  },
                ),
                ListTile(
                  leading: Icon(
                    Icons.people,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                  title: Text(
                    capitalize(
                      localizations?.translate('tenants') ?? 'Tenants',
                    ),
                  ),
                  onTap: () {
                    Navigator.pop(context);
                    Navigator.pushNamed(context, '/tenants');
                  },
                ),
                ExpansionTile(
                  leading: Icon(
                    Icons.payments,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                  title: Text(
                    capitalize(
                      localizations?.translate('payments') ?? 'Payments',
                    ),
                  ),
                  children: [
                    ListTile(
                      leading: const Icon(Icons.verified),
                      contentPadding: const EdgeInsets.only(
                        left: 72.0,
                        right: 16.0,
                      ),
                      title: const Text('Verify Payment'),
                      onTap: () {
                        Navigator.pop(context);
                        Navigator.pushNamed(context, '/payment-history');
                      },
                    ),
                    ListTile(
                      leading: const Icon(Icons.add),
                      contentPadding: const EdgeInsets.only(
                        left: 72.0,
                        right: 16.0,
                      ),
                      title: const Text('Add New Bill'),
                      onTap: () {
                        Navigator.pop(context);
                        Navigator.pushNamed(context, '/select-tenant-for-bill');
                      },
                    ),
                    ListTile(
                      leading: const Icon(Icons.payment),
                      contentPadding: const EdgeInsets.only(
                        left: 72.0,
                        right: 16.0,
                      ),
                      title: const Text('Record Payment'),
                      onTap: () {
                        Navigator.pop(context);
                        Navigator.pushNamed(
                          context,
                          '/select-tenant-for-payment',
                        );
                      },
                    ),
                    ListTile(
                      leading: const Icon(Icons.people),
                      contentPadding: const EdgeInsets.only(
                        left: 72.0,
                        right: 16.0,
                      ),
                      title: const Text('Group Bill'),
                      onTap: () {
                        Navigator.pop(context);
                        Navigator.pushNamed(context, '/multi-tenant-bill');
                      },
                    ),
                    ListTile(
                      leading: const Icon(Icons.approval),
                      contentPadding: const EdgeInsets.only(
                        left: 72.0,
                        right: 16.0,
                      ),
                      title: const Text('Approve/reject payment'),
                      onTap: () {
                        Navigator.pop(context);
                        Navigator.pushNamed(context, '/payment-history');
                      },
                    ),
                    ListTile(
                      leading: const Icon(Icons.auto_awesome),
                      contentPadding: const EdgeInsets.only(
                        left: 72.0,
                        right: 16.0,
                      ),
                      title: const Text('Bill Templates'),
                      onTap: () {
                        Navigator.pop(context);
                        Navigator.pushNamed(context, '/bill-templates');
                      },
                    ),
                  ],
                ),
                ListTile(
                  leading: Icon(
                    Icons.receipt_long,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                  title: Text(
                    capitalize(
                      localizations?.translate('expenses') ?? 'Expenses',
                    ),
                  ),
                  onTap: () {
                    Navigator.pop(context);
                    Navigator.pushNamed(context, '/expenses');
                  },
                ),
                ListTile(
                  leading: Icon(
                    Icons.campaign,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                  title: const Text('Community Notices'),
                  onTap: () {
                    Navigator.pop(context);
                    Navigator.pushNamed(context, '/community-notices');
                  },
                ),
                ListTile(
                  leading: Icon(
                    Icons.bar_chart,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                  title: const Text('Reports'),
                  onTap: () {
                    Navigator.pop(context);
                    Navigator.pushNamed(context, '/reports');
                  },
                ),

                const Divider(),
                ListTile(
                  leading: Icon(
                    Icons.person,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                  title: Text(
                    capitalize(
                      localizations?.translate('profile') ?? 'Profile',
                    ),
                  ),
                  onTap: () {
                    Navigator.pop(context);
                    Navigator.pushNamed(context, '/profile');
                  },
                ),
                ListTile(
                  leading: Icon(
                    Icons.settings,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                  title: Text(
                    capitalize(
                      localizations?.translate('settings') ?? 'Settings',
                    ),
                  ),
                  onTap: () {
                    Navigator.pop(context);
                    Navigator.pushNamed(context, '/settings');
                  },
                ),
              ],
            ),
          ),
          // Sign out button at the bottom
          Container(
            decoration: BoxDecoration(
              border: Border(
                top: BorderSide(color: Colors.grey.withValues(alpha: 0.3)),
              ),
            ),
            child: ListTile(
              leading: Icon(
                Icons.logout,
                color: Theme.of(context).colorScheme.error,
              ),
              title: Text(
                capitalize(localizations?.translate('signOut') ?? 'Sign Out'),
                style: TextStyle(color: Theme.of(context).colorScheme.error),
              ),
              onTap: () async {
                // Store navigator reference before async operations
                final navigator = Navigator.of(context);

                // Show confirmation dialog
                final shouldLogout =
                    await showDialog<bool>(
                      context: context,
                      builder:
                          (context) => AlertDialog(
                            title: Text(
                              capitalize(
                                localizations?.translate('signOut') ??
                                    'Sign Out',
                              ),
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            content: Text(
                              localizations?.translate('signOutConfirmation') ??
                                  'Are you sure you want to sign out?',
                            ),
                            actions: [
                              TextButton(
                                onPressed:
                                    () => Navigator.of(context).pop(false),
                                child: Text(
                                  localizations?.translate('cancel') ??
                                      'Cancel',
                                  style: TextStyle(color: Colors.grey[700]),
                                ),
                              ),
                              ElevatedButton(
                                onPressed:
                                    () => Navigator.of(context).pop(true),
                                style: ElevatedButton.styleFrom(
                                  backgroundColor:
                                      Theme.of(context).colorScheme.error,
                                  foregroundColor: Colors.white,
                                ),
                                child: Text(
                                  localizations?.translate('signOut') ??
                                      'Sign Out',
                                ),
                              ),
                            ],
                          ),
                    ) ??
                    false;

                // If user confirmed, sign out
                if (shouldLogout) {
                  navigator.pop(); // Close drawer first

                  try {
                    await serviceLocator.authService.signOut();
                    // Use the global navigator key to avoid context issues
                    navigatorKey.currentState?.pushReplacementNamed('/auth');
                  } catch (e) {
                    // Handle any logout errors gracefully
                    if (context.mounted) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text(
                            'Failed to sign out. Please try again.',
                          ),
                          backgroundColor: Colors.red,
                        ),
                      );
                    }
                  }
                }
              },
            ),
          ),
        ],
      ),
    );
  }
}
