import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import '../utils/logger.dart';

/// Service to manage onboarding state and track first-time app usage
class OnboardingService {
  static const String _onboardingCompleteKey = 'onboarding_completed';
  static const String _firstLaunchKey = 'first_launch_date';
  
  final FlutterSecureStorage _storage = const FlutterSecureStorage();
  
  /// Check if the user has completed onboarding
  Future<bool> hasCompletedOnboarding() async {
    try {
      final completed = await _storage.read(key: _onboardingCompleteKey);
      return completed == 'true';
    } catch (e) {
      AppLogger.error('Error checking onboarding status: $e');
      return false;
    }
  }
  
  /// Mark onboarding as completed
  Future<void> markOnboardingComplete() async {
    try {
      await _storage.write(key: _onboardingCompleteKey, value: 'true');
      AppLogger.info('Onboarding marked as complete');
    } catch (e) {
      AppLogger.error('Error marking onboarding complete: $e');
    }
  }
  
  /// Check if this is the first app launch
  Future<bool> isFirstLaunch() async {
    try {
      final firstLaunch = await _storage.read(key: _firstLaunchKey);
      if (firstLaunch == null) {
        // This is the first launch, record the date
        await _storage.write(
          key: _firstLaunchKey, 
          value: DateTime.now().toIso8601String(),
        );
        return true;
      }
      return false;
    } catch (e) {
      AppLogger.error('Error checking first launch: $e');
      return false;
    }
  }
  
  /// Get the first launch date
  Future<DateTime?> getFirstLaunchDate() async {
    try {
      final dateString = await _storage.read(key: _firstLaunchKey);
      if (dateString != null) {
        return DateTime.parse(dateString);
      }
      return null;
    } catch (e) {
      AppLogger.error('Error getting first launch date: $e');
      return null;
    }
  }
  
  /// Reset onboarding state (useful for testing or re-onboarding)
  Future<void> resetOnboarding() async {
    try {
      await _storage.delete(key: _onboardingCompleteKey);
      AppLogger.info('Onboarding state reset');
    } catch (e) {
      AppLogger.error('Error resetting onboarding: $e');
    }
  }
  
  /// Check if onboarding should be shown
  /// Returns true if user hasn't completed onboarding
  Future<bool> shouldShowOnboarding() async {
    try {
      final completed = await hasCompletedOnboarding();
      return !completed;
    } catch (e) {
      AppLogger.error('Error checking if should show onboarding: $e');
      return false;
    }
  }
}
