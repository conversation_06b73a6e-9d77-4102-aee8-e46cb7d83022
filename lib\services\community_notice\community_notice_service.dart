import 'package:supabase_flutter/supabase_flutter.dart';
import '../../models/community_notice/community_notice.dart';
import '../../utils/logger.dart';

/// Service class for managing community notices
class CommunityNoticeService {
  final SupabaseClient _supabase = Supabase.instance.client;
  static const String _tableName = 'community_notices';
  static const String _viewName = 'active_community_notices';

  /// Create a new community notice
  Future<CommunityNotice> createNotice(CommunityNotice notice) async {
    try {
      AppLogger.info('Creating community notice: ${notice.title}');

      final response = await _supabase
          .from(_tableName)
          .insert(notice.toJson())
          .select()
          .maybeSingle();

      if (response == null) {
        AppLogger.error('Failed to create notice or access denied');
        throw Exception('Failed to create notice or you do not have permission');
      }

      AppLogger.info('Community notice created successfully');
      return CommunityNotice.fromJson(response);
    } catch (e) {
      AppLogger.error('Error creating community notice: $e');
      rethrow;
    }
  }

  /// Update an existing community notice
  Future<CommunityNotice> updateNotice(CommunityNotice notice) async {
    try {
      AppLogger.info('Updating community notice: ${notice.id}');

      final updateData = notice.toJson();
      updateData['updated_at'] = DateTime.now().toIso8601String();

      final response = await _supabase
          .from(_tableName)
          .update(updateData)
          .eq('id', notice.id)
          .select()
          .maybeSingle();

      if (response == null) {
        AppLogger.error('Notice not found or access denied: ${notice.id}');
        throw Exception('Notice not found or you do not have permission to update it');
      }

      AppLogger.info('Community notice updated successfully');
      return CommunityNotice.fromJson(response);
    } catch (e) {
      AppLogger.error('Error updating community notice: $e');
      rethrow;
    }
  }

  /// Get all notices for a specific property
  Future<List<CommunityNotice>> getNoticesForProperty(String propertyId) async {
    try {
      AppLogger.info('Fetching notices for property: $propertyId');
      
      final response = await _supabase
          .from(_viewName)
          .select()
          .eq('property_id', propertyId)
          .order('created_at', ascending: false);

      final notices = (response as List)
          .map((json) => CommunityNotice.fromJson(json))
          .toList();

      AppLogger.info('Fetched ${notices.length} notices for property');
      return notices;
    } catch (e) {
      AppLogger.error('Error fetching notices for property: $e');
      rethrow;
    }
  }

  /// Get all notices for properties managed by current user
  Future<List<CommunityNotice>> getNoticesForUser() async {
    try {
      AppLogger.info('Fetching notices for current user');
      
      final response = await _supabase
          .from(_viewName)
          .select()
          .order('created_at', ascending: false);

      final notices = (response as List)
          .map((json) => CommunityNotice.fromJson(json))
          .toList();

      AppLogger.info('Fetched ${notices.length} notices for user');
      return notices;
    } catch (e) {
      AppLogger.error('Error fetching notices for user: $e');
      rethrow;
    }
  }

  /// Get notices by type
  Future<List<CommunityNotice>> getNoticesByType(
    String propertyId, 
    NoticeType type
  ) async {
    try {
      AppLogger.info('Fetching notices by type: ${type.name} for property: $propertyId');
      
      final response = await _supabase
          .from(_viewName)
          .select()
          .eq('property_id', propertyId)
          .eq('type', type.name)
          .order('created_at', ascending: false);

      final notices = (response as List)
          .map((json) => CommunityNotice.fromJson(json))
          .toList();

      AppLogger.info('Fetched ${notices.length} notices of type ${type.name}');
      return notices;
    } catch (e) {
      AppLogger.error('Error fetching notices by type: $e');
      rethrow;
    }
  }

  /// Get notices by priority
  Future<List<CommunityNotice>> getNoticesByPriority(
    String propertyId, 
    NoticePriority priority
  ) async {
    try {
      AppLogger.info('Fetching notices by priority: ${priority.name} for property: $propertyId');
      
      final response = await _supabase
          .from(_viewName)
          .select()
          .eq('property_id', propertyId)
          .eq('priority', priority.name)
          .order('created_at', ascending: false);

      final notices = (response as List)
          .map((json) => CommunityNotice.fromJson(json))
          .toList();

      AppLogger.info('Fetched ${notices.length} notices of priority ${priority.name}');
      return notices;
    } catch (e) {
      AppLogger.error('Error fetching notices by priority: $e');
      rethrow;
    }
  }

  /// Get urgent notices (high and urgent priority)
  Future<List<CommunityNotice>> getUrgentNotices([String? propertyId]) async {
    try {
      AppLogger.info('Fetching urgent notices${propertyId != null ? ' for property: $propertyId' : ''}');
      
      var query = _supabase
          .from(_viewName)
          .select()
          .inFilter('priority', ['high', 'urgent']);
      
      if (propertyId != null) {
        query = query.eq('property_id', propertyId);
      }
      
      final response = await query
          .order('priority')
          .order('created_at', ascending: false)
          .limit(10);

      final notices = (response as List)
          .map((json) => CommunityNotice.fromJson(json))
          .toList();

      AppLogger.info('Fetched ${notices.length} urgent notices');
      return notices;
    } catch (e) {
      AppLogger.error('Error fetching urgent notices: $e');
      rethrow;
    }
  }

  /// Delete a notice (soft delete by setting is_active to false)
  Future<void> deleteNotice(String noticeId) async {
    try {
      AppLogger.info('Deleting community notice: $noticeId');
      
      await _supabase
          .from(_tableName)
          .update({
            'is_active': false,
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', noticeId);

      AppLogger.info('Community notice deleted successfully');
    } catch (e) {
      AppLogger.error('Error deleting community notice: $e');
      rethrow;
    }
  }

  /// Get a single notice by ID
  Future<CommunityNotice?> getNoticeById(String noticeId) async {
    try {
      AppLogger.info('Fetching notice by ID: $noticeId');
      
      final response = await _supabase
          .from(_viewName)
          .select()
          .eq('id', noticeId)
          .maybeSingle();

      if (response == null) {
        AppLogger.info('Notice not found: $noticeId');
        return null;
      }

      AppLogger.info('Notice fetched successfully');
      return CommunityNotice.fromJson(response);
    } catch (e) {
      AppLogger.error('Error fetching notice by ID: $e');
      rethrow;
    }
  }

  /// Search notices
  Future<List<CommunityNotice>> searchNotices(
    String searchTerm, {
    String? propertyId,
    NoticeType? type,
    NoticePriority? priority,
  }) async {
    try {
      AppLogger.info('Searching notices for: $searchTerm');
      
      var query = _supabase
          .from(_viewName)
          .select();
      
      // Add property filter if specified
      if (propertyId != null) {
        query = query.eq('property_id', propertyId);
      }
      
      // Add type filter if specified
      if (type != null) {
        query = query.eq('type', type.name);
      }
      
      // Add priority filter if specified
      if (priority != null) {
        query = query.eq('priority', priority.name);
      }
      
      // Add text search
      query = query.or('title.ilike.%$searchTerm%,content.ilike.%$searchTerm%');
      
      final response = await query.order('created_at', ascending: false);

      final notices = (response as List)
          .map((json) => CommunityNotice.fromJson(json))
          .toList();

      AppLogger.info('Found ${notices.length} notices matching search');
      return notices;
    } catch (e) {
      AppLogger.error('Error searching notices: $e');
      rethrow;
    }
  }

  /// Get notices with filters
  Future<List<CommunityNotice>> getFilteredNotices({
    String? propertyId,
    NoticeType? type,
    NoticePriority? priority,
    DateTime? fromDate,
    DateTime? toDate,
    bool? isActive,
    int? limit,
  }) async {
    try {
      AppLogger.info('Fetching filtered notices');
      
      var query = _supabase.from(_viewName).select();
      
      // Apply filters
      if (propertyId != null) {
        query = query.eq('property_id', propertyId);
      }
      
      if (type != null) {
        query = query.eq('type', type.name);
      }
      
      if (priority != null) {
        query = query.eq('priority', priority.name);
      }
      
      if (fromDate != null) {
        query = query.gte('created_at', fromDate.toIso8601String());
      }
      
      if (toDate != null) {
        query = query.lte('created_at', toDate.toIso8601String());
      }
      
      if (isActive != null) {
        query = query.eq('is_active', isActive);
      }
      
      var orderedQuery = query.order('created_at', ascending: false);

      if (limit != null) {
        orderedQuery = orderedQuery.limit(limit);
      }

      final response = await orderedQuery;

      final notices = (response as List)
          .map((json) => CommunityNotice.fromJson(json))
          .toList();

      AppLogger.info('Fetched ${notices.length} filtered notices');
      return notices;
    } catch (e) {
      AppLogger.error('Error fetching filtered notices: $e');
      rethrow;
    }
  }

  /// Get notices count by property
  Future<Map<String, int>> getNoticesCountByProperty() async {
    try {
      AppLogger.info('Fetching notices count by property');

      final response = await _supabase
          .from(_viewName)
          .select('property_id, property_name');

      final Map<String, int> counts = {};
      for (final item in response as List) {
        final propertyName = item['property_name'] as String;
        counts[propertyName] = (counts[propertyName] ?? 0) + 1;
      }

      AppLogger.info('Fetched notices count for ${counts.length} properties');
      return counts;
    } catch (e) {
      AppLogger.error('Error fetching notices count: $e');
      rethrow;
    }
  }

  /// Archive expired notices
  Future<int> archiveExpiredNotices() async {
    try {
      AppLogger.info('Archiving expired notices');

      final response = await _supabase.rpc('archive_expired_notices');
      final archivedCount = response as int;

      AppLogger.info('Archived $archivedCount expired notices');
      return archivedCount;
    } catch (e) {
      AppLogger.error('Error archiving expired notices: $e');
      rethrow;
    }
  }

  /// Get recent notices (last 7 days)
  Future<List<CommunityNotice>> getRecentNotices([String? propertyId]) async {
    final sevenDaysAgo = DateTime.now().subtract(const Duration(days: 7));

    return getFilteredNotices(
      propertyId: propertyId,
      fromDate: sevenDaysAgo,
      limit: 20,
    );
  }

  /// Create a combined property notice
  Future<CommunityNotice> createCombinedNotice({
    required String title,
    required String content,
    required NoticeType type,
    required NoticePriority priority,
    required List<Map<String, dynamic>> properties, // List of {id, name} maps
    required String authorId,
    required String authorName,
    DateTime? expiresAt,
    List<String>? attachmentUrls,
    Map<String, dynamic>? additionalMetadata,
  }) async {
    try {
      if (properties.isEmpty) {
        throw Exception('At least one property must be specified');
      }

      AppLogger.info('Creating combined property notice for ${properties.length} properties');

      // Use the first property as the primary property
      final primaryProperty = properties.first;
      final additionalProperties = properties.skip(1).toList();

      final combinedNotice = CommunityNotice.createCombined(
        title: title,
        content: content,
        type: type,
        priority: priority,
        primaryPropertyId: primaryProperty['id'] as String,
        additionalProperties: additionalProperties,
        authorId: authorId,
        authorName: authorName,
        expiresAt: expiresAt,
        attachmentUrls: attachmentUrls,
        additionalMetadata: additionalMetadata,
      );

      final response = await _supabase
          .from(_tableName)
          .insert(combinedNotice.toJson())
          .select()
          .maybeSingle();

      if (response == null) {
        AppLogger.error('Failed to create combined notice or access denied');
        throw Exception('Failed to create combined notice or you do not have permission');
      }

      AppLogger.info('Combined property notice created successfully');
      return CommunityNotice.fromJson(response);
    } catch (e) {
      AppLogger.error('Error creating combined property notice: $e');
      rethrow;
    }
  }

  /// Convert an existing notice to a combined property notice
  Future<CommunityNotice> convertToCombinedNotice({
    required String noticeId,
    required List<Map<String, dynamic>> additionalProperties,
    Map<String, dynamic>? additionalMetadata,
  }) async {
    try {
      AppLogger.info('Converting notice $noticeId to combined property notice');

      // First, get the existing notice
      final existingNotice = await getNoticeById(noticeId);
      if (existingNotice == null) {
        throw Exception('Notice not found');
      }

      // Convert to combined notice
      final combinedNotice = existingNotice.toCombinedNotice(
        additionalProperties: additionalProperties,
        additionalMetadata: additionalMetadata,
      );

      // Update the notice in the database
      final updatedNotice = await updateNotice(combinedNotice);

      AppLogger.info('Notice converted to combined property notice successfully');
      return updatedNotice;
    } catch (e) {
      AppLogger.error('Error converting notice to combined property notice: $e');
      rethrow;
    }
  }

  /// Get property information for creating combined notices
  Future<List<Map<String, dynamic>>> getPropertiesInfo(List<String> propertyIds) async {
    try {
      AppLogger.info('Fetching property information for ${propertyIds.length} properties');

      final response = await _supabase
          .from('properties')
          .select('id, name')
          .inFilter('id', propertyIds);

      final properties = (response as List)
          .map((json) => {
                'id': json['id'] as String,
                'name': json['name'] as String,
              })
          .toList();

      AppLogger.info('Fetched information for ${properties.length} properties');
      return properties;
    } catch (e) {
      AppLogger.error('Error fetching properties info: $e');
      rethrow;
    }
  }

  /// Get expiring notices (expiring in next 7 days)
  Future<List<CommunityNotice>> getExpiringNotices([String? propertyId]) async {
    try {
      AppLogger.info('Fetching expiring notices');

      final now = DateTime.now();
      final sevenDaysFromNow = now.add(const Duration(days: 7));

      var query = _supabase
          .from(_viewName)
          .select()
          .not('expires_at', 'is', null)
          .gte('expires_at', now.toIso8601String())
          .lte('expires_at', sevenDaysFromNow.toIso8601String());

      if (propertyId != null) {
        query = query.eq('property_id', propertyId);
      }

      final response = await query.order('expires_at');

      final notices = (response as List)
          .map((json) => CommunityNotice.fromJson(json))
          .toList();

      AppLogger.info('Fetched ${notices.length} expiring notices');
      return notices;
    } catch (e) {
      AppLogger.error('Error fetching expiring notices: $e');
      rethrow;
    }
  }

  /// Check if user can edit notice (is author)
  Future<bool> canEditNotice(String noticeId) async {
    try {
      final currentUser = _supabase.auth.currentUser;
      if (currentUser == null) return false;

      final response = await _supabase
          .from(_tableName)
          .select('author_id')
          .eq('id', noticeId)
          .maybeSingle();

      if (response == null) return false;

      return response['author_id'] == currentUser.id;
    } catch (e) {
      AppLogger.error('Error checking edit permissions: $e');
      return false;
    }
  }

  /// Get notice statistics
  Future<Map<String, dynamic>> getNoticeStatistics([String? propertyId]) async {
    try {
      AppLogger.info('Fetching notice statistics');

      var query = _supabase.from(_viewName).select('type, priority');

      if (propertyId != null) {
        query = query.eq('property_id', propertyId);
      }

      final response = await query;
      final notices = response as List;

      final stats = {
        'total': notices.length,
        'by_type': <String, int>{},
        'by_priority': <String, int>{},
      };

      for (final notice in notices) {
        final type = notice['type'] as String;
        final priority = notice['priority'] as String;

        final byType = stats['by_type'] as Map<String, int>;
        final byPriority = stats['by_priority'] as Map<String, int>;

        byType[type] = (byType[type] ?? 0) + 1;
        byPriority[priority] = (byPriority[priority] ?? 0) + 1;
      }

      AppLogger.info('Fetched notice statistics');
      return stats;
    } catch (e) {
      AppLogger.error('Error fetching notice statistics: $e');
      rethrow;
    }
  }
}
