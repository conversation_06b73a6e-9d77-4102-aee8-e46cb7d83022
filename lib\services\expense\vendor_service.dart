import '../../models/expense/vendor_model.dart';
import '../../utils/logger.dart';
import '../supabase_service.dart';

class Logger {
  final String _name;
  
  Logger(this._name);
  
  void error(String message) {
    AppLogger.error('[$_name] $message');
  }
  
  void info(String message) {
    AppLogger.info('[$_name] $message');
  }
  
  void warning(String message) {
    AppLogger.warning('[$_name] $message');
  }
  
  void debug(String message) {
    AppLogger.debug('[$_name] $message');
  }
}

class VendorService {
  final SupabaseService _supabaseService;
  final Logger _logger = Logger('VendorService');

  VendorService(this._supabaseService);

  Future<List<VendorModel>> getVendors({
    String? categoryId,
    bool? isPreferred,
  }) async {
    try {
      final supabase = _supabaseService.client;
      
      var query = supabase
          .from('vendors')
          .select('''
            *,
            expense_categories(name)
          ''');

      if (categoryId != null) {
        query = query.eq('category_id', categoryId);
      }

      if (isPreferred != null) {
        query = query.eq('is_preferred', isPreferred);
      }

      final response = await query.order('name');
      
      return response.map((vendor) {
        final categoryData = vendor['expense_categories'];
        
        return VendorModel.fromJson({
          ...vendor,
          'category_name': categoryData?['name'],
        });
      }).toList();
    } catch (e) {
      _logger.error('Error fetching vendors: $e');
      rethrow;
    }
  }

  Future<VendorModel> getVendorById(String id) async {
    try {
      final supabase = _supabaseService.client;
      
      final response = await supabase
          .from('vendors')
          .select('''
            *,
            expense_categories(name)
          ''')
          .eq('id', id)
          .single();
      
      final categoryData = response['expense_categories'];
      
      return VendorModel.fromJson({
        ...response,
        'category_name': categoryData?['name'],
      });
    } catch (e) {
      _logger.error('Error fetching vendor by ID: $e');
      rethrow;
    }
  }

  Future<VendorModel> createVendor(VendorModel vendor) async {
    try {
      final supabase = _supabaseService.client;
      
      final response = await supabase
          .from('vendors')
          .insert(vendor.toJson())
          .select('''
            *,
            expense_categories(name)
          ''')
          .single();
      
      final categoryData = response['expense_categories'];
      
      return VendorModel.fromJson({
        ...response,
        'category_name': categoryData?['name'],
      });
    } catch (e) {
      _logger.error('Error creating vendor: $e');
      rethrow;
    }
  }

  Future<VendorModel> updateVendor(VendorModel vendor) async {
    try {
      final supabase = _supabaseService.client;
      
      final response = await supabase
          .from('vendors')
          .update(vendor.toJson())
          .eq('id', vendor.id!)
          .select('''
            *,
            expense_categories(name)
          ''')
          .single();
      
      final categoryData = response['expense_categories'];
      
      return VendorModel.fromJson({
        ...response,
        'category_name': categoryData?['name'],
      });
    } catch (e) {
      _logger.error('Error updating vendor: $e');
      rethrow;
    }
  }

  Future<void> deleteVendor(String id) async {
    try {
      final supabase = _supabaseService.client;
      
      // Check if there are any expenses using this vendor
      final expensesResponse = await supabase
          .from('expenses')
          .select('id')
          .eq('vendor_id', id);
      
      if (expensesResponse.isNotEmpty) {
        throw Exception('Cannot delete vendor that is used by expenses');
      }
      
      await supabase
          .from('vendors')
          .delete()
          .eq('id', id);
    } catch (e) {
      _logger.error('Error deleting vendor: $e');
      rethrow;
    }
  }

  Future<void> updateVendorRating(String id, double rating) async {
    try {
      final supabase = _supabaseService.client;
      
      await supabase
          .from('vendors')
          .update({ 'rating': rating })
          .eq('id', id);
    } catch (e) {
      _logger.error('Error updating vendor rating: $e');
      rethrow;
    }
  }

  Future<void> togglePreferred(String id) async {
    try {
      final supabase = _supabaseService.client;
      
      // Get current preferred status
      final vendor = await getVendorById(id);
      
      // Toggle it
      await supabase
          .from('vendors')
          .update({ 'is_preferred': !vendor.isPreferred })
          .eq('id', id);
    } catch (e) {
      _logger.error('Error toggling vendor preferred status: $e');
      rethrow;
    }
  }
} 