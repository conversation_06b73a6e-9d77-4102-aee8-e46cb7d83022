import 'package:flutter/material.dart';
import '../../models/tenant/tenant.dart';
import '../../services/service_locator.dart';
import '../../widgets/app_loading_indicator.dart';
import 'assign_room_page.dart';
import 'add_tenant_page.dart'; // Import the AddTenantPage

class TenantSelectionPage extends StatefulWidget {
  final String roomId;
  final String roomName;

  const TenantSelectionPage({
    super.key,
    required this.roomId,
    required this.roomName,
  });

  @override
  State<TenantSelectionPage> createState() => _TenantSelectionPageState();
}

class _TenantSelectionPageState extends State<TenantSelectionPage> {
  bool _isLoading = true;
  String? _errorMessage;
  List<Tenant> _tenants = [];
  List<Tenant> _filteredTenants = [];
  bool _showOnlyAvailable = true;

  // Search controller
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _loadTenants();
    _searchController.addListener(_filterTenants);
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _filterTenants() {
    final query = _searchController.text.toLowerCase();

    setState(() {
      // Start with all tenants
      List<Tenant> filtered = List.from(_tenants);

      // Apply search filter if query exists
      if (query.isNotEmpty) {
        filtered =
            filtered.where((tenant) {
              final fullName =
                  '${tenant.firstName} ${tenant.lastName}'.toLowerCase();
              final email = tenant.email.toLowerCase();

              return fullName.contains(query) || email.contains(query);
            }).toList();
      }

      // Apply availability filter if enabled
      if (_showOnlyAvailable) {
        filtered =
            filtered
                .where(
                  (tenant) =>
                      tenant.roomId == null ||
                      tenant.status == TenantStatus.movedOut,
                )
                .toList();
      }

      _filteredTenants = filtered;
    });
  }

  Future<void> _loadTenants() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // Get all tenants - include moved out tenants
      final tenants = await serviceLocator.tenantService.getAllTenants();

      // Sort by name
      tenants.sort(
        (a, b) => '${a.firstName} ${a.lastName}'.compareTo(
          '${b.firstName} ${b.lastName}',
        ),
      );

      setState(() {
        _tenants = tenants;

        // Apply filters to get initial filtered list
        _filteredTenants =
            _showOnlyAvailable
                ? tenants.where((t) => t.roomId == null).toList()
                : tenants;

        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'Failed to load tenants: $e';
        _isLoading = false;
      });
    }
  }

  void _selectTenant(Tenant tenant) {
    // Capture context before async gap
    final navigator = Navigator.of(context);

    // If tenant is moved out, we need to reactivate them
    final bool isMovedOut = tenant.status == TenantStatus.movedOut;

    // Navigate to assign room page with the selected tenant
    navigator
        .push(
          MaterialPageRoute(
            builder:
                (context) => AssignRoomPage(
                  tenant: tenant,
                  preselectedRoomId: widget.roomId,
                  preselectedRoomName: widget.roomName,
                  isReactivation: isMovedOut,
                ),
          ),
        )
        .then((result) {
          if (result != null) {
            // Return the result to the previous page
            navigator.pop(result);
          }
        });
  }

  void _createNewTenant() {
    // Capture context before async gap
    final navigator = Navigator.of(context);

    // Navigate to AddTenantPage first
    navigator
        .push<Tenant>(
          MaterialPageRoute(
            builder: (context) => const AddTenantPage(),
          ),
        )
        .then((newTenant) {
          if (newTenant != null) {
            // If a new tenant was created, navigate to assign room page
            navigator
                .push(
                  MaterialPageRoute(
                    builder: (context) => AssignRoomPage(
                      tenant: newTenant,
                      preselectedRoomId: widget.roomId,
                      preselectedRoomName: widget.roomName,
                    ),
                  ),
                )
                .then((result) {
                  if (result != null) {
                    // Return the result to the previous page
                    navigator.pop(result);
                  }
                });
          }
        });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Select Tenant'), elevation: 2),
      body:
          _isLoading
              ? const AppLoadingIndicator(message: 'Loading tenants...')
              : _errorMessage != null
              ? _buildErrorView()
              : _buildTenantsList(),
      floatingActionButton: FloatingActionButton(
        onPressed: _createNewTenant,
        tooltip: 'Add New Tenant',
        child: const Icon(Icons.person_add),
      ),
      bottomNavigationBar:
          _isLoading || _errorMessage != null
              ? null
              : BottomAppBar(
                padding: EdgeInsets.zero,
                height: 56,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Text('Show only available tenants'),
                    Switch(
                      value: _showOnlyAvailable,
                      onChanged: (value) {
                        setState(() {
                          _showOnlyAvailable = value;
                          _filterTenants();
                        });
                      },
                    ),
                  ],
                ),
              ),
    );
  }

  Widget _buildErrorView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.error_outline, size: 64, color: Colors.red),
          const SizedBox(height: 16),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 32.0),
            child: Text(
              _errorMessage!,
              textAlign: TextAlign.center,
              style: const TextStyle(color: Colors.red, fontSize: 16),
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: _loadTenants,
            icon: const Icon(Icons.refresh),
            label: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Widget _buildTenantsList() {
    return Column(
      children: [
        // Search bar
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'Search tenants...',
              prefixIcon: const Icon(Icons.search),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              contentPadding: const EdgeInsets.symmetric(
                vertical: 0,
                horizontal: 16,
              ),
              suffixIcon:
                  _searchController.text.isNotEmpty
                      ? IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () {
                          _searchController.clear();
                        },
                      )
                      : null,
            ),
          ),
        ),

        // Results count
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                _getTenantsCountText(),
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[700],
                  fontStyle: FontStyle.italic,
                ),
              ),
              if (_filteredTenants.isEmpty && _tenants.isNotEmpty)
                TextButton.icon(
                  onPressed: () => _searchController.clear(),
                  icon: const Icon(Icons.refresh, size: 14),
                  label: const Text('Clear search'),
                  style: TextButton.styleFrom(
                    padding: EdgeInsets.zero,
                    minimumSize: const Size(0, 0),
                    tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  ),
                ),
            ],
          ),
        ),

        // Tenants list
        Expanded(
          child:
              _filteredTenants.isEmpty
                  ? _buildEmptyState()
                  : ListView.builder(
                    itemCount: _filteredTenants.length,
                    itemBuilder: (context, index) {
                      final tenant = _filteredTenants[index];
                      return _buildTenantCard(tenant);
                    },
                  ),
        ),
      ],
    );
  }

  Widget _buildEmptyState() {
    final bool hasTenantsWithRooms =
        _tenants.isNotEmpty &&
        _tenants.every(
          (t) => t.roomId != null && t.status != TenantStatus.movedOut,
        );
    final bool isFiltering = _searchController.text.isNotEmpty;

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            isFiltering
                ? Icons.search_off
                : (hasTenantsWithRooms ? Icons.home : Icons.person_off),
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            isFiltering
                ? 'No matches found'
                : (hasTenantsWithRooms
                    ? 'All tenants have rooms'
                    : 'No tenants available'),
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey[700],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            isFiltering
                ? 'Try a different search term'
                : (hasTenantsWithRooms
                    ? 'Create a new tenant or reassign an existing one'
                    : 'Add a new tenant to assign to this room'),
            style: TextStyle(color: Colors.grey[600]),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: _createNewTenant,
            icon: const Icon(Icons.person_add),
            label: const Text('Add New Tenant'),
          ),
        ],
      ),
    );
  }

  Widget _buildTenantCard(Tenant tenant) {
    final bool hasRoom = tenant.roomId != null;
    final bool isMovedOut = tenant.status == TenantStatus.movedOut;

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      child: InkWell(
        onTap: (hasRoom && !isMovedOut) ? null : () => _selectTenant(tenant),
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(12.0),
          child: Row(
            children: [
              // Avatar
              CircleAvatar(
                backgroundColor:
                    isMovedOut
                        ? Colors.grey.shade200
                        : (hasRoom
                            ? Colors.grey.shade200
                            : Colors.blue.shade100),
                child: Text(
                  '${tenant.firstName[0]}${tenant.lastName.isNotEmpty ? tenant.lastName[0] : ''}',
                  style: TextStyle(
                    color:
                        isMovedOut
                            ? Colors.grey.shade700
                            : (hasRoom
                                ? Colors.grey.shade700
                                : Colors.blue.shade800),
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              const SizedBox(width: 16),

              // Tenant details
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            '${tenant.firstName} ${tenant.lastName}',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color:
                                  isMovedOut
                                      ? Colors.grey.shade600
                                      : (hasRoom
                                          ? Colors.grey.shade600
                                          : Colors.black),
                            ),
                          ),
                        ),
                        if (hasRoom && !isMovedOut)
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 2,
                            ),
                            decoration: BoxDecoration(
                              color: Colors.orange.shade100,
                              borderRadius: BorderRadius.circular(4),
                              border: Border.all(color: Colors.orange.shade300),
                            ),
                            child: Text(
                              'Has Room',
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.orange.shade800,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        if (isMovedOut)
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 2,
                            ),
                            decoration: BoxDecoration(
                              color: Colors.grey.shade100,
                              borderRadius: BorderRadius.circular(4),
                              border: Border.all(color: Colors.grey.shade300),
                            ),
                            child: Text(
                              'Moved Out',
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.grey.shade800,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                      ],
                    ),
                    const SizedBox(height: 4),
                    Text(
                      tenant.email,
                      style: TextStyle(
                        fontSize: 14,
                        color:
                            isMovedOut
                                ? Colors.grey.shade500
                                : (hasRoom
                                    ? Colors.grey.shade500
                                    : Colors.grey.shade700),
                      ),
                    ),
                    if (tenant.phoneNumber != null) ...[
                      const SizedBox(height: 4),
                      Text(
                        tenant.phoneNumber!,
                        style: TextStyle(
                          fontSize: 14,
                          color:
                              isMovedOut
                                  ? Colors.grey.shade500
                                  : (hasRoom
                                      ? Colors.grey.shade500
                                      : Colors.grey.shade700),
                        ),
                      ),
                    ],
                  ],
                ),
              ),

              // Select button
              ElevatedButton(
                onPressed:
                    (hasRoom && !isMovedOut)
                        ? null
                        : () => _selectTenant(tenant),
                style: ElevatedButton.styleFrom(
                  backgroundColor:
                      isMovedOut
                          ? Colors.blue
                          : (hasRoom ? Colors.grey.shade300 : Colors.green),
                  foregroundColor:
                      isMovedOut
                          ? Colors.white
                          : (hasRoom ? Colors.grey.shade700 : Colors.white),
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 8,
                  ),
                ),
                child: Text(
                  isMovedOut
                      ? 'Reactivate'
                      : (hasRoom ? 'Unavailable' : 'Select'),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _getTenantsCountText() {
    final int availableCount =
        _filteredTenants
            .where((t) => t.roomId == null || t.status == TenantStatus.movedOut)
            .length;
    final int totalCount = _filteredTenants.length;

    if (_filteredTenants.isEmpty) {
      return 'No tenants found';
    } else if (availableCount == 0) {
      return '$totalCount tenant${totalCount != 1 ? 's' : ''} (none available)';
    } else {
      return '$availableCount available of $totalCount tenant${totalCount != 1 ? 's' : ''}';
    }
  }
}
