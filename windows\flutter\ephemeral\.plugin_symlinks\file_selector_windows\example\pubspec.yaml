name: example
description: Example for file_selector_windows implementation.
publish_to: 'none'
version: 1.0.0

environment:
  sdk: ^3.4.0
  flutter: ">=3.22.0"

dependencies:
  file_selector_platform_interface: ^2.6.0
  file_selector_windows:
    # When depending on this package from a real application you should use:
    #   file_selector_windows: ^x.y.z
    # See https://dart.dev/tools/pub/dependencies#version-constraints
    # The example app is bundled with the plugin so we use a path dependency on
    # the parent directory to use the current plugin's version.
    path: ..
  flutter:
    sdk: flutter

dev_dependencies:
  flutter_test:
    sdk: flutter

flutter:
  uses-material-design: true
