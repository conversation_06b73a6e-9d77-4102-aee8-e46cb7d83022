import 'package:flutter/material.dart';
import '../../utils/currency_formatter.dart';

class PaymentSummaryWidget extends StatelessWidget {
  final double totalPending;
  final double totalPaid;
  final double totalOverdue;
  final int pendingCount;
  final int paidCount;
  final int overdueCount;

  const PaymentSummaryWidget({
    super.key,
    required this.totalPending,
    required this.totalPaid,
    required this.totalOverdue,
    required this.pendingCount,
    required this.paidCount,
    required this.overdueCount,
  });

  @override
  Widget build(BuildContext context) {
    final totalAmount = totalPending + totalPaid + totalOverdue;
    final paidPercentage =
        totalAmount > 0 ? (totalPaid / totalAmount * 100) : 0;

    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Payment Summary',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),

            // Total amount
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text('Total Amount:'),
                Text(
                  CurrencyFormatter.formatCurrency(totalAmount),
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 8),

            // Paid amount
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text('Paid (${paidCount} bills):'),
                Text(
                  CurrencyFormatter.formatCurrency(totalPaid),
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Colors.green,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),

            // Pending amount
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text('Pending (${pendingCount} bills):'),
                Text(
                  CurrencyFormatter.formatCurrency(totalPending),
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Colors.orange,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),

            // Overdue amount
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text('Overdue (${overdueCount} bills):'),
                Text(
                  CurrencyFormatter.formatCurrency(totalOverdue),
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Colors.red,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Payment progress
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text('Payment Progress:'),
                    Text(
                      '${paidPercentage.toStringAsFixed(1)}%',
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: LinearProgressIndicator(
                    value: totalAmount > 0 ? totalPaid / totalAmount : 0,
                    minHeight: 10,
                    backgroundColor: Colors.grey[300],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
