import 'package:uuid/uuid.dart';
import 'bill.dart';

/// Model for bill templates that can be used to generate recurring bills
class BillTemplate {
  final String id;
  final String name;
  final String description;
  final BillType type;
  final RecurrenceType recurrence;
  final double amount;
  final int dueDayOfMonth; // Day of the month when bill is due (1-31)
  final String? propertyId;
  final bool applyToAllTenants; // Whether to apply to all tenants in a property
  final bool autoGenerate; // Whether to automatically generate bills
  final int daysBeforeDue; // How many days before due date to generate the bill
  final String? notes;
  final UtilityType? utilityType;
  final double? ratePerUnit;
  final bool includeInRent;
  final DateTime createdAt;
  final DateTime? updatedAt;

  BillTemplate({
    String? id,
    required this.name,
    required this.description,
    required this.type,
    required this.recurrence,
    required this.amount,
    required this.dueDayOfMonth,
    this.propertyId,
    this.applyToAllTenants = false,
    this.autoGenerate = false,
    this.daysBeforeDue = 7,
    this.notes,
    this.utilityType,
    this.ratePerUnit,
    this.includeInRent = false,
    DateTime? createdAt,
    this.updatedAt,
  })  : id = id ?? const Uuid().v4(),
        createdAt = createdAt ?? DateTime.now(),
        assert(dueDayOfMonth >= 1 && dueDayOfMonth <= 31, 'Due day must be between 1 and 31');

  // Create a BillTemplate from JSON data
  factory BillTemplate.fromJson(Map<String, dynamic> json) {
    return BillTemplate(
      id: json['id'],
      name: json['name'],
      description: json['description'],
      type: BillType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => BillType.other,
      ),
      recurrence: RecurrenceType.values.firstWhere(
        (e) => e.name == json['recurrence'],
        orElse: () => RecurrenceType.monthly,
      ),
      amount: (json['amount'] as num).toDouble(),
      dueDayOfMonth: json['due_day_of_month'],
      propertyId: json['property_id'],
      applyToAllTenants: json['apply_to_all_tenants'] ?? false,
      autoGenerate: json['auto_generate'] ?? false,
      daysBeforeDue: json['days_before_due'] ?? 7,
      notes: json['notes'],
      utilityType: json['utility_type'] != null
          ? UtilityType.values.firstWhere(
              (e) => e.name == json['utility_type'],
              orElse: () => UtilityType.other,
            )
          : null,
      ratePerUnit: json['rate_per_unit']?.toDouble(),
      includeInRent: json['include_in_rent'] ?? false,
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: json['updated_at'] != null
          ? DateTime.parse(json['updated_at'])
          : null,
    );
  }

  // Convert BillTemplate to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'type': type.name,
      'recurrence': recurrence.name,
      'amount': amount,
      'due_day_of_month': dueDayOfMonth,
      'property_id': propertyId,
      'apply_to_all_tenants': applyToAllTenants,
      'auto_generate': autoGenerate,
      'days_before_due': daysBeforeDue,
      'notes': notes,
      'utility_type': utilityType?.name,
      'rate_per_unit': ratePerUnit,
      'include_in_rent': includeInRent,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  // Create a copy of BillTemplate with updated fields
  BillTemplate copyWith({
    String? name,
    String? description,
    BillType? type,
    RecurrenceType? recurrence,
    double? amount,
    int? dueDayOfMonth,
    String? propertyId,
    bool? applyToAllTenants,
    bool? autoGenerate,
    int? daysBeforeDue,
    String? notes,
    UtilityType? utilityType,
    double? ratePerUnit,
    bool? includeInRent,
    DateTime? updatedAt,
  }) {
    return BillTemplate(
      id: id,
      name: name ?? this.name,
      description: description ?? this.description,
      type: type ?? this.type,
      recurrence: recurrence ?? this.recurrence,
      amount: amount ?? this.amount,
      dueDayOfMonth: dueDayOfMonth ?? this.dueDayOfMonth,
      propertyId: propertyId ?? this.propertyId,
      applyToAllTenants: applyToAllTenants ?? this.applyToAllTenants,
      autoGenerate: autoGenerate ?? this.autoGenerate,
      daysBeforeDue: daysBeforeDue ?? this.daysBeforeDue,
      notes: notes ?? this.notes,
      utilityType: utilityType ?? this.utilityType,
      ratePerUnit: ratePerUnit ?? this.ratePerUnit,
      includeInRent: includeInRent ?? this.includeInRent,
      createdAt: createdAt,
      updatedAt: updatedAt ?? DateTime.now(),
    );
  }

  // Generate a due date for a given month and year
  DateTime generateDueDate(int month, int year) {
    // Handle month with fewer days than the due day
    int maxDaysInMonth = DateTime(year, month + 1, 0).day;
    int actualDueDay = dueDayOfMonth > maxDaysInMonth ? maxDaysInMonth : dueDayOfMonth;
    
    return DateTime(year, month, actualDueDay);
  }

  // Generate a bill from this template for a specific tenant
  Bill generateBill({
    required String tenantId,
    String? roomId,
    DateTime? customDueDate,
    double? customAmount,
    String? customDescription,
    double? roomRentalPrice, // Add room rental price parameter
    double? propertyUtilityRate, // Add property utility rate parameter
  }) {
    final dueDate = customDueDate ?? generateNextDueDate();
    
    // For rent bills, use the room's rental price instead of template amount
    double billAmount;
    if (type == BillType.rent && roomRentalPrice != null) {
      billAmount = roomRentalPrice;
    } else {
      billAmount = customAmount ?? amount;
    }
    
    // For utility bills, use property utility rate if available
    double? effectiveRatePerUnit = ratePerUnit;
    if (type == BillType.utility && propertyUtilityRate != null) {
      effectiveRatePerUnit = propertyUtilityRate;
    }

    return Bill(
      title: name,
      description: customDescription ?? description,
      amount: billAmount,
      dueDate: dueDate,
      type: type,
      recurrence: recurrence,
      tenantId: tenantId,
      propertyId: propertyId,
      roomId: roomId,
      notes: notes,
      includeInRent: includeInRent,
      utilityType: utilityType,
      ratePerUnit: effectiveRatePerUnit,
    );
  }

  // Generate the next appropriate due date (ensuring it's in the future)
  DateTime generateNextDueDate() {
    final now = DateTime.now();
    final currentMonth = now.month;
    final currentYear = now.year;
    
    // First, try the current month
    DateTime candidateDate = generateDueDate(currentMonth, currentYear);
    
    // If the due date has already passed this month (or is today and we're past the generation window),
    // generate for next month instead
    final generateWindow = candidateDate.subtract(Duration(days: daysBeforeDue));
    
    if (candidateDate.isBefore(now) || (now.isAfter(generateWindow) && candidateDate.day == now.day)) {
      // Move to next month
      if (currentMonth == 12) {
        candidateDate = generateDueDate(1, currentYear + 1);
      } else {
        candidateDate = generateDueDate(currentMonth + 1, currentYear);
      }
    }
    
    return candidateDate;
  }
} 