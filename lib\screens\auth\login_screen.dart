import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../services/service_locator.dart';
import '../../utils/validators.dart';
import '../../widgets/ui_components.dart';
import 'signup_screen.dart';
import 'forgot_password_screen.dart';
import '../legal/terms_and_conditions_screen.dart';
import '../legal/privacy_policy_screen.dart';

class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => LoginScreenState();
}

class LoginScreenState extends State<LoginScreen> {
  final _formKey = GlobalKey<FormState>();
  bool _isLoading = false;
  bool _passwordVisible = false;
  bool _rememberMe = true;
  bool _useOTP = false; // Toggle between password and OTP login
  bool _otpSent = false; // Track if <PERSON><PERSON> has been sent
  bool _isOtpLoading = false; // Loading state for OTP request

  // Controllers
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _otpController = TextEditingController();

  @override
  void initState() {
    super.initState();
    // Try to load saved email if any
    _loadSavedEmail();
  }

  Future<void> _loadSavedEmail() async {
    // This would typically use SharedPreferences or another storage mechanism
    // For demo purposes, we're not implementing actual storage logic
  }

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    _otpController.dispose();
    super.dispose();
  }

  // Handle login
  Future<void> _handleLogin() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // Save email if remember me is checked
      if (_rememberMe) {
        // This would typically use SharedPreferences or another storage mechanism
        // For demo purposes, we're not implementing actual storage logic
      }

      final response = await serviceLocator.authService.signInWithPassword(
        email: _emailController.text.trim(),
        password: _passwordController.text,
      );

      if (mounted) {
        if (response.user != null) {
          showSuccessMessage(context, 'Login successful!');
          Navigator.of(context).pushNamedAndRemoveUntil('/dashboard', (route) => false);
        } else {
          showErrorMessage(context, 'Failed to login. Please check your credentials.');
        }
      }
    } on AuthException catch (error) {
      if (mounted) {
        showErrorMessage(context, error.message);
      }
    } catch (error) {
      if (mounted) {
        showErrorMessage(
          context,
          'Login error: ${error.toString()}',
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // Handle OTP request
  Future<void> _handleRequestOTP() async {
    if (_emailController.text.trim().isEmpty) {
      showErrorMessage(context, 'Please enter your email address');
      return;
    }

    // Validate email format
    final emailError = Validators.validateEmail(_emailController.text.trim());
    if (emailError != null) {
      showErrorMessage(context, emailError);
      return;
    }

    setState(() {
      _isOtpLoading = true;
    });

    try {
      await serviceLocator.authService.requestEmailOTP(
        email: _emailController.text.trim(),
      );

      if (mounted) {
        setState(() {
          _otpSent = true;
        });
        showSuccessMessage(context, 'OTP sent to your email!');
      }
    } on AuthException catch (error) {
      if (mounted) {
        showErrorMessage(context, error.message);
      }
    } catch (error) {
      if (mounted) {
        showErrorMessage(
          context,
          'Failed to send OTP: ${error.toString()}',
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isOtpLoading = false;
        });
      }
    }
  }

  // Handle OTP verification
  Future<void> _handleVerifyOTP() async {
    if (_otpController.text.trim().isEmpty) {
      showErrorMessage(context, 'Please enter the OTP code');
      return;
    }

    if (_otpController.text.trim().length != 6) {
      showErrorMessage(context, 'OTP must be 6 digits');
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final response = await serviceLocator.authService.verifyEmailOTP(
        email: _emailController.text.trim(),
        token: _otpController.text.trim(),
        type: OtpType.magiclink,
      );

      if (mounted) {
        if (response.user != null) {
          showSuccessMessage(context, 'Login successful!');
          Navigator.of(context).pushNamedAndRemoveUntil('/dashboard', (route) => false);
        } else {
          showErrorMessage(context, 'Failed to verify OTP. Please check your code.');
        }
      }
    } on AuthException catch (error) {
      if (mounted) {
        showErrorMessage(context, error.message);
      }
    } catch (error) {
      if (mounted) {
        showErrorMessage(
          context,
          'OTP verification error: ${error.toString()}',
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Login'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: Center(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(24.0),
          child: Form(
            key: _formKey,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Image.asset(
                  'assets/images/Shadow-Home.png',
                  height: 80,
                  width: 80,
                ),
                const SizedBox(height: 24),
                const Text(
                  'Welcome Back',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 32),

                // Email
                CustomTextField(
                  controller: _emailController,
                  labelText: 'Email',
                  prefixIcon: Icons.email,
                  keyboardType: TextInputType.emailAddress,
                  validator: Validators.validateEmail,
                  textInputAction: TextInputAction.next,
                ),
                const SizedBox(height: 16),

// Password or OTP
                if (!_useOTP) ...[
                  CustomTextField(
                    controller: _passwordController,
                    labelText: 'Password',
                    prefixIcon: Icons.lock,
                    suffixIcon: _passwordVisible 
                        ? Icons.visibility 
                        : Icons.visibility_off,
                    onSuffixIconPressed: () {
                      setState(() {
                        _passwordVisible = !_passwordVisible;
                      });
                    },
                    obscureText: !_passwordVisible,
                    validator: Validators.validatePassword,
                    textInputAction: TextInputAction.done,
                    onFieldSubmitted: (_) => _handleLogin(),
                  ),
                  const SizedBox(height: 16),
                ] else ...[
                  if (!_otpSent) ...[
                    // Initial OTP request button
                    CustomButton(
                      text: 'Request OTP',
                      onPressed: _handleRequestOTP,
                      isLoading: _isOtpLoading,
                    ),
                    const SizedBox(height: 16),
                  ] else ...[
                    // OTP input and verify button after OTP is sent
                    OTPInputField(
                      controller: _otpController,
                      onChanged: (val) {},
                      validator: (val) {
                        if (val == null || val.length != 6) {
                          return 'Please enter a 6-digit OTP';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 16),
                    CustomButton(
                      text: 'Verify OTP',
                      onPressed: _handleVerifyOTP,
                      isLoading: _isLoading,
                    ),
                    const SizedBox(height: 8),
                    TextButton(
                      onPressed: _isOtpLoading ? null : _handleRequestOTP,
                      child: Text(
                        'Resend OTP',
                        style: TextStyle(
                          color: _isOtpLoading ? Colors.grey : Theme.of(context).primaryColor,
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),
                  ],
                ],

                // Remember me option (only for password login)
                if (!_useOTP) Row(
                  children: [
                    Checkbox(
                      value: _rememberMe,
                      onChanged: (value) {
                        setState(() {
                          _rememberMe = value ?? false;
                        });
                      },
                    ),
                    const Text('Remember me'),
                    const Spacer(),
                    // Forgot Password
                    TextButton(
                      onPressed: () {
                        Navigator.of(context).push(
                          MaterialPageRoute(
                            builder: (context) => const ForgotPasswordScreen(),
                          ),
                        );
                      },
                      child: const Text('Forgot Password?'),
                    ),
                  ],
                ),
                const SizedBox(height: 16),

                // Login Mode Toggle
                TextButton(
                  onPressed: () {
                    setState(() {
                      _useOTP = !_useOTP;
                      _otpSent = false;
                      _otpController.clear();
                    });
                  },
                  child: Text(
                    _useOTP ? 'Use Password Instead' : 'Login with OTP',
                    style: TextStyle(
                      color: Theme.of(context).primaryColor,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                const SizedBox(height: 16),

                // Login Button (only for password login)
                if (!_useOTP) ...[  
                  CustomButton(
                    text: 'Login',
                    onPressed: _handleLogin,
                    isLoading: _isLoading,
                  ),
                  const SizedBox(height: 16),
                ],

                // Sign Up
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Text('Don\'t have an account?'),
                    TextButton(
                      onPressed: () {
                        Navigator.of(context).push(
                          MaterialPageRoute(
                            builder: (context) => const SignUpScreen(),
                          ),
                        );
                      },
                      child: const Text('Sign Up'),
                    ),
                  ],
                ),

                const SizedBox(height: 32),

                // Legal Links Footer
                Container(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  child: Column(
                    children: [
                      Text(
                        'By using Tenanta, you agree to our',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey.shade600,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 8),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          TextButton(
                            onPressed: () {
                              Navigator.of(context).push(
                                MaterialPageRoute(
                                  builder: (context) => const TermsAndConditionsScreen(),
                                ),
                              );
                            },
                            style: TextButton.styleFrom(
                              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                              minimumSize: Size.zero,
                              tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                            ),
                            child: Text(
                              'Terms & Conditions',
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.blue.shade600,
                                decoration: TextDecoration.underline,
                              ),
                            ),
                          ),
                          Text(
                            ' and ',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey.shade600,
                            ),
                          ),
                          TextButton(
                            onPressed: () {
                              Navigator.of(context).push(
                                MaterialPageRoute(
                                  builder: (context) => const PrivacyPolicyScreen(),
                                ),
                              );
                            },
                            style: TextButton.styleFrom(
                              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                              minimumSize: Size.zero,
                              tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                            ),
                            child: Text(
                              'Privacy Policy',
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.blue.shade600,
                                decoration: TextDecoration.underline,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}