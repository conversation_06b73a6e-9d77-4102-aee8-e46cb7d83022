name: app_links
description: Android App Links, Deep Links, iOs Universal Links and Custom URL schemes handler for Flutter (desktop included).
version: 6.4.0
homepage: https://github.com/llfbandit/app_links

environment:
  sdk: ^3.3.0
  flutter: ">=3.19.0"

dependencies:
  flutter:
    sdk: flutter
  
  app_links_linux: ^1.0.3
  app_links_platform_interface: ^2.0.2
  app_links_web: ^1.0.4

dev_dependencies:
  flutter_lints: ^5.0.0

flutter:
  plugin:
    platforms:
      android:
        package: com.llfbandit.app_links
        pluginClass: AppLinksPlugin
      ios:
        pluginClass: AppLinksIosPlugin
      linux:
        default_package: app_links_linux
      macos:
        pluginClass: AppLinksMacosPlugin
      web:
        default_package: app_links_web
      windows:
        pluginClass: AppLinksPluginCApi

topics:
  - deeplink
  - app-links
  - universal-links
  - custom-url-schemes
  - web-to-app
