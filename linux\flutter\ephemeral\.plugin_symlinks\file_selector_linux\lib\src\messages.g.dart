// Copyright 2013 The Flutter Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.
// Autogenerated from Pig<PERSON> (v22.6.2), do not edit directly.
// See also: https://pub.dev/packages/pigeon
// ignore_for_file: public_member_api_docs, non_constant_identifier_names, avoid_as, unused_import, unnecessary_parenthesis, prefer_null_aware_operators, omit_local_variable_types, unused_shown_name, unnecessary_import, no_leading_underscores_for_local_identifiers

import 'dart:async';
import 'dart:typed_data' show Float<PERSON><PERSON>ist, Int32List, Int64List, Uint8List;

import 'package:flutter/foundation.dart' show ReadBuffer, WriteBuffer;
import 'package:flutter/services.dart';

PlatformException _createConnectionError(String channelName) {
  return PlatformException(
    code: 'channel-error',
    message: 'Unable to establish connection on channel: "$channelName".',
  );
}

/// A Pigeon representation of the GTK_FILE_CHOOSER_ACTION_* options.
enum PlatformFileChooserActionType {
  open,
  chooseDirectory,
  save,
}

/// A Pigeon representation of the Linux portion of an `XTypeGroup`.
class PlatformTypeGroup {
  PlatformTypeGroup({
    this.label = '',
    this.extensions = const <String>[],
    this.mimeTypes = const <String>[],
  });

  String label;

  List<String> extensions;

  List<String> mimeTypes;

  Object encode() {
    return <Object?>[
      label,
      extensions,
      mimeTypes,
    ];
  }

  static PlatformTypeGroup decode(Object result) {
    result as List<Object?>;
    return PlatformTypeGroup(
      label: result[0]! as String,
      extensions: (result[1] as List<Object?>?)!.cast<String>(),
      mimeTypes: (result[2] as List<Object?>?)!.cast<String>(),
    );
  }
}

/// Options for GKT file chooser.
///
/// These correspond to gtk_file_chooser_set_* options.
class PlatformFileChooserOptions {
  PlatformFileChooserOptions({
    this.allowedFileTypes,
    this.currentFolderPath,
    this.currentName,
    this.acceptButtonLabel,
    this.selectMultiple,
  });

  List<PlatformTypeGroup>? allowedFileTypes;

  String? currentFolderPath;

  String? currentName;

  String? acceptButtonLabel;

  /// Whether to allow multiple file selection.
  ///
  /// Nullable because it does not apply to the "save" action.
  bool? selectMultiple;

  Object encode() {
    return <Object?>[
      allowedFileTypes,
      currentFolderPath,
      currentName,
      acceptButtonLabel,
      selectMultiple,
    ];
  }

  static PlatformFileChooserOptions decode(Object result) {
    result as List<Object?>;
    return PlatformFileChooserOptions(
      allowedFileTypes:
          (result[0] as List<Object?>?)?.cast<PlatformTypeGroup>(),
      currentFolderPath: result[1] as String?,
      currentName: result[2] as String?,
      acceptButtonLabel: result[3] as String?,
      selectMultiple: result[4] as bool?,
    );
  }
}

class _PigeonCodec extends StandardMessageCodec {
  const _PigeonCodec();
  @override
  void writeValue(WriteBuffer buffer, Object? value) {
    if (value is int) {
      buffer.putUint8(4);
      buffer.putInt64(value);
    } else if (value is PlatformFileChooserActionType) {
      buffer.putUint8(129);
      writeValue(buffer, value.index);
    } else if (value is PlatformTypeGroup) {
      buffer.putUint8(130);
      writeValue(buffer, value.encode());
    } else if (value is PlatformFileChooserOptions) {
      buffer.putUint8(131);
      writeValue(buffer, value.encode());
    } else {
      super.writeValue(buffer, value);
    }
  }

  @override
  Object? readValueOfType(int type, ReadBuffer buffer) {
    switch (type) {
      case 129:
        final int? value = readValue(buffer) as int?;
        return value == null
            ? null
            : PlatformFileChooserActionType.values[value];
      case 130:
        return PlatformTypeGroup.decode(readValue(buffer)!);
      case 131:
        return PlatformFileChooserOptions.decode(readValue(buffer)!);
      default:
        return super.readValueOfType(type, buffer);
    }
  }
}

class FileSelectorApi {
  /// Constructor for [FileSelectorApi].  The [binaryMessenger] named argument is
  /// available for dependency injection.  If it is left null, the default
  /// BinaryMessenger will be used which routes to the host platform.
  FileSelectorApi(
      {BinaryMessenger? binaryMessenger, String messageChannelSuffix = ''})
      : pigeonVar_binaryMessenger = binaryMessenger,
        pigeonVar_messageChannelSuffix =
            messageChannelSuffix.isNotEmpty ? '.$messageChannelSuffix' : '';
  final BinaryMessenger? pigeonVar_binaryMessenger;

  static const MessageCodec<Object?> pigeonChannelCodec = _PigeonCodec();

  final String pigeonVar_messageChannelSuffix;

  /// Shows an file chooser with the given [type] and [options], returning the
  /// list of selected paths.
  ///
  /// An empty list corresponds to a cancelled selection.
  Future<List<String>> showFileChooser(PlatformFileChooserActionType type,
      PlatformFileChooserOptions options) async {
    final String pigeonVar_channelName =
        'dev.flutter.pigeon.file_selector_linux.FileSelectorApi.showFileChooser$pigeonVar_messageChannelSuffix';
    final BasicMessageChannel<Object?> pigeonVar_channel =
        BasicMessageChannel<Object?>(
      pigeonVar_channelName,
      pigeonChannelCodec,
      binaryMessenger: pigeonVar_binaryMessenger,
    );
    final List<Object?>? pigeonVar_replyList = await pigeonVar_channel
        .send(<Object?>[type, options]) as List<Object?>?;
    if (pigeonVar_replyList == null) {
      throw _createConnectionError(pigeonVar_channelName);
    } else if (pigeonVar_replyList.length > 1) {
      throw PlatformException(
        code: pigeonVar_replyList[0]! as String,
        message: pigeonVar_replyList[1] as String?,
        details: pigeonVar_replyList[2],
      );
    } else if (pigeonVar_replyList[0] == null) {
      throw PlatformException(
        code: 'null-error',
        message: 'Host platform returned null value for non-null return value.',
      );
    } else {
      return (pigeonVar_replyList[0] as List<Object?>?)!.cast<String>();
    }
  }
}
