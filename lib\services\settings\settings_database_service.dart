import 'package:supabase_flutter/supabase_flutter.dart';
import '../../models/settings/settings_model.dart';
import '../../utils/logger.dart';

class SettingsDatabaseService {
  final SupabaseClient _client;

  // Database table name
  static const String _tableName = 'user_settings';

  SettingsDatabaseService(this._client);

  // Get user settings from the database
  Future<UserSettings?> getUserSettings(String userId) async {
    try {
      AppLogger.debug('Fetching settings for user: $userId');
      final response =
          await _client.from(_tableName).select().eq('id', userId).single();
      AppLogger.debug('Settings fetch response: $response');
      return UserSettings.fromJson(response);
    } catch (e) {
      AppLogger.error('Error fetching user settings: $e');
      return null;
    }
  }

  // Save user settings to the database (creates or updates)
  Future<bool> saveUserSettings(UserSettings settings) async {
    try {
      AppLogger.debug('Saving settings for user: ${settings.id}');
      AppLogger.debug('Settings data: ${settings.toJson()}');

      // Upsert - insert if not exists, update if exists
      final response =
          await _client.from(_tableName).upsert(settings.toJson()).select();
      AppLogger.debug('Settings save response: $response');
      if ((response as List).isEmpty) {
        AppLogger.warning(
          'Settings save returned empty list for user: ${settings.id}',
        );
        return false;
      }
      return true;
    } catch (e) {
      AppLogger.error('Error saving user settings: $e');
      // Print more detailed error information
      if (e is PostgrestException) {
        AppLogger.error('PostgrestException code: ${e.code}');
        AppLogger.error('PostgrestException message: ${e.message}');
        AppLogger.error('PostgrestException details: ${e.details}');
      }
      return false;
    }
  }

  // Update specific setting fields
  Future<bool> updateCurrency(String userId, String currencyCode) async {
    try {
      AppLogger.debug('Updating currency for user: $userId to $currencyCode');
      final response = await _client
          .from(_tableName)
          .update({
            'currency_code': currencyCode,
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', userId);
      AppLogger.debug('Currency update response: $response');
      if ((response as List).isEmpty) {
        AppLogger.warning(
          'Currency update returned empty list for user: $userId',
        );
        return false;
      }
      return true;
    } catch (e) {
      AppLogger.error('Error updating currency: $e');
      return false;
    }
  }

  Future<bool> updateLanguage(String userId, String languageCode) async {
    try {
      AppLogger.debug('Updating language for user: $userId to $languageCode');
      final response = await _client
          .from(_tableName)
          .update({
            'language_code': languageCode,
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', userId);
      AppLogger.debug('Language update response: $response');
      if ((response as List).isEmpty) {
        AppLogger.warning(
          'Language update returned empty list for user: $userId',
        );
        return false;
      }
      return true;
    } catch (e) {
      AppLogger.error('Error updating language: $e');
      return false;
    }
  }

  // Delete user settings (e.g., when user logs out or deletes account)
  Future<bool> deleteUserSettings(String userId) async {
    try {
      await _client.from(_tableName).delete().eq('id', userId);
      return true;
    } catch (e) {
      AppLogger.error('Error deleting user settings: $e');
      return false;
    }
  }

  // Check if the user_settings table exists
  Future<bool> checkIfTableExists() async {
    try {
      // Try to query the table
      await _client.from(_tableName).select('id').limit(1);
      return true;
    } catch (e) {
      // If we get an error, the table likely doesn't exist
      return false;
    }
  }
}
