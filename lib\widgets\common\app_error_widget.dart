import 'package:flutter/material.dart';
import '../../utils/logger.dart';

/// A widget that displays an error message with retry functionality
class AppErrorWidget extends StatelessWidget {
  /// The error message to display
  final String message;

  /// The action to perform when the retry button is pressed
  final VoidCallback? onRetry;

  /// Whether to show a retry button
  final bool showRetryButton;

  /// Additional actions to display below the error message
  final List<Widget>? additionalActions;

  /// The title of the error widget
  final String title;

  /// Creates an AppErrorWidget
  const AppErrorWidget({
    super.key,
    required this.message,
    this.onRetry,
    this.showRetryButton = true,
    this.additionalActions,
    this.title = 'Error',
  });

  /// Creates an AppErrorWidget from an exception
  factory AppErrorWidget.fromException(
    dynamic exception, {
    Key? key,
    VoidCallback? onRetry,
    bool showRetryButton = true,
    List<Widget>? additionalActions,
    String title = 'Error',
  }) {
    // Log the exception
    AppLogger.error('Error in AppErrorWidget: $exception');

    // Create a user-friendly message
    final String message = _getUserFriendlyMessage(exception);

    return AppErrorWidget(
      key: key,
      message: message,
      onRetry: onRetry,
      showRetryButton: showRetryButton,
      additionalActions: additionalActions,
      title: title,
    );
  }

  /// Converts an exception to a user-friendly message
  static String _getUserFriendlyMessage(dynamic exception) {
    if (exception == null) {
      return 'An unknown error occurred';
    }

    // Handle common exceptions with user-friendly messages
    if (exception.toString().contains('connection failed')) {
      return 'Unable to connect to the server. Please check your internet connection and try again.';
    } else if (exception.toString().contains('authentication')) {
      return 'Authentication error. Please sign in again.';
    } else if (exception.toString().contains('permission')) {
      return 'You don\'t have permission to perform this action.';
    } else if (exception.toString().contains('timeout')) {
      return 'The operation timed out. Please try again.';
    } else if (exception.toString().contains('already initialized')) {
      return 'The application is already running. Please restart the app if you continue to see this message.';
    }

    // Return a generic message for other exceptions
    return exception.toString();
  }

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, color: Colors.red, size: 48),
            const SizedBox(height: 16),
            Text(
              title,
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Text(
              message,
              textAlign: TextAlign.center,
              style: TextStyle(color: Colors.grey[700]),
            ),
            const SizedBox(height: 16),
            if (showRetryButton && onRetry != null)
              ElevatedButton(onPressed: onRetry, child: const Text('Retry')),
            if (additionalActions != null) ...additionalActions!,
          ],
        ),
      ),
    );
  }
}
