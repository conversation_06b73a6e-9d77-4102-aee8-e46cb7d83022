import 'package:flutter/material.dart';
import 'network_error_card.dart';

/// Example usage of the NetworkErrorCard widget
class NetworkErrorCardExample extends StatelessWidget {
  const NetworkErrorCardExample({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Network Error Example'),
      ),
      body: Center(
        child: NetworkErrorCard(
          onRetry: () {
            // Implement your retry logic here
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Retrying connection...'),
              ),
            );
          },
        ),
      ),
    );
  }
} 