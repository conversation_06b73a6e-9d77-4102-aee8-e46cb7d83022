import 'package:flutter/material.dart';
import '../../models/tenant/tenant.dart';
import '../../models/room/room_model.dart';
import '../../models/activity_log.dart';
import '../../services/service_locator.dart';
import '../../widgets/app_loading_indicator.dart';

class VacateRoomPage extends StatefulWidget {
  final Tenant tenant;
  final String roomName;

  const VacateRoomPage({
    super.key,
    required this.tenant,
    required this.roomName,
  });

  @override
  State<VacateRoomPage> createState() => _VacateRoomPageState();
}

class _VacateRoomPageState extends State<VacateRoomPage> {
  final _formKey = GlobalKey<FormState>();
  bool _isLoading = false;
  String? _errorMessage;

  // Form controllers
  final _vacateNoticeController = TextEditingController();
  final _vacateDateController = TextEditingController();
  final _notesController = TextEditingController();

  // Selected date for vacating
  DateTime? _selectedVacateDate;

  // Option to terminate lease
  bool _terminateLease = true;

  @override
  void initState() {
    super.initState();
    // Set default vacate date to 30 days from now
    _selectedVacateDate = DateTime.now().add(const Duration(days: 30));
    _updateVacateDateText();
  }

  @override
  void dispose() {
    _vacateNoticeController.dispose();
    _vacateDateController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  void _updateVacateDateText() {
    if (_selectedVacateDate != null) {
      _vacateDateController.text =
          '${_selectedVacateDate!.year}-'
          '${_selectedVacateDate!.month.toString().padLeft(2, '0')}-'
          '${_selectedVacateDate!.day.toString().padLeft(2, '0')}';
    }
  }

  Future<void> _selectVacateDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate:
          _selectedVacateDate ?? DateTime.now().add(const Duration(days: 30)),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      helpText: 'Select Vacate Date',
    );

    if (picked != null && picked != _selectedVacateDate) {
      setState(() {
        _selectedVacateDate = picked;
        _updateVacateDateText();
      });
    }
  }

  Future<void> _submitVacateNotice() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    // Store navigator for later use
    final navigator = Navigator.of(context);

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // Generate vacate notice text
      final noticeText =
          'Vacate Notice: ${_vacateNoticeController.text}\n'
          'Planned Vacate Date: ${_vacateDateController.text}\n'
          'Notice Date: ${DateTime.now().toString().split(' ')[0]}\n'
          'Will Terminate Lease: ${_terminateLease ? 'Yes' : 'No'}\n'
          'Additional Notes: ${_notesController.text}';

      // Update tenant notes with vacate notice
      final existingNotes = widget.tenant.notes ?? '';
      final updatedNotes =
          '$existingNotes\n\n--- VACATE NOTICE ---\n$noticeText';

      // Update tenant in database
      final updatedTenant = widget.tenant.copyWith(
        notes: updatedNotes,
        updatedAt: DateTime.now(),
      );

      await serviceLocator.tenantService.updateTenant(updatedTenant);

      // Log the vacate notice
      await serviceLocator.activityLogService.logActivity(
        ActivityLog(
          type: ActivityType.vacateNotice,
          tenantId: widget.tenant.id,
          roomId: widget.tenant.roomId!,
          action: 'Vacate notice submitted',
          details: {
            'tenant_name':
                '${widget.tenant.firstName} ${widget.tenant.lastName}',
            'room_name': widget.roomName,
            'notice_text': _vacateNoticeController.text,
            'planned_vacate_date': _vacateDateController.text,
            'notice_date': DateTime.now().toString().split(' ')[0],
            'will_terminate_lease': _terminateLease,
          },
        ),
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Vacate notice submitted successfully'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 3),
          ),
        );

        // Return to previous screen using stored navigator
        navigator.pop(updatedTenant);
      }
    } catch (e) {
      if (!mounted) return;

      setState(() {
        _isLoading = false;
        _errorMessage = 'Failed to submit vacate notice: $e';
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error submitting vacate notice: ${e.toString()}'),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 5),
          action: SnackBarAction(
            label: 'Retry',
            onPressed: _submitVacateNotice,
            textColor: Colors.white,
          ),
        ),
      );
    }
  }

  Future<void> _vacateNow() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    // Capture context-dependent variables before async gap
    final scaffoldMessenger = ScaffoldMessenger.of(context);
    final navigator = Navigator.of(context);

    final bool? confirmed = await showDialog<bool>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Confirm Immediate Vacate'),
            content: Text(
              'Are you sure you want to mark ${widget.tenant.firstName} ${widget.tenant.lastName} as moved out immediately?',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () => Navigator.of(context).pop(true),
                child: const Text('Confirm'),
              ),
            ],
          ),
    );

    if (confirmed != true) {
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // Generate vacate record
      final vacateText =
          'IMMEDIATE VACATE\n'
          'Vacate Date: ${DateTime.now().toString().split(' ')[0]}\n'
          'Lease Terminated: ${_terminateLease ? 'Yes' : 'No'}\n'
          'Notes: ${_notesController.text}';

      // Update tenant notes with vacate information
      final existingNotes = widget.tenant.notes ?? '';
      final updatedNotes =
          '$existingNotes\n\n--- TENANT MOVED OUT ---\n$vacateText';

      // Update tenant in database - mark as moved out and remove room assignment
      final updatedTenant = widget.tenant.copyWith(
        status: TenantStatus.movedOut,
        roomId: null, // Remove room assignment
        // Only clear lease dates if terminating the lease
        leaseStartDate: _terminateLease ? null : widget.tenant.leaseStartDate,
        leaseEndDate: _terminateLease ? null : widget.tenant.leaseEndDate,
        notes: updatedNotes,
        updatedAt: DateTime.now(),
      );

      await serviceLocator.tenantService.updateTenant(updatedTenant);

      // Update room status to vacant
      if (widget.tenant.roomId != null) {
        await serviceLocator.roomService.updateRoomOccupancyStatus(
          widget.tenant.roomId!,
          RoomOccupancyStatus.vacant,
        );
      }

      // Log the vacate action
      await serviceLocator.activityLogService.logActivity(
        ActivityLog(
          type: ActivityType.tenantMovedOut,
          tenantId: widget.tenant.id,
          roomId: widget.tenant.roomId!,
          action: 'Tenant moved out',
          details: {
            'tenant_name':
                '${widget.tenant.firstName} ${widget.tenant.lastName}',
            'room_name': widget.roomName,
            'vacate_date': DateTime.now().toString().split(' ')[0],
            'lease_terminated': _terminateLease,
            'notes': _notesController.text,
          },
        ),
      );

      if (!mounted) return;

      scaffoldMessenger.showSnackBar(
        const SnackBar(
          content: Text('Tenant moved out successfully'),
          backgroundColor: Colors.green,
          duration: Duration(seconds: 3),
        ),
      );

      // Return to previous screen using stored navigator
      navigator.pop(updatedTenant);
    } catch (e) {
      if (!mounted) return;

      setState(() {
        _isLoading = false;
        _errorMessage = 'Failed to process vacate: $e';
      });

      scaffoldMessenger.showSnackBar(
        SnackBar(
          content: Text('Error processing vacate: ${e.toString()}'),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 5),
          action: SnackBarAction(
            label: 'Retry',
            onPressed: _vacateNow,
            textColor: Colors.white,
          ),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Vacate Room - ${widget.tenant.firstName} ${widget.tenant.lastName}',
        ),
      ),
      body:
          _isLoading
              ? const AppLoadingIndicator(message: 'Processing...')
              : SingleChildScrollView(
                padding: const EdgeInsets.all(16.0),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Current room info
                      Card(
                        elevation: 2,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text(
                                'Current Room Assignment',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 12),
                              Row(
                                children: [
                                  const Icon(
                                    Icons.meeting_room,
                                    size: 18,
                                    color: Colors.blue,
                                  ),
                                  const SizedBox(width: 8),
                                  Text(
                                    'Room: ${widget.roomName}',
                                    style: const TextStyle(fontSize: 16),
                                  ),
                                ],
                              ),
                              if (widget.tenant.leaseStartDate != null) ...[
                                const SizedBox(height: 8),
                                Row(
                                  children: [
                                    const Icon(
                                      Icons.calendar_today,
                                      size: 18,
                                      color: Colors.blue,
                                    ),
                                    const SizedBox(width: 8),
                                    Text(
                                      'Lease Start: ${widget.tenant.leaseStartDate!.toString().split(' ')[0]}',
                                      style: const TextStyle(fontSize: 16),
                                    ),
                                  ],
                                ),
                              ],
                              if (widget.tenant.leaseEndDate != null) ...[
                                const SizedBox(height: 8),
                                Row(
                                  children: [
                                    const Icon(
                                      Icons.event_busy,
                                      size: 18,
                                      color: Colors.blue,
                                    ),
                                    const SizedBox(width: 8),
                                    Text(
                                      'Lease End: ${widget.tenant.leaseEndDate!.toString().split(' ')[0]}',
                                      style: const TextStyle(fontSize: 16),
                                    ),
                                  ],
                                ),
                              ],
                            ],
                          ),
                        ),
                      ),

                      const SizedBox(height: 24),

                      // Vacate options
                      Card(
                        elevation: 2,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text(
                                'Vacate Notice',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 16),
                              TextFormField(
                                controller: _vacateNoticeController,
                                decoration: InputDecoration(
                                  labelText: 'Reason for Vacating',
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  prefixIcon: const Icon(Icons.description),
                                ),
                                maxLines: 3,
                                validator: (value) {
                                  if (value == null || value.isEmpty) {
                                    return 'Please enter a reason for vacating';
                                  }
                                  return null;
                                },
                              ),
                              const SizedBox(height: 16),
                              TextFormField(
                                controller: _vacateDateController,
                                decoration: InputDecoration(
                                  labelText: 'Planned Vacate Date',
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  prefixIcon: const Icon(Icons.calendar_today),
                                  suffixIcon: IconButton(
                                    icon: const Icon(Icons.date_range),
                                    onPressed: () => _selectVacateDate(context),
                                  ),
                                ),
                                readOnly: true,
                                validator: (value) {
                                  if (value == null || value.isEmpty) {
                                    return 'Please select a planned vacate date';
                                  }
                                  return null;
                                },
                              ),
                              const SizedBox(height: 16),
                              TextFormField(
                                controller: _notesController,
                                decoration: InputDecoration(
                                  labelText: 'Additional Notes',
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  prefixIcon: const Icon(Icons.note),
                                ),
                                maxLines: 3,
                              ),

                              // Add lease termination checkbox
                              if (widget.tenant.leaseStartDate != null ||
                                  widget.tenant.leaseEndDate != null)
                                Padding(
                                  padding: const EdgeInsets.only(top: 16),
                                  child: Row(
                                    children: [
                                      Checkbox(
                                        value: _terminateLease,
                                        onChanged: (value) {
                                          setState(() {
                                            _terminateLease = value ?? true;
                                          });
                                        },
                                      ),
                                      const SizedBox(width: 8),
                                      Expanded(
                                        child: Text(
                                          'Terminate lease (clears lease dates)',
                                          style: TextStyle(
                                            fontSize: 16,
                                            color: Colors.grey.shade800,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                            ],
                          ),
                        ),
                      ),

                      const SizedBox(height: 16),

                      // Error message
                      if (_errorMessage != null)
                        Padding(
                          padding: const EdgeInsets.only(bottom: 16.0),
                          child: Text(
                            _errorMessage!,
                            style: const TextStyle(color: Colors.red),
                          ),
                        ),

                      // Action buttons
                      Row(
                        children: [
                          Expanded(
                            child: ElevatedButton.icon(
                              onPressed: _submitVacateNotice,
                              icon: const Icon(Icons.notification_important),
                              label: const Text('Submit Vacate Notice'),
                              style: ElevatedButton.styleFrom(
                                padding: const EdgeInsets.symmetric(
                                  vertical: 12,
                                ),
                                backgroundColor: Colors.blue,
                              ),
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: ElevatedButton.icon(
                              onPressed: _vacateNow,
                              icon: const Icon(Icons.exit_to_app),
                              label: const Text('Vacate Now'),
                              style: ElevatedButton.styleFrom(
                                padding: const EdgeInsets.symmetric(
                                  vertical: 12,
                                ),
                                backgroundColor: Colors.red,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
    );
  }
}
