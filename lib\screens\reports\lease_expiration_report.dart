import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../widgets/navigation/app_drawer.dart';
import '../../utils/currency_formatter.dart';

class LeaseExpirationReport extends StatefulWidget {
  const LeaseExpirationReport({super.key});

  @override
  State<LeaseExpirationReport> createState() => _LeaseExpirationReportState();
}

class _LeaseExpirationReportState extends State<LeaseExpirationReport> {
  bool _isLoading = true;
  String? _error;
  List<Map<String, dynamic>> _tenants = [];
  String _searchQuery = '';
  String _selectedTimeframe = 'all';
  String? _selectedPropertyId;
  List<Map<String, dynamic>> _properties = [];
  
  @override
  void initState() {
    super.initState();
    _loadProperties();
    _loadData();
  }

  Future<void> _loadProperties() async {
    try {
      final client = Supabase.instance.client;
      
      final response = await client
          .from('properties')
          .select('id, name')
          .order('name');

      if (mounted) {
        setState(() {
          _properties = List<Map<String, dynamic>>.from(response);
        });
      }
    } catch (e) {
      // Handle error silently
    }
  }

  Future<void> _loadData() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      final client = Supabase.instance.client;
      final now = DateTime.now();
      
      // Base query to get tenants with lease information
      var query = client
          .from('tenants')
          .select('''
            id,
            first_name,
            last_name,
            email,
            phone_number,
            lease_start_date,
            lease_end_date,
            status,
            room_id,
            rooms:room_id (
              name,
              rental_price,
              property_id,
              properties:property_id (
                name
              )
            )
          ''')
          .eq('status', 'active')
          .not('lease_end_date', 'is', null);

      // Apply timeframe filter
      if (_selectedTimeframe == '30_days') {
        final thirtyDaysLater = now.add(const Duration(days: 30));
        query = query.lte('lease_end_date', thirtyDaysLater.toIso8601String());
        query = query.gte('lease_end_date', now.toIso8601String());
      } else if (_selectedTimeframe == '60_days') {
        final sixtyDaysLater = now.add(const Duration(days: 60));
        query = query.lte('lease_end_date', sixtyDaysLater.toIso8601String());
        query = query.gte('lease_end_date', now.toIso8601String());
      } else if (_selectedTimeframe == '90_days') {
        final ninetyDaysLater = now.add(const Duration(days: 90));
        query = query.lte('lease_end_date', ninetyDaysLater.toIso8601String());
        query = query.gte('lease_end_date', now.toIso8601String());
      }
      
      // Apply property filter
      if (_selectedPropertyId != null) {
        query = query.eq('rooms.property_id', _selectedPropertyId as Object);
      }
      
      // Execute the query
      final response = await query.order('lease_end_date');

      // Process the data to add calculated fields
      final List<Map<String, dynamic>> processedData = [];
      
      for (final tenant in response) {
        final leaseStartDate = tenant['lease_start_date'] != null 
            ? DateTime.parse(tenant['lease_start_date']) 
            : null;
        final leaseEndDate = tenant['lease_end_date'] != null 
            ? DateTime.parse(tenant['lease_end_date']) 
            : null;
        
        // Skip if no lease end date
        if (leaseEndDate == null) continue;
        
        // Calculate remaining time
        final daysRemaining = leaseEndDate.difference(now).inDays;
        final weeksRemaining = (daysRemaining / 7).floor();
        final monthsRemaining = (daysRemaining / 30).floor();
        
        // Calculate total lease duration
        int totalLeaseDays = 0;
        if (leaseStartDate != null) {
          totalLeaseDays = leaseEndDate.difference(leaseStartDate).inDays;
        }
        
        final totalLeaseWeeks = (totalLeaseDays / 7).floor();
        final totalLeaseMonths = (totalLeaseDays / 30).floor();
        
        // Add processed data
        final processedTenant = {
          ...tenant,
          'days_remaining': daysRemaining,
          'weeks_remaining': weeksRemaining,
          'months_remaining': monthsRemaining,
          'total_lease_days': totalLeaseDays,
          'total_lease_weeks': totalLeaseWeeks,
          'total_lease_months': totalLeaseMonths,
        };
        
        processedData.add(processedTenant);
      }
      
      // Apply search query filter
      final filteredData = processedData.where((tenant) {
        if (_searchQuery.isEmpty) return true;
        
        final searchLower = _searchQuery.toLowerCase();
        final firstName = tenant['first_name']?.toString().toLowerCase() ?? '';
        final lastName = tenant['last_name']?.toString().toLowerCase() ?? '';
        final fullName = '$firstName $lastName';
        final roomName = tenant['rooms']?['name']?.toString().toLowerCase() ?? '';
        final propertyName = tenant['rooms']?['properties']?['name']?.toString().toLowerCase() ?? '';
        
        return fullName.contains(searchLower) || 
               roomName.contains(searchLower) || 
               propertyName.contains(searchLower);
      }).toList();

      setState(() {
        _tenants = filteredData;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  String _formatDate(String? dateStr) {
    if (dateStr == null) return 'N/A';
    try {
      final date = DateTime.parse(dateStr);
      return DateFormat('MMM d, y').format(date);
    } catch (e) {
      return 'Invalid date';
    }
  }

  String _getRemainingTimeLabel(Map<String, dynamic> tenant) {
    final daysRemaining = tenant['days_remaining'] as int;
    
    if (daysRemaining < 0) {
      return 'Expired ${-daysRemaining} days ago';
    }
    
    if (daysRemaining == 0) {
      return 'Expires today';
    }
    
    if (daysRemaining < 7) {
      return '$daysRemaining days remaining';
    }
    
    if (daysRemaining < 30) {
      final weeks = tenant['weeks_remaining'] as int;
      return '$weeks weeks remaining';
    }
    
    final months = tenant['months_remaining'] as int;
    final remainingDays = daysRemaining - (months * 30);
    
    if (months > 0) {
      if (remainingDays > 15) {
        return '${months + 1} months remaining';
      }
      return '$months months remaining';
    }
    
    return '$daysRemaining days remaining';
  }

  Color _getStatusColor(Map<String, dynamic> tenant) {
    final daysRemaining = tenant['days_remaining'] as int;
    
    if (daysRemaining < 0) {
      return Colors.red[900]!;
    }
    
    if (daysRemaining <= 7) {
      return Colors.red;
    }
    
    if (daysRemaining <= 30) {
      return Colors.orange;
    }
    
    if (daysRemaining <= 60) {
      return Colors.amber;
    }
    
    return Colors.green;
  }
  
  void _showContactInfo(String? phoneNumber, String? email) {
    if (!mounted) return;
    
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Contact Information'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (phoneNumber != null && phoneNumber.isNotEmpty) ...[
                const Text(
                  'Phone Number:',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                SelectableText(phoneNumber),
                const SizedBox(height: 16),
              ],
              if (email != null && email.isNotEmpty) ...[
                const Text(
                  'Email:',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                SelectableText(email),
              ],
              if ((phoneNumber == null || phoneNumber.isEmpty) && 
                  (email == null || email.isEmpty))
                const Text('No contact information available'),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Close'),
            ),
          ],
        );
      },
    );
  }
  
  Future<void> _renewLease(Map<String, dynamic> tenant) async {
    if (!mounted) return;

    final tenantId = tenant['id'];
    final tenantName = '${tenant['first_name']} ${tenant['last_name']}';
    final currentLeaseEndDate = tenant['lease_end_date'] != null 
        ? DateTime.parse(tenant['lease_end_date']) 
        : DateTime.now();
    
    // Default new lease end date to 1 year from current end date
    DateTime newLeaseEndDate = currentLeaseEndDate.add(const Duration(days: 365));
    
    final bool? shouldRenew = await showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: Text('Renew Lease for $tenantName'),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text('Current lease ends:'),
                  Text(
                    _formatDate(tenant['lease_end_date']),
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 16),
                  const Text('New lease end date:'),
                  InkWell(
                    onTap: () async {
                      final DateTime? picked = await showDatePicker(
                        context: context,
                        initialDate: newLeaseEndDate,
                        firstDate: DateTime.now(),
                        lastDate: DateTime.now().add(const Duration(days: 365 * 5)),
                      );
                      if (picked != null && picked != newLeaseEndDate) {
                        setState(() {
                          newLeaseEndDate = picked;
                        });
                      }
                    },
                    child: Container(
                      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(DateFormat('MMM d, y').format(newLeaseEndDate)),
                          const Icon(Icons.calendar_today),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(false),
                  child: const Text('Cancel'),
                ),
                ElevatedButton(
                  onPressed: () => Navigator.of(context).pop(true),
                  child: const Text('Renew Lease'),
                ),
              ],
            );
          },
        );
      },
    );

    if (shouldRenew != true || !mounted) return;
                    
                    // Show loading indicator
    if (!mounted) return;
    await showDialog(
      context: context,
                      barrierDismissible: false,
                      builder: (BuildContext context) {
                        return const AlertDialog(
                          content: Row(
                            children: [
                              CircularProgressIndicator(),
                              SizedBox(width: 16),
                              Text('Updating lease...'),
                            ],
                          ),
                        );
                      },
                    );
                    
                    try {
                      // Update lease end date in database
                      await Supabase.instance.client
                          .from('tenants')
                          .update({
                            'lease_end_date': newLeaseEndDate.toIso8601String(),
                          })
                          .eq('id', tenantId);
                      
                      if (!mounted) return;
                      
                      // Show success message
      _showSnackBar('Lease renewed successfully');
                      
                      // Refresh data
      await _loadData();
                    } catch (e) {
                      if (!mounted) return;
                      
      // Show error message
      _showSnackBar('Error renewing lease: ${e.toString()}', isError: true);
    }
  }

  void _showSnackBar(String message, {bool isError = false}) {
    if (!mounted) return;
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isError ? Colors.red : Colors.green,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Lease Expiration Report'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      drawer: const AppDrawer(),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _error != null
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text('Error: $_error'),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: _loadData,
                        child: const Text('Retry'),
                      ),
                    ],
                  ),
                )
              : Column(
                  children: [
                    _buildFilters(),
                    Expanded(
                      child: _tenants.isEmpty
                          ? const Center(
                              child: Text('No lease expirations found for the selected criteria'),
                            )
                          : ListView.builder(
                              itemCount: _tenants.length,
                              itemBuilder: (context, index) {
                                final tenant = _tenants[index];
                                return _buildTenantCard(tenant);
                              },
                            ),
                    ),
                  ],
                ),
    );
  }

  Widget _buildFilters() {
    return Card(
      margin: const EdgeInsets.all(8.0),
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Filter Lease Expirations',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            TextField(
              decoration: const InputDecoration(
                hintText: 'Search by name or property',
                prefixIcon: Icon(Icons.search),
                border: OutlineInputBorder(),
                contentPadding: EdgeInsets.symmetric(vertical: 8, horizontal: 16),
              ),
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
                _loadData();
              },
            ),
            const SizedBox(height: 12),
            // Use Wrap instead of Row to allow filters to flow to next line on smaller screens
            Wrap(
              spacing: 8.0, // horizontal space between items
              runSpacing: 8.0, // vertical space between lines
              children: [
                SizedBox(
                  width: MediaQuery.of(context).size.width > 600 
                    ? (MediaQuery.of(context).size.width - 56) / 2 // On larger screens, take half width
                    : MediaQuery.of(context).size.width - 40, // On smaller screens, take full width
                  child: DropdownButtonFormField<String>(
                    isExpanded: true,
                    decoration: const InputDecoration(
                      labelText: 'Timeframe',
                      border: OutlineInputBorder(),
                      contentPadding: EdgeInsets.symmetric(vertical: 8, horizontal: 16),
                    ),
                    value: _selectedTimeframe,
                    items: const [
                      DropdownMenuItem(value: 'all', child: Text('All Leases')),
                      DropdownMenuItem(value: '30_days', child: Text('Expiring in 30 days')),
                      DropdownMenuItem(value: '60_days', child: Text('Expiring in 60 days')),
                      DropdownMenuItem(value: '90_days', child: Text('Expiring in 90 days')),
                    ],
                    onChanged: (value) {
                      if (value != null) {
                        setState(() {
                          _selectedTimeframe = value;
                        });
                        _loadData();
                      }
                    },
                  ),
                ),
                SizedBox(
                  width: MediaQuery.of(context).size.width > 600 
                    ? (MediaQuery.of(context).size.width - 56) / 2 // On larger screens, take half width
                    : MediaQuery.of(context).size.width - 40, // On smaller screens, take full width
                  child: DropdownButtonFormField<String>(
                    isExpanded: true,
                    decoration: const InputDecoration(
                      labelText: 'Property',
                      border: OutlineInputBorder(),
                      contentPadding: EdgeInsets.symmetric(vertical: 8, horizontal: 16),
                    ),
                    value: _selectedPropertyId,
                    items: [
                      const DropdownMenuItem(value: null, child: Text('All Properties')),
                      ..._properties.map((property) {
                        return DropdownMenuItem(
                          value: property['id'],
                          child: Text(
                            property['name'],
                            overflow: TextOverflow.ellipsis,
                          ),
                        );
                      }),
                    ],
                    onChanged: (value) {
                      setState(() {
                        _selectedPropertyId = value;
                      });
                      _loadData();
                    },
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTenantCard(Map<String, dynamic> tenant) {
    final statusColor = _getStatusColor(tenant);
    final remainingTimeLabel = _getRemainingTimeLabel(tenant);
    
    final rentalPrice = tenant['rooms']?['rental_price'] != null
        ? double.tryParse(tenant['rooms']['rental_price'].toString()) ?? 0.0
        : 0.0;
    
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '${tenant['first_name']} ${tenant['last_name']}',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        tenant['rooms']?['properties']?['name'] ?? 'No Property',
                        style: TextStyle(
                          color: Colors.grey[700],
                        ),
                      ),
                      const SizedBox(height: 2),
                      Text(
                        'Room: ${tenant['rooms']?['name'] ?? 'No Room'}',
                        style: TextStyle(
                          color: Colors.grey[700],
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: statusColor.withAlpha(25), // Using withAlpha instead of withOpacity
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(color: statusColor),
                  ),
                  child: Text(
                    remainingTimeLabel,
                    style: TextStyle(
                      color: statusColor,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            const Divider(),
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text('Lease Start'),
                      Text(
                        _formatDate(tenant['lease_start_date']),
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                    ],
                  ),
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text('Lease End'),
                      Text(
                        _formatDate(tenant['lease_end_date']),
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text('Total Lease Duration'),
                      Text(
                        '${tenant['total_lease_days']} days (${tenant['total_lease_months']} months)',
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                    ],
                  ),
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text('Monthly Rent'),
                      Text(
                        CurrencyFormatter.formatCurrency(rentalPrice),
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                ElevatedButton.icon(
                  icon: const Icon(Icons.phone),
                  label: const Text('Contact'),
                  onPressed: () => _showContactInfo(tenant['phone_number'], tenant['email']),
                ),
                const SizedBox(width: 8),
                ElevatedButton.icon(
                  icon: const Icon(Icons.calendar_today),
                  label: const Text('Renew Lease'),
                  onPressed: () => _renewLease(tenant),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
} 