-- Create bills table if it doesn't exist
CREATE TABLE IF NOT EXISTS "public"."bills" (
  "id" UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  "title" TEXT NOT NULL,
  "description" TEXT NOT NULL,
  "amount" DECIMAL(10, 2) NOT NULL,
  "due_date" TIMESTAMP WITH TIME ZONE NOT NULL,
  "status" TEXT NOT NULL DEFAULT 'pending',
  "type" TEXT NOT NULL,
  "recurrence" TEXT NOT NULL,
  "tenant_id" UUID REFERENCES "public"."tenants"("id") ON DELETE CASCADE,
  "property_id" UUID REFERENCES "public"."properties"("id") ON DELETE CASCADE,
  "room_id" UUID REFERENCES "public"."rooms"("id") ON DELETE CASCADE,
  "bill_number" TEXT,
  "paid_at" TIMESTAMP WITH TIME ZONE,
  "paid_amount" DECIMAL(10, 2),
  "notes" TEXT,
  "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  "updated_at" TIMESTAMP WITH TIME ZONE
);

-- Check and add utility metering fields to bills table
DO $$
BEGIN
    -- Check for include_in_rent column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_schema = 'public' 
                  AND table_name = 'bills'
                  AND column_name = 'include_in_rent') THEN
        ALTER TABLE public.bills ADD COLUMN include_in_rent BOOLEAN DEFAULT FALSE;
    END IF;

    -- Check for utility_type column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_schema = 'public' 
                  AND table_name = 'bills'
                  AND column_name = 'utility_type') THEN
        ALTER TABLE public.bills ADD COLUMN utility_type TEXT;
    END IF;

    -- Check for previous_meter_reading column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_schema = 'public' 
                  AND table_name = 'bills'
                  AND column_name = 'previous_meter_reading') THEN
        ALTER TABLE public.bills ADD COLUMN previous_meter_reading DECIMAL(10, 2);
    END IF;

    -- Check for current_meter_reading column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_schema = 'public' 
                  AND table_name = 'bills'
                  AND column_name = 'current_meter_reading') THEN
        ALTER TABLE public.bills ADD COLUMN current_meter_reading DECIMAL(10, 2);
    END IF;

    -- Check for unit_consumed column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_schema = 'public' 
                  AND table_name = 'bills'
                  AND column_name = 'unit_consumed') THEN
        ALTER TABLE public.bills ADD COLUMN unit_consumed DECIMAL(10, 2);
    END IF;

    -- Check for rate_per_unit column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_schema = 'public' 
                  AND table_name = 'bills'
                  AND column_name = 'rate_per_unit') THEN
        ALTER TABLE public.bills ADD COLUMN rate_per_unit DECIMAL(10, 2);
    END IF;

    -- Check for notes column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_schema = 'public' 
                  AND table_name = 'bills'
                  AND column_name = 'notes') THEN
        ALTER TABLE public.bills ADD COLUMN notes TEXT;
    END IF;

    -- Check for bill_components column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_schema = 'public' 
                  AND table_name = 'bills'
                  AND column_name = 'bill_components') THEN
        ALTER TABLE public.bills ADD COLUMN bill_components JSONB;
    END IF;
    
    -- Check for bill_number column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_schema = 'public' 
                  AND table_name = 'bills'
                  AND column_name = 'bill_number') THEN
        ALTER TABLE public.bills ADD COLUMN bill_number TEXT;
    END IF;
END $$;

-- Create indexes
CREATE INDEX IF NOT EXISTS "bills_tenant_id_idx" ON "public"."bills" ("tenant_id");
CREATE INDEX IF NOT EXISTS "bills_property_id_idx" ON "public"."bills" ("property_id");
CREATE INDEX IF NOT EXISTS "bills_room_id_idx" ON "public"."bills" ("room_id");
CREATE INDEX IF NOT EXISTS "bills_status_idx" ON "public"."bills" ("status");
CREATE INDEX IF NOT EXISTS "bills_type_idx" ON "public"."bills" ("type");
CREATE INDEX IF NOT EXISTS "bills_due_date_idx" ON "public"."bills" ("due_date");
CREATE INDEX IF NOT EXISTS "idx_bills_utility_type" ON "public"."bills" ("utility_type");
CREATE INDEX IF NOT EXISTS "idx_bills_include_in_rent" ON "public"."bills" ("include_in_rent");
CREATE INDEX IF NOT EXISTS "idx_bills_bill_number" ON "public"."bills" ("bill_number");

-- Add RLS policies
ALTER TABLE "public"."bills" ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Allow authenticated users to view bills" ON "public"."bills";
DROP POLICY IF EXISTS "Allow authenticated users to insert bills" ON "public"."bills";
DROP POLICY IF EXISTS "Allow authenticated users to update their own bills" ON "public"."bills";
DROP POLICY IF EXISTS "Allow authenticated users to delete their own bills" ON "public"."bills";

-- Create policy to allow authenticated users to view bills
CREATE POLICY "Allow authenticated users to view bills" 
  ON "public"."bills" 
  FOR SELECT 
  TO authenticated 
  USING (true);

-- Create policy to allow authenticated users to insert bills
CREATE POLICY "Allow authenticated users to insert bills" 
  ON "public"."bills" 
  FOR INSERT 
  TO authenticated 
  WITH CHECK (true);

-- Create policy to allow authenticated users to update their own bills
CREATE POLICY "Allow authenticated users to update their own bills" 
  ON "public"."bills" 
  FOR UPDATE 
  TO authenticated 
  USING (true);

-- Create policy to allow authenticated users to delete their own bills
CREATE POLICY "Allow authenticated users to delete their own bills" 
  ON "public"."bills" 
  FOR DELETE 
  TO authenticated 
  USING (true);

-- Add bills table to realtime publication
BEGIN;
  DROP PUBLICATION IF EXISTS supabase_realtime;
  CREATE PUBLICATION supabase_realtime FOR TABLE "public"."bills";
COMMIT; 