import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../services/service_locator.dart';
import '../../utils/validators.dart';
import '../../widgets/ui_components.dart';

class NewPasswordScreen extends StatefulWidget {
  final String email;

  const NewPasswordScreen({
    super.key,
    required this.email,
  });

  @override
  State<NewPasswordScreen> createState() => NewPasswordScreenState();
}

class NewPasswordScreenState extends State<NewPasswordScreen> {
  final _formKey = GlobalKey<FormState>();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  bool _isLoading = false;
  bool _passwordVisible = false;
  bool _confirmPasswordVisible = false;

  @override
  void dispose() {
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  // Update password
  Future<void> _updatePassword() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // Update password using Supabase
      await serviceLocator.authService.client.auth.updateUser(
        UserAttributes(password: _passwordController.text),
      );

      if (mounted) {
        showSuccessMessage(
          context, 
          'Password updated successfully!'
        );
        
        // Navigate to login screen
        Navigator.of(context).pushNamedAndRemoveUntil('/auth', (route) => false);
      }
    } catch (error) {
      if (mounted) {
        showErrorMessage(
          context,
          'Error updating password: ${error.toString()}',
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('New Password'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: Center(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(24.0),
          child: Form(
            key: _formKey,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  Icons.lock_reset,
                  size: 80,
                  color: Colors.blue,
                ),
                const SizedBox(height: 24),
                const Text(
                  'Create New Password',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),
                Text(
                  'Create a new password for ${widget.email}',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.grey[700],
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 32),

                // Password requirements guide
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.grey[100],
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: const [
                      Text(
                        'Your password must:',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                      SizedBox(height: 8),
                      Text('• Be at least 8 characters long'),
                      Text('• Include at least one uppercase letter (A-Z)'),
                      Text('• Include at least one lowercase letter (a-z)'),
                      Text('• Include at least one number (0-9)'),
                      Text('• Include at least one special character (!@#\$%^&*(),.?":{}|<>)'),
                    ],
                  ),
                ),
                const SizedBox(height: 24),

                // New Password
                CustomTextField(
                  controller: _passwordController,
                  labelText: 'New Password',
                  prefixIcon: Icons.lock,
                  suffixIcon: _passwordVisible 
                      ? Icons.visibility 
                      : Icons.visibility_off,
                  onSuffixIconPressed: () {
                    setState(() {
                      _passwordVisible = !_passwordVisible;
                    });
                  },
                  obscureText: !_passwordVisible,
                  validator: (value) => Validators.validatePassword(value, isSignUp: true),
                  textInputAction: TextInputAction.next,
                ),
                const SizedBox(height: 16),

                // Confirm Password
                CustomTextField(
                  controller: _confirmPasswordController,
                  labelText: 'Confirm Password',
                  prefixIcon: Icons.lock,
                  suffixIcon: _confirmPasswordVisible 
                      ? Icons.visibility 
                      : Icons.visibility_off,
                  onSuffixIconPressed: () {
                    setState(() {
                      _confirmPasswordVisible = !_confirmPasswordVisible;
                    });
                  },
                  obscureText: !_confirmPasswordVisible,
                  validator: (value) => Validators.validateConfirmPassword(
                    value,
                    _passwordController.text,
                  ),
                  textInputAction: TextInputAction.done,
                  onFieldSubmitted: (_) => _updatePassword(),
                ),
                const SizedBox(height: 32),

                // Update Password Button
                CustomButton(
                  text: 'Update Password',
                  onPressed: _updatePassword,
                  isLoading: _isLoading,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
} 