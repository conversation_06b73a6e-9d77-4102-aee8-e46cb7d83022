import 'package:flutter/material.dart';
import '../../models/bill/bill.dart';
import '../../models/bill/bill_template.dart';
import '../../services/service_locator.dart';
import '../../utils/logger.dart';
import '../../widgets/loading_indicator.dart';
import 'template_form_page.dart';

class BillTemplatesPage extends StatefulWidget {
  const BillTemplatesPage({super.key});

  @override
  State<BillTemplatesPage> createState() => _BillTemplatesPageState();
}

class _BillTemplatesPageState extends State<BillTemplatesPage> {
  List<BillTemplate> _templates = [];
  bool _isLoading = true;
  String? _filterType;

  @override
  void initState() {
    super.initState();
    _loadTemplates();
  }

  Future<void> _loadTemplates() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final templates = await serviceLocator.billTemplateService.getAllTemplates();
      
      if (!mounted) return;

      setState(() {
        _templates = templates;
        _isLoading = false;
      });
    } catch (e) {
      AppLogger.error('Error loading bill templates: $e');
      if (!mounted) return;
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error loading templates: $e')),
      );
      
      setState(() {
        _isLoading = false;
      });
    }
  }

  List<BillTemplate> _getFilteredTemplates() {
    if (_filterType == null) {
      return _templates;
    }
    
    final billType = BillType.values.firstWhere(
      (type) => type.name == _filterType,
      orElse: () => BillType.other,
    );
    
    return _templates.where((template) => template.type == billType).toList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Bill Templates'),
        actions: [
          PopupMenuButton<String>(
            icon: const Icon(Icons.filter_list),
            tooltip: 'Filter by type',
            onSelected: (value) {
              setState(() {
                _filterType = value == 'all' ? null : value;
              });
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'all',
                child: Text('All Types'),
              ),
              ...BillType.values.map((type) => PopupMenuItem(
                value: type.name,
                child: Text(_getBillTypeDisplayName(type)),
              )),
            ],
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: LoadingIndicator())
          : _templates.isEmpty
              ? _buildEmptyState()
              : _buildTemplatesList(),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _navigateToTemplateForm(),
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.description_outlined,
            size: 64,
            color: Colors.grey,
          ),
          const SizedBox(height: 16),
          const Text(
            'No bill templates yet',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'Create templates to automate your bill generation',
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () => _navigateToTemplateForm(),
            icon: const Icon(Icons.add),
            label: const Text('Create Template'),
          ),
        ],
      ),
    );
  }

  Widget _buildTemplatesList() {
    final filteredTemplates = _getFilteredTemplates();
    
    return filteredTemplates.isEmpty
        ? Center(
            child: Text(
              'No ${_filterType != null ? _getBillTypeDisplayName(BillType.values.firstWhere(
                (type) => type.name == _filterType,
                orElse: () => BillType.other,
              )) : ''} templates found',
            ),
          )
        : RefreshIndicator(
            onRefresh: _loadTemplates,
            child: ListView.builder(
              itemCount: filteredTemplates.length,
              padding: const EdgeInsets.all(16),
              itemBuilder: (context, index) {
                final template = filteredTemplates[index];
                return _buildTemplateCard(template);
              },
            ),
          );
  }

  Widget _buildTemplateCard(BillTemplate template) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () => _navigateToTemplateForm(template: template),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Text(
                      template.name,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  _buildTypeChip(template.type),
                ],
              ),
              const SizedBox(height: 8),
              Text(
                template.description,
                style: const TextStyle(fontSize: 14),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      const Icon(Icons.calendar_today, size: 16),
                      const SizedBox(width: 4),
                      Text(
                        'Due: Day ${template.dueDayOfMonth} (${_getRecurrenceText(template.recurrence)})',
                        style: const TextStyle(fontSize: 14),
                      ),
                    ],
                  ),
                  Text(
                    template.type == BillType.rent && template.amount == 0
                        ? 'Dynamic (Room Rent)'
                        : serviceLocator.settingsService.formatCurrency(template.amount),
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Row(
                      children: [
                        Icon(
                          template.autoGenerate
                              ? Icons.auto_awesome
                              : Icons.auto_awesome_outlined,
                          size: 16,
                          color: template.autoGenerate 
                              ? Theme.of(context).colorScheme.primary 
                              : Theme.of(context).colorScheme.primary.withAlpha(128),
                        ),
                        const SizedBox(width: 4),
                        Text(
                          template.autoGenerate ? 'Auto-generates' : 'Manual',
                          style: TextStyle(
                            fontSize: 14,
                            color: template.autoGenerate 
                                ? Theme.of(context).colorScheme.primary 
                                : Theme.of(context).colorScheme.primary.withAlpha(128),
                          ),
                        ),
                      ],
                    ),
                  ),
                  Row(
                    children: [
                      IconButton(
                        icon: const Icon(Icons.play_arrow),
                        tooltip: 'Generate bills now',
                        onPressed: () => _generateBillsNow(template),
                      ),
                      IconButton(
                        icon: const Icon(Icons.edit),
                        tooltip: 'Edit template',
                        onPressed: () => _navigateToTemplateForm(template: template),
                      ),
                      IconButton(
                        icon: const Icon(Icons.delete),
                        tooltip: 'Delete template',
                        onPressed: () => _confirmDeleteTemplate(template),
                      ),
                    ],
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTypeChip(BillType type) {
    Color chipColor;
    
    switch (type) {
      case BillType.rent:
        chipColor = Colors.blue;
        break;
      case BillType.utility:
        chipColor = Colors.green;
        break;
      case BillType.maintenance:
        chipColor = Colors.orange;
        break;
      case BillType.service:
        chipColor = Colors.purple;
        break;
      case BillType.other:
        chipColor = Colors.grey;
        break;
    }
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: chipColor.withAlpha(26),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: chipColor.withAlpha(128)),
      ),
      child: Text(
        _getBillTypeDisplayName(type),
        style: TextStyle(
          color: chipColor,
          fontSize: 12,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  String _getBillTypeDisplayName(BillType type) {
    switch (type) {
      case BillType.rent:
        return 'Rent';
      case BillType.utility:
        return 'Utility';
      case BillType.maintenance:
        return 'Maintenance';
      case BillType.service:
        return 'Service';
      case BillType.other:
        return 'Other';
    }
  }

  String _getRecurrenceText(RecurrenceType recurrence) {
    switch (recurrence) {
      case RecurrenceType.oneTime:
        return 'One-time';
      case RecurrenceType.monthly:
        return 'Monthly';
      case RecurrenceType.quarterly:
        return 'Quarterly';
      case RecurrenceType.yearly:
        return 'Yearly';
    }
  }

  void _navigateToTemplateForm({BillTemplate? template}) async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => TemplateFormPage(template: template),
      ),
    );
    
    if (result == true) {
      _loadTemplates();
    }
  }

  Future<void> _confirmDeleteTemplate(BillTemplate template) async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Template'),
        content: Text(
          'Are you sure you want to delete the "${template.name}" template? '
          'This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
    
    if (result == true) {
      _deleteTemplate(template);
    }
  }

  Future<void> _deleteTemplate(BillTemplate template) async {
    setState(() {
      _isLoading = true;
    });
    
    try {
      await serviceLocator.billTemplateService.deleteTemplate(template.id);
      
      if (!mounted) return;
      
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Template deleted successfully')),
      );
      
      _loadTemplates();
    } catch (e) {
      AppLogger.error('Error deleting template: $e');
      if (!mounted) return;
      
      setState(() {
        _isLoading = false;
      });
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error deleting template: $e')),
      );
    }
  }

  Future<void> _generateBillsNow(BillTemplate template) async {
    setState(() {
      _isLoading = true;
    });
    
    try {
      final now = DateTime.now();
      final bills = await serviceLocator.billTemplateService.generateBillsFromTemplate(
        template: template,
        month: now.month,
        year: now.year,
      );
      
      if (!mounted) return;
      
      setState(() {
        _isLoading = false;
      });
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            bills.isEmpty
                ? 'No new bills were generated (bills may already exist)'
                : '${bills.length} bills generated successfully',
          ),
        ),
      );
    } catch (e) {
      AppLogger.error('Error generating bills: $e');
      if (!mounted) return;
      
      setState(() {
        _isLoading = false;
      });
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error generating bills: $e')),
      );
    }
  }
} 