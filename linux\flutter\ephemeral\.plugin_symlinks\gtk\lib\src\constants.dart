/// [gtk-alternative-button-order](https://docs.gtk.org/gtk3/property.Settings.gtk-alternative-button-order.html)
const kGtkAlternativeButtonOrder = 'gtk-alternative-button-order';

/// [gtk-alternative-sort-arrows](https://docs.gtk.org/gtk3/property.Settings.gtk-alternative-sort-arrows.html)
const kGtkAlternativeSortArrows = 'gtk-alternative-sort-arrows';

/// [gtk-application-prefer-dark-theme](https://docs.gtk.org/gtk3/property.Settings.gtk-application-prefer-dark-theme.html)
const kGtkApplicationPreferDarkTheme = 'gtk-application-prefer-dark-theme';

/// [gtk-cursor-aspect-ratio](https://docs.gtk.org/gtk3/property.Settings.gtk-cursor-aspect-ratio.html)
const kGtkCursorAspectRatio = 'gtk-cursor-aspect-ratio';

/// [gtk-cursor-blink](https://docs.gtk.org/gtk3/property.Settings.gtk-cursor-blink.html)
const kGtkCursorBlink = 'gtk-cursor-blink';

/// [gtk-cursor-blink-time](https://docs.gtk.org/gtk3/property.Settings.gtk-cursor-blink-time.html)
const kGtkCursorBlinkTime = 'gtk-cursor-blink-time';

/// [gtk-cursor-blink-timeout](https://docs.gtk.org/gtk3/property.Settings.gtk-cursor-blink-timeout.html)
const kGtkCursorBlinkTimeout = 'gtk-cursor-blink-timeout';

/// [gtk-cursor-theme-name](https://docs.gtk.org/gtk3/property.Settings.gtk-cursor-theme-name.html)
const kGtkCursorThemeName = 'gtk-cursor-theme-name';

/// [gtk-cursor-theme-size](https://docs.gtk.org/gtk3/property.Settings.gtk-cursor-theme-size.html)
const kGtkCursorThemeSize = 'gtk-cursor-theme-size';

/// [gtk-decoration-layout](https://docs.gtk.org/gtk3/property.Settings.gtk-decoration-layout.html)
const kGtkDecorationLayout = 'gtk-decoration-layout';

/// [gtk-dialogs-use-header](https://docs.gtk.org/gtk3/property.Settings.gtk-dialogs-use-header.html)
const kGtkDialogsUseHeader = 'gtk-dialogs-use-header';

/// [gtk-dnd-drag-threshold](https://docs.gtk.org/gtk3/property.Settings.gtk-dnd-drag-threshold.html)
const kGtkDndDragThreshold = 'gtk-dnd-drag-threshold';

/// [gtk-double-click-distance](https://docs.gtk.org/gtk3/property.Settings.gtk-double-click-distance.html)
const kGtkDoubleClickDistance = 'gtk-double-click-distance';

/// [gtk-double-click-time](https://docs.gtk.org/gtk3/property.Settings.gtk-double-click-time.html)
const kGtkDoubleClickTime = 'gtk-double-click-time';

/// [gtk-enable-accels](https://docs.gtk.org/gtk3/property.Settings.gtk-enable-accels.html)
const kGtkEnableAccels = 'gtk-enable-accels';

/// [gtk-enable-animations](https://docs.gtk.org/gtk3/property.Settings.gtk-enable-animations.html)
const kGtkEnableAnimations = 'gtk-enable-animations';

/// [gtk-enable-event-sounds](https://docs.gtk.org/gtk3/property.Settings.gtk-enable-event-sounds.html)
const kGtkEnableEventSounds = 'gtk-enable-event-sounds';

/// [gtk-enable-input-feedback-sounds](https://docs.gtk.org/gtk3/property.Settings.gtk-enable-input-feedback-sounds.html)
const kGtkEnableInputFeedbackSounds = 'gtk-enable-input-feedback-sounds';

/// [gtk-enable-primary-paste](https://docs.gtk.org/gtk3/property.Settings.gtk-enable-primary-paste.html)
const kGtkEnablePrimaryPaste = 'gtk-enable-primary-paste';

/// [gtk-entry-password-hint-timeout](https://docs.gtk.org/gtk3/property.Settings.gtk-entry-password-hint-timeout.html)
const kGtkEntryPasswordHintTimeout = 'gtk-entry-password-hint-timeout';

/// [gtk-entry-select-on-focus](https://docs.gtk.org/gtk3/property.Settings.gtk-entry-select-on-focus.html)
const kGtkEntrySelectOnFocus = 'gtk-entry-select-on-focus';

/// [gtk-error-bell](https://docs.gtk.org/gtk3/property.Settings.gtk-error-bell.html)
const kGtkErrorBell = 'gtk-error-bell';

/// [gtk-font-name](https://docs.gtk.org/gtk3/property.Settings.gtk-font-name.html)
const kGtkFontName = 'gtk-font-name';

/// [gtk-fontconfig-timestamp](https://docs.gtk.org/gtk3/property.Settings.gtk-fontconfig-timestamp.html)
const kGtkFontconfigTimestamp = 'gtk-fontconfig-timestamp';

/// [gtk-icon-theme-name](https://docs.gtk.org/gtk3/property.Settings.gtk-icon-theme-name.html)
const kGtkIconThemeName = 'gtk-icon-theme-name';

/// [gtk-im-module](https://docs.gtk.org/gtk3/property.Settings.gtk-im-module.html)
const kGtkImModule = 'gtk-im-module';

/// [gtk-key-theme-name](https://docs.gtk.org/gtk3/property.Settings.gtk-key-theme-name.html)
const kGtkKeyThemeName = 'gtk-key-theme-name';

/// [gtk-keynav-use-caret](https://docs.gtk.org/gtk3/property.Settings.gtk-keynav-use-caret.html)
const kGtkKeynavUseCaret = 'gtk-keynav-use-caret';

/// [gtk-label-select-on-focus](https://docs.gtk.org/gtk3/property.Settings.gtk-label-select-on-focus.html)
const kGtkLabelSelectOnFocus = 'gtk-label-select-on-focus';

/// [gtk-long-press-time](https://docs.gtk.org/gtk3/property.Settings.gtk-long-press-time.html)
const kGtkLongPressTime = 'gtk-long-press-time';

/// [gtk-modules](https://docs.gtk.org/gtk3/property.Settings.gtk-modules.html)
const kGtkModules = 'gtk-modules';

/// [gtk-overlay-scrolling](https://docs.gtk.org/gtk3/property.Settings.gtk-overlay-scrolling.html)
const kGtkOverlayScrolling = 'gtk-overlay-scrolling';

/// [gtk-primary-button-warps-slider](https://docs.gtk.org/gtk3/property.Settings.gtk-primary-button-warps-slider.html)
const kGtkPrimaryButtonWarpsSlider = 'gtk-primary-button-warps-slider';

/// [gtk-print-backends](https://docs.gtk.org/gtk3/property.Settings.gtk-print-backends.html)
const kGtkPrintBackends = 'gtk-print-backends';

/// [gtk-print-preview-command](https://docs.gtk.org/gtk3/property.Settings.gtk-print-preview-command.html)
const kGtkPrintPreviewCommand = 'gtk-print-preview-command';

/// [gtk-recent-files-enabled](https://docs.gtk.org/gtk3/property.Settings.gtk-recent-files-enabled.html)
const kGtkRecentFilesEnabled = 'gtk-recent-files-enabled';

/// [gtk-recent-files-max-age](https://docs.gtk.org/gtk3/property.Settings.gtk-recent-files-max-age.html)
const kGtkRecentFilesMaxAge = 'gtk-recent-files-max-age';

/// [gtk-shell-shows-app-menu](https://docs.gtk.org/gtk3/property.Settings.gtk-shell-shows-app-menu.html)
const kGtkShellShowsAppMenu = 'gtk-shell-shows-app-menu';

/// [gtk-shell-shows-desktop](https://docs.gtk.org/gtk3/property.Settings.gtk-shell-shows-desktop.html)
const kGtkShellShowsDesktop = 'gtk-shell-shows-desktop';

/// [gtk-shell-shows-menubar](https://docs.gtk.org/gtk3/property.Settings.gtk-shell-shows-menubar.html)
const kGtkShellShowsMenubar = 'gtk-shell-shows-menubar';

/// [gtk-sound-theme-name](https://docs.gtk.org/gtk3/property.Settings.gtk-sound-theme-name.html)
const kGtkSoundThemeName = 'gtk-sound-theme-name';

/// [gtk-split-cursor](https://docs.gtk.org/gtk3/property.Settings.gtk-split-cursor.html)
const kGtkSplitCursor = 'gtk-split-cursor';

/// [gtk-theme-name](https://docs.gtk.org/gtk3/property.Settings.gtk-theme-name.html)
const kGtkThemeName = 'gtk-theme-name';

/// [gtk-titlebar-double-click](https://docs.gtk.org/gtk3/property.Settings.gtk-titlebar-double-click.html)
const kGtkTitlebarDoubleClick = 'gtk-titlebar-double-click';

/// [gtk-titlebar-middle-click](https://docs.gtk.org/gtk3/property.Settings.gtk-titlebar-middle-click.html)
const kGtkTitlebarMiddleClick = 'gtk-titlebar-middle-click';

/// [gtk-titlebar-right-click](https://docs.gtk.org/gtk3/property.Settings.gtk-titlebar-right-click.html)
const kGtkTitlebarRightClick = 'gtk-titlebar-right-click';

/// [gtk-xft-antialias](https://docs.gtk.org/gtk3/property.Settings.gtk-xft-antialias.html)
const kGtkXftAntialias = 'gtk-xft-antialias';

/// [gtk-xft-dpi](https://docs.gtk.org/gtk3/property.Settings.gtk-xft-dpi.html)
const kGtkXftDpi = 'gtk-xft-dpi';

/// [gtk-xft-hinting](https://docs.gtk.org/gtk3/property.Settings.gtk-xft-hinting.html)
const kGtkXftHinting = 'gtk-xft-hinting';

/// [gtk-xft-hintstyle](https://docs.gtk.org/gtk3/property.Settings.gtk-xft-hintstyle.html)
const kGtkXftHintstyle = 'gtk-xft-hintstyle';

/// [gtk-xft-rgba](https://docs.gtk.org/gtk3/property.Settings.gtk-xft-rgba.html)
const kGtkXftRgba = 'gtk-xft-rgba';
