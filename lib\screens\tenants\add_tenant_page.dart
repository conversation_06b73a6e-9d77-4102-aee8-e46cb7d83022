import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../models/tenant/tenant.dart';
import '../../services/service_locator.dart';
import '../../widgets/app_loading_indicator.dart';

class AddTenantPage extends StatefulWidget {
  const AddTenantPage({super.key});

  @override
  State<AddTenantPage> createState() => _AddTenantPageState();
}

class _AddTenantPageState extends State<AddTenantPage> {
  final _formKey = GlobalKey<FormState>();
  bool _isLoading = false;
  String _errorMessage = '';
  bool _isSuccess = false;
  bool _welcomeEmailSent = false;
  bool _passwordResetSent = false;

  // Form fields
  final _firstNameController = TextEditingController();
  final _lastNameController = TextEditingController();
  final _emailController = TextEditingController();
  final _phoneController = TextEditingController();
  final _emergencyContactNameController = TextEditingController();
  final _emergencyContactPhoneController = TextEditingController();
  final _notesController = TextEditingController();
  DateTime? _leaseStartDate;
  DateTime? _leaseEndDate;
  TenantStatus _selectedStatus =
      TenantStatus.pending; // Default status is pending

  @override
  void dispose() {
    _firstNameController.dispose();
    _lastNameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _emergencyContactNameController.dispose();
    _emergencyContactPhoneController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  // Date picker for lease start date
  Future<void> _selectLeaseStartDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _leaseStartDate ?? DateTime.now(),
      firstDate: DateTime(2000),
      lastDate: DateTime(2101),
    );
    if (picked != null && picked != _leaseStartDate) {
      setState(() {
        _leaseStartDate = picked;
        // If end date is before start date, update end date
        if (_leaseEndDate != null &&
            _leaseEndDate!.isBefore(_leaseStartDate!)) {
          _leaseEndDate = _leaseStartDate!.add(const Duration(days: 365));
        }
      });
    }
  }

  // Date picker for lease end date
  Future<void> _selectLeaseEndDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate:
          _leaseEndDate ??
          (_leaseStartDate?.add(const Duration(days: 365)) ??
              DateTime.now().add(const Duration(days: 365))),
      firstDate: _leaseStartDate ?? DateTime.now(),
      lastDate: DateTime(2101),
    );
    if (picked != null && picked != _leaseEndDate) {
      setState(() {
        _leaseEndDate = picked;
      });
    }
  }

  // Check if email is the current authenticated user's email
  bool _isCurrentUserEmail(String email) {
    final currentUser = Supabase.instance.client.auth.currentUser;
    if (currentUser != null && currentUser.email != null) {
      return currentUser.email!.toLowerCase() == email.toLowerCase();
    }
    return false;
  }

  // Submit form
  Future<void> _submitForm() async {
    if (_formKey.currentState!.validate()) {
      setState(() {
        _isLoading = true;
        _errorMessage = '';
      });

      try {
        final email = _emailController.text.trim();

        // Check if email already exists
        final emailExists = await serviceLocator.tenantService.checkEmailExists(
          email,
        );
        if (emailExists) {
          setState(() {
            _isLoading = false;
            _errorMessage =
                'A tenant with this email address already exists. Please use a different email.';
          });
          return;
        }

        final tenant = Tenant(
          firstName: _firstNameController.text.trim(),
          lastName: _lastNameController.text.trim(),
          email: email,
          phoneNumber: _phoneController.text.trim(), // Required field
          leaseStartDate: _leaseStartDate,
          leaseEndDate: _leaseEndDate,
          status: _selectedStatus,
          emergencyContactName:
              _emergencyContactNameController.text.trim(), // Required field
          emergencyContactPhone:
              _emergencyContactPhoneController.text.trim(), // Required field
          notes:
              _notesController.text.trim().isNotEmpty
                  ? _notesController.text.trim()
                  : null,
        );

        // Save tenant data with all notifications
        final savedTenant = await serviceLocator.tenantService
            .createTenantWithNotification(tenant);

        // Check email status from tenant object directly
        setState(() {
          _isLoading = false;
          _isSuccess = true;
          _welcomeEmailSent = savedTenant.welcomeEmailSent;
          _passwordResetSent = savedTenant.passwordResetSent;

          // Log email status for debugging (only in debug mode)
          if (kDebugMode) debugPrint('Welcome email sent: $_welcomeEmailSent');
          if (kDebugMode)
            debugPrint('Password reset sent: $_passwordResetSent');
        });

        // Wait a moment before navigating back
        await Future.delayed(const Duration(seconds: 2));
        if (mounted) {
          Navigator.pop(context, savedTenant);
        }
      } catch (e) {
        String errorMessage = e.toString();

        // Check for duplicate email error from PostgreSQL
        if (errorMessage.contains(
              'duplicate key value violates unique constraint',
            ) &&
            errorMessage.contains('tenants.email_key')) {
          errorMessage =
              'A tenant with this email address already exists. Please use a different email.';
        }

        setState(() {
          _isLoading = false;
          _errorMessage = errorMessage;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Add New Tenant')),
      body:
          _isLoading
              ? const AppLoadingIndicator(message: 'Creating tenant account...')
              : _isSuccess
              ? _buildSuccessMessage()
              : _buildForm(),
    );
  }

  Widget _buildSuccessMessage() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.check_circle_outline, color: Colors.green, size: 80),
          const SizedBox(height: 16),
          const Text(
            'Tenant Added Successfully!',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 32.0),
            child: Column(
              children: [
                const Text(
                  'A welcome email has been sent to the tenant with instructions to:',
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 12),
                _buildInstructionStep(1, 'Download the tenant app'),
                _buildInstructionStep(
                  2,
                  'Click "Forgot Password" to set their password',
                ),
                _buildInstructionStep(3, 'Log in with their email address'),
                const SizedBox(height: 12),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      _passwordResetSent
                          ? Icons.check_circle
                          : Icons.error_outline,
                      color: _passwordResetSent ? Colors.green : Colors.orange,
                    ),
                    const SizedBox(width: 8),
                    const Text(
                      'Password reset email',
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      _welcomeEmailSent
                          ? Icons.check_circle
                          : Icons.error_outline,
                      color: _welcomeEmailSent ? Colors.green : Colors.orange,
                    ),
                    const SizedBox(width: 8),
                    const Text('Welcome email', textAlign: TextAlign.center),
                  ],
                ),
              ],
            ),
          ),
          const SizedBox(height: 24),
          const Text(
            'Redirecting to tenant list...',
            style: TextStyle(fontStyle: FontStyle.italic),
          ),
        ],
      ),
    );
  }

  Widget _buildInstructionStep(int number, String text) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        children: [
          Container(
            width: 24,
            height: 24,
            decoration: const BoxDecoration(
              color: Colors.green,
              shape: BoxShape.circle,
            ),
            child: Center(
              child: Text(
                number.toString(),
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          const SizedBox(width: 8),
          Expanded(child: Text(text)),
        ],
      ),
    );
  }

  Widget _buildForm() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSectionHeader('Tenant Information'),

            // First Name and Last Name in a row
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // First Name - 50% width
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.only(right: 8, bottom: 16),
                    child: TextFormField(
                      controller: _firstNameController,
                      decoration: InputDecoration(
                        labelText: 'First Name *',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        prefixIcon: const Icon(Icons.person),
                        filled: true,
                        fillColor: Colors.grey.shade50,
                      ),
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'Please enter first name';
                        }
                        return null;
                      },
                    ),
                  ),
                ),
                // Last Name - 50% width
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.only(left: 8, bottom: 16),
                    child: TextFormField(
                      controller: _lastNameController,
                      decoration: InputDecoration(
                        labelText: 'Last Name *',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        prefixIcon: const Icon(Icons.person_outline),
                        filled: true,
                        fillColor: Colors.grey.shade50,
                      ),
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'Please enter last name';
                        }
                        return null;
                      },
                    ),
                  ),
                ),
              ],
            ),

            // Email - Full width
            Padding(
              padding: const EdgeInsets.only(bottom: 16),
              child: TextFormField(
                controller: _emailController,
                decoration: InputDecoration(
                  labelText: 'Email *',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  prefixIcon: const Icon(Icons.email),
                  filled: true,
                  fillColor: Colors.grey.shade50,
                  helperText: 'Enter tenant email (not your own email)',
                  helperStyle: TextStyle(fontSize: 12),
                ),
                keyboardType: TextInputType.emailAddress,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Please enter email';
                  }
                  if (!RegExp(
                    r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$',
                  ).hasMatch(value)) {
                    return 'Please enter a valid email';
                  }

                  // Check if email is the current user's email during validation
                  final email = value.trim();
                  if (_isCurrentUserEmail(email)) {
                    return 'You cannot add yourself as a tenant';
                  }

                  return null;
                },
              ),
            ),

            // Phone and Status in a row
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Phone - 50% width
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.only(right: 8, bottom: 24),
                    child: TextFormField(
                      controller: _phoneController,
                      decoration: InputDecoration(
                        labelText: 'Phone Number *',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        prefixIcon: const Icon(Icons.phone),
                        filled: true,
                        fillColor: Colors.grey.shade50,
                      ),
                      keyboardType: TextInputType.phone,
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'Please enter phone number';
                        }
                        return null;
                      },
                    ),
                  ),
                ),
                // Status dropdown - 50% width
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.only(left: 8, bottom: 24),
                    child: DropdownButtonFormField<TenantStatus>(
                      value: _selectedStatus,
                      decoration: InputDecoration(
                        labelText: 'Tenant Status',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        prefixIcon: const Icon(Icons.person_pin_circle),
                        filled: true,
                        fillColor: Colors.grey.shade50,
                      ),
                      items:
                          TenantStatus.values.map((status) {
                            String label;
                            Color textColor;

                            switch (status) {
                              case TenantStatus.active:
                                label = 'Active';
                                textColor = Colors.green;
                                break;
                              case TenantStatus.pending:
                                label = 'Pending';
                                textColor = Colors.orange;
                                break;
                              case TenantStatus.movedOut:
                                label = 'Moved Out';
                                textColor = Colors.grey;
                                break;
                            }

                            return DropdownMenuItem<TenantStatus>(
                              value: status,
                              child: Text(
                                label,
                                style: TextStyle(
                                  color: textColor,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            );
                          }).toList(),
                      onChanged: (value) {
                        if (value != null) {
                          setState(() {
                            _selectedStatus = value;
                          });
                        }
                      },
                    ),
                  ),
                ),
              ],
            ),

            _buildSectionHeader('Lease Information'),

            // Lease Start Date and End Date in a row
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Lease Start Date - 50% width
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.only(right: 8, bottom: 24),
                    child: InkWell(
                      onTap: () => _selectLeaseStartDate(context),
                      child: InputDecorator(
                        decoration: InputDecoration(
                          labelText: 'Lease Start Date',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          prefixIcon: const Icon(Icons.calendar_today),
                          filled: true,
                          fillColor: Colors.grey.shade50,
                          hintText: 'Optional',
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Expanded(
                              child: Text(
                                _leaseStartDate != null
                                    ? DateFormat(
                                      'MMM dd, yyyy',
                                    ).format(_leaseStartDate!)
                                    : 'Select Date',
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            const Icon(
                              Icons.arrow_drop_down,
                              color: Colors.grey,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
                // Lease End Date - 50% width
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.only(left: 8, bottom: 24),
                    child: InkWell(
                      onTap: () => _selectLeaseEndDate(context),
                      child: InputDecorator(
                        decoration: InputDecoration(
                          labelText: 'Lease End Date',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          prefixIcon: const Icon(Icons.event),
                          filled: true,
                          fillColor: Colors.grey.shade50,
                          hintText: 'Optional',
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Expanded(
                              child: Text(
                                _leaseEndDate != null
                                    ? DateFormat(
                                      'MMM dd, yyyy',
                                    ).format(_leaseEndDate!)
                                    : 'Select Date',
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            const Icon(
                              Icons.arrow_drop_down,
                              color: Colors.grey,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),

            _buildSectionHeader('Emergency Contact'),

            // Emergency Contact Name and Phone in a row
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Emergency Contact Name - 50% width
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.only(right: 8, bottom: 24),
                    child: TextFormField(
                      controller: _emergencyContactNameController,
                      decoration: InputDecoration(
                        labelText: 'Emergency Contact Name *',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        prefixIcon: const Icon(Icons.contacts),
                        filled: true,
                        fillColor: Colors.grey.shade50,
                      ),
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'Please enter emergency contact name';
                        }
                        return null;
                      },
                    ),
                  ),
                ),
                // Emergency Contact Phone - 50% width
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.only(left: 8, bottom: 24),
                    child: TextFormField(
                      controller: _emergencyContactPhoneController,
                      decoration: InputDecoration(
                        labelText: 'Emergency Contact Phone *',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        prefixIcon: const Icon(Icons.phone_forwarded),
                        filled: true,
                        fillColor: Colors.grey.shade50,
                      ),
                      keyboardType: TextInputType.phone,
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'Please enter emergency contact phone';
                        }
                        return null;
                      },
                    ),
                  ),
                ),
              ],
            ),

            _buildSectionHeader('Additional Information'),

            // Notes - Full width
            Padding(
              padding: const EdgeInsets.only(bottom: 24),
              child: TextFormField(
                controller: _notesController,
                decoration: InputDecoration(
                  labelText: 'Notes',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  prefixIcon: const Icon(Icons.note),
                  filled: true,
                  fillColor: Colors.grey.shade50,
                  hintText: 'Optional',
                ),
                maxLines: 3,
              ),
            ),

            // Error message
            if (_errorMessage.isNotEmpty)
              Container(
                padding: const EdgeInsets.all(12),
                margin: const EdgeInsets.only(bottom: 16),
                decoration: BoxDecoration(
                  color: Colors.red.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.red.shade200),
                ),
                width: double.infinity,
                child: Row(
                  children: [
                    Icon(Icons.error_outline, color: Colors.red.shade700),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        _errorMessage,
                        style: TextStyle(color: Colors.red.shade700),
                      ),
                    ),
                  ],
                ),
              ),

            // Submit button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _submitForm,
                icon: const Icon(Icons.person_add),
                label: const Text('Add Tenant'),
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16, top: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 4),
          Divider(color: Colors.grey.shade300, thickness: 1),
        ],
      ),
    );
  }
}
