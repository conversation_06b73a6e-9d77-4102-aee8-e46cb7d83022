name: url_launcher_example
description: Demonstrates the Windows implementation of the url_launcher plugin.
publish_to: none

environment:
  sdk: ^3.4.0
  flutter: ">=3.22.0"

dependencies:
  flutter:
    sdk: flutter
  url_launcher_platform_interface: ^2.2.0
  url_launcher_windows:
    # When depending on this package from a real application you should use:
    #   url_launcher_windows: ^x.y.z
    # See https://dart.dev/tools/pub/dependencies#version-constraints
    # The example app is bundled with the plugin so we use a path dependency on
    # the parent directory to use the current plugin's version.
    path: ../

dev_dependencies:
  flutter_test:
    sdk: flutter
  integration_test:
    sdk: flutter

flutter:
  uses-material-design: true
