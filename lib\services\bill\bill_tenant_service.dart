import 'package:supabase_flutter/supabase_flutter.dart';
import '../../models/bill/bill_tenant_relation.dart';
import '../../models/bill/bill.dart';
import '../../models/tenant/tenant.dart';
import '../../utils/logger.dart';

// Define enums for sorting options
enum BillTenantSortField {
  createdAt,
  updatedAt,
  splitAmount
}

// Define filter options for bill-tenant relations
class BillTenantFilter {
  final DateTime? startDate;
  final DateTime? endDate;
  final List<String>? billIds;
  final List<String>? tenantIds;
  final double? minSplitAmount;
  final double? maxSplitAmount;

  BillTenantFilter({
    this.startDate,
    this.endDate,
    this.billIds,
    this.tenantIds,
    this.minSplitAmount,
    this.maxSplitAmount,
  });
}

class BillTenantService {
  final SupabaseClient _client = Supabase.instance.client;
  final String _tableName = 'bill_tenants';

  // Create a new bill-tenant relation
  Future<BillTenantRelation> createBillTenantRelation(BillTenantRelation relation) async {
    try {
      final relationData = relation.toJson();
      
      final response =
          await _client
              .from(_tableName)
              .insert(relationData)
              .select()
              .single();

      return BillTenantRelation.fromJson(response);
    } catch (e) {
      throw Exception('Failed to create bill-tenant relation: $e');
    }
  }

  // Get relations by bill ID with optional sorting
  Future<List<BillTenantRelation>> getRelationsByBillId(
    String billId, {
    BillTenantSortField sortField = BillTenantSortField.createdAt,
    bool ascending = true,
  }) async {
    try {
      final String orderField = _getSortFieldString(sortField);
      
      final response = await _client
          .from(_tableName)
          .select()
          .eq('bill_id', billId)
          .order(orderField, ascending: ascending);

      return response.map<BillTenantRelation>((data) => BillTenantRelation.fromJson(data)).toList();
    } catch (e) {
      throw Exception('Failed to get bill-tenant relations: $e');
    }
  }

  // Get relations by tenant ID with optional sorting
  Future<List<BillTenantRelation>> getRelationsByTenantId(
    String tenantId, {
    BillTenantSortField sortField = BillTenantSortField.createdAt,
    bool ascending = true,
  }) async {
    try {
      final String orderField = _getSortFieldString(sortField);
      
      final response = await _client
          .from(_tableName)
          .select()
          .eq('tenant_id', tenantId)
          .order(orderField, ascending: ascending);

      return response.map<BillTenantRelation>((data) => BillTenantRelation.fromJson(data)).toList();
    } catch (e) {
      throw Exception('Failed to get tenant bill relations: $e');
    }
  }

  // Get tenants for a bill
  Future<List<Tenant>> getTenantsByBillId(String billId) async {
    try {
      final response = await _client
          .from(_tableName)
          .select('tenant_id')
          .eq('bill_id', billId);

      if (response.isEmpty) {
        return [];
      }

      final tenantIds = response.map<String>((data) => data['tenant_id'] as String).toList();
      
      // Get all tenants with these IDs
      final tenantsResponse = await _client
          .from('tenants')
          .select()
          .inFilter('id', tenantIds);

      return tenantsResponse.map<Tenant>((data) => Tenant.fromJson(data)).toList();
    } catch (e) {
      throw Exception('Failed to get tenants for bill: $e');
    }
  }

  // Get bills for a tenant with filtering and sorting options
  Future<List<Bill>> getBillsByTenantId(
    String tenantId, {
    DateTime? startDate,
    DateTime? endDate,
    List<BillStatus>? statusFilter,
    List<BillType>? typeFilter,
    String sortBy = 'due_date',
    bool ascending = true,
  }) async {
    try {
      final response = await _client
          .from(_tableName)
          .select('bill_id')
          .eq('tenant_id', tenantId);

      if (response.isEmpty) {
        return [];
      }

      final billIds = response.map<String>((data) => data['bill_id'] as String).toList();
      
      // Build the query for bills
      var query = _client
          .from('bills')
          .select()
          .inFilter('id', billIds);
      
      // Apply date filters if provided
      if (startDate != null) {
        query = query.gte('due_date', startDate.toIso8601String());
      }
      
      if (endDate != null) {
        query = query.lte('due_date', endDate.toIso8601String());
      }
      
      // Apply status filter if provided
      if (statusFilter != null && statusFilter.isNotEmpty) {
        final statusNames = statusFilter.map((s) => s.name).toList();
        query = query.inFilter('status', statusNames);
      }
      
      // Apply type filter if provided
      if (typeFilter != null && typeFilter.isNotEmpty) {
        final typeNames = typeFilter.map((t) => t.name).toList();
        query = query.inFilter('type', typeNames);
      }
      
      // Apply sorting
      final sortedQuery = query.order(sortBy, ascending: ascending);
      
      final billsResponse = await sortedQuery;
      return billsResponse.map<Bill>((data) => Bill.fromJson(data)).toList();
    } catch (e) {
      throw Exception('Failed to get bills for tenant: $e');
    }
  }

  // Get relations with advanced filtering
  Future<List<BillTenantRelation>> getRelationsWithFilter(
    BillTenantFilter filter, {
    BillTenantSortField sortField = BillTenantSortField.createdAt,
    bool ascending = true,
  }) async {
    try {
      final String orderField = _getSortFieldString(sortField);
      
      var query = _client.from(_tableName).select();
      
      // Apply filters
      if (filter.startDate != null) {
        query = query.gte('created_at', filter.startDate!.toIso8601String());
      }
      
      if (filter.endDate != null) {
        query = query.lte('created_at', filter.endDate!.toIso8601String());
      }
      
      if (filter.billIds != null && filter.billIds!.isNotEmpty) {
        query = query.inFilter('bill_id', filter.billIds!);
      }
      
      if (filter.tenantIds != null && filter.tenantIds!.isNotEmpty) {
        query = query.inFilter('tenant_id', filter.tenantIds!);
      }
      
      if (filter.minSplitAmount != null) {
        query = query.gte('split_amount', filter.minSplitAmount!);
      }
      
      if (filter.maxSplitAmount != null) {
        query = query.lte('split_amount', filter.maxSplitAmount!);
      }
      
      // Apply sorting
      final sortedQuery = query.order(orderField, ascending: ascending);
      
      final response = await sortedQuery;
      return response.map<BillTenantRelation>((data) => BillTenantRelation.fromJson(data)).toList();
    } catch (e) {
      AppLogger.error('Failed to get relations with filter: $e');
      throw Exception('Failed to get relations with filter: $e');
    }
  }

  // Get bills by multiple tenants with filtering options
  Future<List<Bill>> getBillsByMultipleTenants(
    List<String> tenantIds, {
    DateTime? startDate,
    DateTime? endDate,
    List<BillStatus>? statusFilter,
    List<BillType>? typeFilter,
    String sortBy = 'due_date',
    bool ascending = true,
  }) async {
    try {
      if (tenantIds.isEmpty) {
        return [];
      }
      
      final response = await _client
          .from(_tableName)
          .select('bill_id')
          .inFilter('tenant_id', tenantIds);

      if (response.isEmpty) {
        return [];
      }

      final billIds = response.map<String>((data) => data['bill_id'] as String).toList();
      // Remove duplicates
      final uniqueBillIds = billIds.toSet().toList();
      
      // Build the query for bills
      var query = _client
          .from('bills')
          .select()
          .inFilter('id', uniqueBillIds);
      
      // Apply date filters if provided
      if (startDate != null) {
        query = query.gte('due_date', startDate.toIso8601String());
      }
      
      if (endDate != null) {
        query = query.lte('due_date', endDate.toIso8601String());
      }
      
      // Apply status filter if provided
      if (statusFilter != null && statusFilter.isNotEmpty) {
        final statusNames = statusFilter.map((s) => s.name).toList();
        query = query.inFilter('status', statusNames);
      }
      
      // Apply type filter if provided
      if (typeFilter != null && typeFilter.isNotEmpty) {
        final typeNames = typeFilter.map((t) => t.name).toList();
        query = query.inFilter('type', typeNames);
      }
      
      // Apply sorting
      final sortedQuery = query.order(sortBy, ascending: ascending);
      
      final billsResponse = await sortedQuery;
      return billsResponse.map<Bill>((data) => Bill.fromJson(data)).toList();
    } catch (e) {
      AppLogger.error('Failed to get bills for multiple tenants: $e');
      throw Exception('Failed to get bills for multiple tenants: $e');
    }
  }

  // Create multiple bill-tenant relations at once
  Future<List<BillTenantRelation>> createMultipleRelations(
      String billId, List<String> tenantIds, {bool splitEvenly = false, double? billAmount}) async {
    try {
      List<BillTenantRelation> relations = [];
      
      // Calculate split amount if needed
      double? splitAmount;
      if (splitEvenly && billAmount != null && tenantIds.isNotEmpty) {
        splitAmount = billAmount / tenantIds.length;
      }
      
      // Create the relations data
      final List<Map<String, dynamic>> relationsData = tenantIds.map((tenantId) {
        return BillTenantRelation(
          billId: billId,
          tenantId: tenantId,
          splitAmount: splitAmount,
        ).toJson();
      }).toList();
      
      // Insert all relations at once
      final response = await _client
          .from(_tableName)
          .insert(relationsData)
          .select();
      
      // Convert response to BillTenantRelation objects
      relations = response.map<BillTenantRelation>(
        (data) => BillTenantRelation.fromJson(data)
      ).toList();
      
      return relations;
    } catch (e) {
      throw Exception('Failed to create multiple bill-tenant relations: $e');
    }
  }

  // Delete all relations for a bill
  Future<void> deleteRelationsForBill(String billId) async {
    try {
      await _client
          .from(_tableName)
          .delete()
          .eq('bill_id', billId);
    } catch (e) {
      throw Exception('Failed to delete bill-tenant relations: $e');
    }
  }

  // Delete specific relation by ID
  Future<void> deleteRelation(String relationId) async {
    try {
      await _client
          .from(_tableName)
          .delete()
          .eq('id', relationId);
    } catch (e) {
      AppLogger.error('Failed to delete relation: $e');
      throw Exception('Failed to delete relation: $e');
    }
  }

  // Update a bill-tenant relation
  Future<BillTenantRelation> updateRelation(BillTenantRelation relation) async {
    try {
      final relationData = relation.toJson();
      
      final response =
          await _client
              .from(_tableName)
              .update(relationData)
              .eq('id', relation.id)
              .select()
              .single();

      return BillTenantRelation.fromJson(response);
    } catch (e) {
      throw Exception('Failed to update bill-tenant relation: $e');
    }
  }

  // Get tenants with outstanding bills
  Future<List<Tenant>> getTenantsWithOutstandingBills() async {
    try {
      // Get all pending bills
      final pendingBills = await _client
          .from('bills')
          .select('id')
          .eq('status', BillStatus.pending.name);
      
      if (pendingBills.isEmpty) {
        return [];
      }
      
      final pendingBillIds = pendingBills.map<String>((data) => data['id'] as String).toList();
      
      // Get tenant IDs with these pending bills and sort them
      final response = await _client
          .from(_tableName)
          .select('tenant_id')
          .inFilter('bill_id', pendingBillIds)
          .order('tenant_id');
      
      if (response.isEmpty) {
        return [];
      }
      
      // Extract unique tenant IDs
      final tenantIds = response
          .map<String>((data) => data['tenant_id'] as String)
          .toSet()
          .toList();
      
      // Get tenant details
      final tenantsResponse = await _client
          .from('tenants')
          .select()
          .inFilter('id', tenantIds);
      
      return tenantsResponse.map<Tenant>((data) => Tenant.fromJson(data)).toList();
    } catch (e) {
      AppLogger.error('Failed to get tenants with outstanding bills: $e');
      throw Exception('Failed to get tenants with outstanding bills: $e');
    }
  }

  // Get total split amount for a tenant across all bills
  Future<double> getTotalSplitAmountForTenant(String tenantId) async {
    try {
      final response = await _client
          .from(_tableName)
          .select('split_amount')
          .eq('tenant_id', tenantId)
          .not('split_amount', 'is', null);
      
      if (response.isEmpty) {
        return 0.0;
      }
      
      double total = 0.0;
      for (var item in response) {
        total += (item['split_amount'] as num).toDouble();
      }
      
      return total;
    } catch (e) {
      AppLogger.error('Failed to get total split amount: $e');
      throw Exception('Failed to get total split amount: $e');
    }
  }

  // Helper method to convert enum to string field name
  String _getSortFieldString(BillTenantSortField field) {
    switch (field) {
      case BillTenantSortField.createdAt:
        return 'created_at';
      case BillTenantSortField.updatedAt:
        return 'updated_at';
      case BillTenantSortField.splitAmount:
        return 'split_amount';
    }
  }
} 