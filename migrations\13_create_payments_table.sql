-- Create payments table to track payment transactions
CREATE TABLE IF NOT EXISTS payments (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  tenant_id UUID REFERENCES tenants(id) ON DELETE SET NULL,
  bill_ids UUID[] NOT NULL, -- Array of bill IDs this payment applies to
  amount DECIMAL(10, 2) NOT NULL,
  payment_date TIMESTAMPTZ NOT NULL,
  status VARCHAR(20) NOT NULL DEFAULT 'pending', -- pending, verified, rejected
  method VARCHAR(20) NOT NULL, -- cash, bankTransfer, mobileMoney, check, other
  receipt_reference VARCHAR(100),
  notes TEXT,
  proof_image_url TEXT,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ,
  verified_by UUID REFERENCES auth.users(id) ON DELETE SET NULL
);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_payments_tenant_id ON payments(tenant_id);
CREATE INDEX IF NOT EXISTS idx_payments_payment_date ON payments(payment_date);
CREATE INDEX IF NOT EXISTS idx_payments_status ON payments(status);

-- Create a GIN index for the bill_ids array for efficient searching
CREATE INDEX IF NOT EXISTS idx_payments_bill_ids ON payments USING GIN (bill_ids);

-- Create view for tenant payment history
CREATE OR REPLACE VIEW tenant_payments_view 
WITH (security_invoker=true)
AS
SELECT 
  p.id as payment_id,
  p.tenant_id,
  p.bill_ids,
  p.amount,
  p.payment_date,
  p.status,
  p.method,
  p.receipt_reference,
  p.notes,
  p.proof_image_url,
  p.created_at,
  p.updated_at,
  t.first_name || ' ' || t.last_name as tenant_name,
  t.email as tenant_email
FROM 
  payments p
LEFT JOIN 
  tenants t ON p.tenant_id = t.id;

-- Add RLS policies for payments table
ALTER TABLE payments ENABLE ROW LEVEL SECURITY;

-- Policy for users to see payments
CREATE POLICY payment_select_policy ON payments
  FOR SELECT 
  USING (
    -- Allow authenticated users to see payments
    (SELECT auth.role()) = 'authenticated'
  );

-- Policy for users to insert payments
CREATE POLICY payment_insert_policy ON payments
  FOR INSERT 
  WITH CHECK (
    -- Allow authenticated users to create payments
    (SELECT auth.role()) = 'authenticated'
  );

-- Policy for users to update payments
CREATE POLICY payment_update_policy ON payments
  FOR UPDATE 
  USING (
    -- Allow authenticated users to update payments
    (SELECT auth.role()) = 'authenticated'
  );

-- Policy for users to delete payments
CREATE POLICY payment_delete_policy ON payments
  FOR DELETE 
  USING (
    -- Allow authenticated users to delete payments
    (SELECT auth.role()) = 'authenticated'
  ); 