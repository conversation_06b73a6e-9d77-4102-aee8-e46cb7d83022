import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../models/bill/bill.dart';
import '../../models/payment/payment_model.dart';
import '../../models/tenant/tenant.dart';
import '../../services/service_locator.dart';
import '../../utils/currency_formatter.dart';

class RecordMultipleBillsPaymentPage extends StatefulWidget {
  final Tenant tenant;
  final List<Bill> bills;
  final double totalAmount;

  const RecordMultipleBillsPaymentPage({
    super.key,
    required this.tenant,
    required this.bills,
    required this.totalAmount,
  });

  @override
  State<RecordMultipleBillsPaymentPage> createState() =>
      _RecordMultipleBillsPaymentPageState();
}

class _RecordMultipleBillsPaymentPageState
    extends State<RecordMultipleBillsPaymentPage> {
  final _formKey = GlobalKey<FormState>();
  final _amountController = TextEditingController();
  final _notesController = TextEditingController();
  final _receiptReferenceController = TextEditingController();
  final _transactionCodeController = TextEditingController();
  
  bool _isSubmitting = false;
  PaymentMethod _selectedPaymentMethod = PaymentMethod.cash;
  DateTime _paymentDate = DateTime.now();
  String? _generatedReceiptNumber;
  
  // Property and room details
  String? _propertyName;
  String? _roomName;

  @override
  void initState() {
    super.initState();
    _amountController.text = widget.totalAmount.toStringAsFixed(2);
    _loadGeneratedReceiptNumber();
    _loadTenantDetails();
  }

  Future<void> _loadGeneratedReceiptNumber() async {
    try {
      // We'll just display a placeholder until the actual payment is created
      setState(() {
        _generatedReceiptNumber = "Auto-generated on save";
      });
    } catch (e) {
      // Handle error silently
    }
  }

  Future<void> _loadTenantDetails() async {
    try {
      if (widget.tenant.roomId != null) {
        final room = await serviceLocator.roomService.getRoomById(widget.tenant.roomId!);
        if (room != null) {
          setState(() {
            _roomName = room.name;
          });
          
          final property = await serviceLocator.propertyService.getPropertyById(room.propertyId);
          if (property != null) {
            setState(() {
              _propertyName = property.name;
            });
          }
        }
      }
    } catch (e) {
      // Handle error silently
    }
  }

  @override
  void dispose() {
    _amountController.dispose();
    _notesController.dispose();
    _receiptReferenceController.dispose();
    _transactionCodeController.dispose();
    super.dispose();
  }

  Future<void> _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _paymentDate,
      firstDate: DateTime(2000),
      lastDate: DateTime.now().add(const Duration(days: 1)),
    );
    if (picked != null && picked != _paymentDate) {
      setState(() {
        _paymentDate = picked;
      });
    }
  }

  Future<void> _submitPayment() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    // Show confirmation dialog with bill details
    final bool? confirmed = await showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Confirm Payment'),
          content: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'By proceeding, you will record a payment of ${CurrencyFormatter.formatCurrency(double.parse(_amountController.text))} '
                  'for ${widget.tenant.firstName} ${widget.tenant.lastName}.\n'
                ),
                const SizedBox(height: 8),
                const Text('This payment will be applied to the following bills:'),
                const SizedBox(height: 8),
                ...widget.bills.map((bill) => Padding(
                  padding: const EdgeInsets.only(bottom: 4.0),
                  child: Text('• ${bill.title} (${bill.billNumber}): ${CurrencyFormatter.formatCurrency(bill.getRemainingAmount())}'),
                )),
                const SizedBox(height: 12),
                const Text('This action cannot be undone. Do you want to continue?', 
                  style: TextStyle(fontWeight: FontWeight.bold)),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('CANCEL'),
            ),
            FilledButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: const Text('CONFIRM'),
            ),
          ],
        );
      },
    );

    if (confirmed != true) {
      return;
    }

    setState(() {
      _isSubmitting = true;
    });

    try {
      final amount = double.parse(_amountController.text);
      
      // Get reference based on payment method
      String? reference;
      if (_selectedPaymentMethod == PaymentMethod.mobileMoney && 
          _transactionCodeController.text.isNotEmpty) {
        reference = _transactionCodeController.text.trim();
      }
      
      // Create a new payment record
      final payment = Payment(
        tenantId: widget.tenant.id,
        billIds: widget.bills.map((bill) => bill.id).toList(),
        amount: amount,
        paymentDate: _paymentDate,
        method: _selectedPaymentMethod,
        receiptReference: reference,
        notes: _notesController.text.trim(),
      );
      
      // Save the payment
      await serviceLocator.paymentService.createPayment(payment);

      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Payment recorded successfully')),
      );

      // Return true to indicate success
      Navigator.of(context).pop(true);
    } catch (e) {
      if (!mounted) return;

      setState(() {
        _isSubmitting = false;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error recording payment: $e')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Record Payment')),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Tenant info card - updated design
              Card(
                margin: const EdgeInsets.only(bottom: 16.0),
                elevation: 0,
                color: Colors.grey[50],
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Tenant: ${widget.tenant.firstName} ${widget.tenant.lastName}',
                        style: const TextStyle(
                          fontSize: 16, 
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 8),
                      if (_roomName != null)
                        Row(
                          children: [
                            Icon(Icons.home, size: 14, color: Colors.grey[600]),
                            const SizedBox(width: 4),
                            Text(
                              'House: $_roomName',
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.grey[800],
                              ),
                            ),
                          ],
                        ),
                      if (_propertyName != null)
                        Padding(
                          padding: const EdgeInsets.only(top: 4.0),
                          child: Row(
                            children: [
                              Icon(Icons.location_on, size: 14, color: Colors.grey[600]),
                              const SizedBox(width: 4),
                              Text(
                                'Property: $_propertyName',
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Colors.grey[800],
                                ),
                              ),
                            ],
                          ),
                        ),
                      if (widget.tenant.phoneNumber != null && widget.tenant.phoneNumber!.isNotEmpty)
                        Padding(
                          padding: const EdgeInsets.only(top: 4.0),
                          child: Row(
                            children: [
                              Icon(Icons.phone, size: 14, color: Colors.grey[600]),
                              const SizedBox(width: 4),
                              Text(
                                widget.tenant.phoneNumber!,
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Colors.grey[800],
                                ),
                              ),
                            ],
                          ),
                        ),
                    ],
                  ),
                ),
              ),

              // Selected bills summary
              Card(
                margin: const EdgeInsets.only(bottom: 24.0),
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Selected Bills',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      const SizedBox(height: 8),
                      ...widget.bills.map((bill) => Padding(
                        padding: const EdgeInsets.symmetric(vertical: 4.0),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Expanded(
                              child: Text(
                                bill.title,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            Text(
                              CurrencyFormatter.formatCurrency(
                                bill.getRemainingAmount(),
                              ),
                            ),
                          ],
                        ),
                      )),
                      const Divider(),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const Text(
                            'Total',
                            style: TextStyle(fontWeight: FontWeight.bold),
                          ),
                          Text(
                            CurrencyFormatter.formatCurrency(widget.totalAmount),
                            style: const TextStyle(fontWeight: FontWeight.bold),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),

              // Payment details
              Text(
                'Payment Details',
                style: Theme.of(context).textTheme.titleMedium,
              ),
              const SizedBox(height: 16),

              // Payment method
              DropdownButtonFormField<PaymentMethod>(
                decoration: const InputDecoration(
                  labelText: 'Payment Method',
                  border: OutlineInputBorder(),
                ),
                value: _selectedPaymentMethod,
                items: PaymentMethod.values.map((method) {
                  return DropdownMenuItem<PaymentMethod>(
                    value: method,
                    child: Text(_getPaymentMethodName(method)),
                  );
                }).toList(),
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _selectedPaymentMethod = value;
                    });
                  }
                },
              ),

              // Payment date
              const SizedBox(height: 16),
              InkWell(
                onTap: () => _selectDate(context),
                child: InputDecorator(
                  decoration: const InputDecoration(
                    labelText: 'Payment Date',
                    border: OutlineInputBorder(),
                    suffixIcon: Icon(Icons.calendar_today),
                  ),
                  child: Text(
                    '${_paymentDate.day}/${_paymentDate.month}/${_paymentDate.year}',
                  ),
                ),
              ),

              // Amount field
              const SizedBox(height: 16),
              TextFormField(
                controller: _amountController,
                decoration: InputDecoration(
                  labelText: 'Payment Amount',
                  prefixText: '${CurrencyFormatter.getCurrencySymbol()} ',
                  border: const OutlineInputBorder(),
                ),
                keyboardType: const TextInputType.numberWithOptions(
                  decimal: true,
                ),
                inputFormatters: [
                  FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d{0,2}')),
                ],
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter an amount';
                  }

                  final amount = double.tryParse(value);
                  if (amount == null) {
                    return 'Please enter a valid amount';
                  }

                  if (amount <= 0) {
                    return 'Amount must be greater than zero';
                  }

                  return null;
                },
              ),

              // Receipt reference field
              const SizedBox(height: 16),
              InputDecorator(
                decoration: InputDecoration(
                  labelText: 'Receipt Number',
                  border: const OutlineInputBorder(),
                  contentPadding: const EdgeInsets.symmetric(horizontal: 10, vertical: 8),
                ),
                child: Row(
                  children: [
                    Icon(Icons.receipt, color: Colors.grey[600], size: 18),
                    const SizedBox(width: 8),
                    Text(
                      _generatedReceiptNumber ?? "Loading...",
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey[800],
                      ),
                    ),
                  ],
                ),
              ),
              
              // Transaction code field (visible only for mobile money)
              if (_selectedPaymentMethod == PaymentMethod.mobileMoney)
                Padding(
                  padding: const EdgeInsets.only(top: 16.0),
                  child: TextFormField(
                    controller: _transactionCodeController,
                    decoration: const InputDecoration(
                      labelText: 'Transaction Code',
                      hintText: 'Enter mobile money transaction code',
                      border: OutlineInputBorder(),
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Transaction code is required for mobile money payments';
                      }
                      return null;
                    },
                  ),
                ),

              // Notes field
              const SizedBox(height: 16),
              TextFormField(
                controller: _notesController,
                decoration: const InputDecoration(
                  labelText: 'Notes (optional)',
                  hintText: 'Add any relevant payment details',
                  border: OutlineInputBorder(),
                ),
                maxLines: 3,
              ),

              // Submit button
              const SizedBox(height: 32),
              SizedBox(
                width: double.infinity,
                height: 50,
                child: ElevatedButton(
                  onPressed: _isSubmitting ? null : _submitPayment,
                  child: _isSubmitting
                      ? const CircularProgressIndicator()
                      : const Text('Record Payment'),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _getPaymentMethodName(PaymentMethod method) {
    switch (method) {
      case PaymentMethod.cash:
        return 'Cash';
      case PaymentMethod.bankTransfer:
        return 'Bank Transfer';
      case PaymentMethod.mobileMoney:
        return 'Mobile Money';
      case PaymentMethod.check:
        return 'Check';
      case PaymentMethod.other:
        return 'Other';
    }
  }
} 