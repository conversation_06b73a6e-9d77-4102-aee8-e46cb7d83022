import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../services/service_locator.dart';
import '../../widgets/ui_components.dart';
import 'new_password_screen.dart';

class PasswordResetOTPScreen extends StatefulWidget {
  final String email;

  const PasswordResetOTPScreen({
    super.key,
    required this.email,
  });

  @override
  State<PasswordResetOTPScreen> createState() => PasswordResetOTPScreenState();
}

class PasswordResetOTPScreenState extends State<PasswordResetOTPScreen> {
  final _formKey = GlobalKey<FormState>();
  final List<TextEditingController> _otpControllers = List.generate(
    6, 
    (index) => TextEditingController()
  );
  final List<FocusNode> _focusNodes = List.generate(
    6, 
    (index) => FocusNode()
  );
  bool _isLoading = false;
  bool _isResending = false;
  int _resendCountdown = 0;

  @override
  void initState() {
    super.initState();
    // Start countdown for resend button
    _startResendCountdown();
  }

  @override
  void dispose() {
    for (var controller in _otpControllers) {
      controller.dispose();
    }
    for (var focusNode in _focusNodes) {
      focusNode.dispose();
    }
    super.dispose();
  }

  void _startResendCountdown() {
    setState(() {
      _resendCountdown = 60; // 60 seconds countdown
    });

    Future.delayed(const Duration(seconds: 1), () {
      if (mounted && _resendCountdown > 0) {
        setState(() {
          _resendCountdown--;
        });
        _startResendCountdown();
      }
    });
  }

  // Get complete OTP code
  String _getOtpCode() {
    return _otpControllers.map((controller) => controller.text).join();
  }

  // Move to next field when digit is entered
  void _onOtpDigitChanged(String value, int index) {
    if (value.isNotEmpty) {
      if (index < 5) {
        // Move to next field
        _focusNodes[index + 1].requestFocus();
      } else {
        // Submit when the last digit is entered
        _focusNodes[index].unfocus();
        _verifyOTP();
      }
    }
  }

  // Verify OTP
  Future<void> _verifyOTP() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    final otpCode = _getOtpCode();
    if (otpCode.length != 6) {
      showErrorMessage(context, 'Please enter the complete 6-digit code');
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final response = await serviceLocator.authService.verifyEmailOTP(
        email: widget.email,
        token: otpCode,
        type: OtpType.recovery,
      );

      if (mounted) {
        if (response.user != null) {
          showSuccessMessage(context, 'Code verified successfully!');
          
          // Navigate to new password screen
          Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) => NewPasswordScreen(
                email: widget.email,
              ),
            ),
          );
        } else {
          showErrorMessage(context, 'Invalid verification code. Please try again.');
        }
      }
    } catch (error) {
      if (mounted) {
        showErrorMessage(
          context,
          'Error verifying code: ${error.toString()}',
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // Resend OTP
  Future<void> _resendOTP() async {
    if (_resendCountdown > 0) {
      return;
    }

    setState(() {
      _isResending = true;
    });

    try {
      await serviceLocator.authService.requestEmailOTP(
        email: widget.email,
      );

      if (mounted) {
        showSuccessMessage(context, 'Verification code resent successfully!');
        
        // Reset OTP fields
        for (var controller in _otpControllers) {
          controller.clear();
        }
        // Focus on first field
        _focusNodes[0].requestFocus();
        
        // Start countdown again
        _startResendCountdown();
      }
    } catch (error) {
      if (mounted) {
        showErrorMessage(
          context,
          'Error resending code: ${error.toString()}',
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isResending = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Verify Code'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: Center(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(24.0),
          child: Form(
            key: _formKey,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  Icons.lock_reset,
                  size: 80,
                  color: Colors.blue,
                ),
                const SizedBox(height: 24),
                const Text(
                  'Verify Your Code',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),
                Text(
                  'We\'ve sent a verification code to:',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.grey[700],
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 8),
                Text(
                  widget.email,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 32),

                // OTP input fields
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: List.generate(
                    6,
                    (index) => Container(
                      width: 45,
                      height: 55,
                      margin: const EdgeInsets.symmetric(horizontal: 5),
                      child: TextFormField(
                        controller: _otpControllers[index],
                        focusNode: _focusNodes[index],
                        decoration: InputDecoration(
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          contentPadding: EdgeInsets.zero,
                          counter: const SizedBox.shrink(),
                        ),
                        textAlign: TextAlign.center,
                        keyboardType: TextInputType.number,
                        maxLength: 1,
                        onChanged: (value) => _onOtpDigitChanged(value, index),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return '';
                          }
                          if (!RegExp(r'^\d$').hasMatch(value)) {
                            return '';
                          }
                          return null;
                        },
                        style: const TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 16),
                if (_formKey.currentState?.validate() == false)
                  const Text(
                    'Please enter all digits of the verification code',
                    style: TextStyle(color: Colors.red, fontSize: 12),
                  ),
                const SizedBox(height: 32),

                // Verify Button
                CustomButton(
                  text: 'Verify Code',
                  onPressed: _verifyOTP,
                  isLoading: _isLoading,
                ),
                const SizedBox(height: 24),

                // Resend Code
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Text('Didn\'t receive a code?'),
                    TextButton(
                      onPressed: _resendCountdown > 0 || _isResending ? null : _resendOTP,
                      child: _isResending
                          ? const CircularProgressIndicator.adaptive(
                              valueColor: AlwaysStoppedAnimation<Color>(Colors.blue),
                              strokeWidth: 2,
                            )
                          : Text(
                              _resendCountdown > 0
                                  ? 'Resend in $_resendCountdown s'
                                  : 'Resend Code',
                            ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
} 