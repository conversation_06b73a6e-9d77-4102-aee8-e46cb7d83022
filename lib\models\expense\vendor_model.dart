class VendorModel {
  final String? id;
  final String name;
  final String? description;
  final String? contactPerson;
  final String? phone;
  final String? email;
  final String? address;
  final String? website;
  final String? categoryId;
  final String? categoryName; // For display purposes
  final bool isPreferred;
  final double? rating;
  final String userId;
  final DateTime createdAt;
  final DateTime? updatedAt;

  VendorModel({
    this.id,
    required this.name,
    this.description,
    this.contactPerson,
    this.phone,
    this.email,
    this.address,
    this.website,
    this.categoryId,
    this.categoryName,
    this.isPreferred = false,
    this.rating,
    required this.userId,
    required this.createdAt,
    this.updatedAt,
  });

  factory VendorModel.fromJson(Map<String, dynamic> json) {
    return VendorModel(
      id: json['id'],
      name: json['name'],
      description: json['description'],
      contactPerson: json['contact_person'],
      phone: json['phone'],
      email: json['email'],
      address: json['address'],
      website: json['website'],
      categoryId: json['category_id'],
      categoryName: json['category_name'],
      isPreferred: json['is_preferred'] ?? false,
      rating: json['rating']?.toDouble(),
      userId: json['user_id'],
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: json['updated_at'] != null
          ? DateTime.parse(json['updated_at'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'contact_person': contactPerson,
      'phone': phone,
      'email': email,
      'address': address,
      'website': website,
      'category_id': categoryId,
      'is_preferred': isPreferred,
      'rating': rating,
      'user_id': userId,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  VendorModel copyWith({
    String? id,
    String? name,
    String? description,
    String? contactPerson,
    String? phone,
    String? email,
    String? address,
    String? website,
    String? categoryId,
    String? categoryName,
    bool? isPreferred,
    double? rating,
    String? userId,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return VendorModel(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      contactPerson: contactPerson ?? this.contactPerson,
      phone: phone ?? this.phone,
      email: email ?? this.email,
      address: address ?? this.address,
      website: website ?? this.website,
      categoryId: categoryId ?? this.categoryId,
      categoryName: categoryName ?? this.categoryName,
      isPreferred: isPreferred ?? this.isPreferred,
      rating: rating ?? this.rating,
      userId: userId ?? this.userId,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
} 