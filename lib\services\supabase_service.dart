import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/bill/bill.dart';
import '../utils/logger.dart';
import 'dart:async';

class SupabaseService {
  static final SupabaseService instance = SupabaseService._internal();
  final SupabaseClient _client = Supabase.instance.client;
  
  // Getter for client
  SupabaseClient get client => _client;
  
  // Token refresh lock to prevent multiple simultaneous refresh attempts
  bool _isRefreshing = false;
  final _refreshCompleter = Completer<void>();

  SupabaseService._internal();
  
  /// Refreshes the access token if it's expired
  Future<bool> refreshToken() async {
    try {
      // Check if already refreshing
      if (_isRefreshing) {
        await _refreshCompleter.future;
        return true;
      }

      _isRefreshing = true;
      
      final session = _client.auth.currentSession;
      if (session == null) {
        AppLogger.error('No session available for refresh');
        _completeRefresh();
        return false;
      }
      
      // If refresh token is expired, we need to log in again
      final refreshToken = session.refreshToken;
      final refreshTokenExpiresIn = session.expiresIn;
      final now = DateTime.now();
      final refreshTokenExpiryTime = now.add(Duration(seconds: refreshTokenExpiresIn ?? 0));
      
      if (refreshToken != null && 
          DateTime.now().isAfter(refreshTokenExpiryTime)) {
        AppLogger.info('Refresh token expired, user needs to log in again');
        _completeRefresh();
        return false;
      }

      // Use the built-in refresh token mechanism
      await _client.auth.refreshSession();
      AppLogger.info('Token refreshed successfully');
      _completeRefresh();
      return true;
    } catch (e) {
      AppLogger.error('Error refreshing token: $e');
      _completeRefresh();
      return false;
    }
  }
  
  void _completeRefresh() {
    _isRefreshing = false;
    if (!_refreshCompleter.isCompleted) {
      _refreshCompleter.complete();
    }
  }

  /// Execute a Supabase function with automatic token refresh on JWT expired errors
  Future<T> executeWithRefresh<T>(Future<T> Function() operation) async {
    try {
      return await operation();
    } on AuthException catch (e) {
      // Convert statusCode to int before comparison
      final statusCodeInt = int.tryParse(e.statusCode ?? '');
      if (statusCodeInt == 401) {
        AppLogger.info('Auth error, attempting to refresh token');
        final refreshed = await refreshToken();
        if (refreshed) {
          // Retry the operation
          return await operation();
        }
      }
      rethrow;
    } on PostgrestException catch (e) {
      if (e.code == 'PGRST301' && e.message == 'JWT expired') {
        AppLogger.info('JWT expired, attempting to refresh token');
        final refreshed = await refreshToken();
        if (refreshed) {
          // Retry the operation
          return await operation();
        }
      }
      rethrow;
    }
  }

  Future<Bill> createBill(Bill bill) async {
    return await executeWithRefresh(() async {
      final response =
          await _client.from('bills').insert(bill.toJson()).select().single();
      return Bill.fromJson(response);
    });
  }

  Future<Bill> updateBill(Bill bill) async {
    return await executeWithRefresh(() async {
      final response =
          await _client
              .from('bills')
              .update(bill.toJson())
              .eq('id', bill.id)
              .select()
              .single();
      return Bill.fromJson(response);
    });
  }

  Future<List<Bill>> getBills({String? tenantId}) async {
    return await executeWithRefresh(() async {
      var query = _client.from('bills').select();
      if (tenantId != null) {
        query = query.eq('tenant_id', tenantId);
      }
      final response = await query;
      return response.map((json) => Bill.fromJson(json)).toList();
    });
  }

  Future<void> deleteBill(String id) async {
    return await executeWithRefresh(() async {
      await _client.from('bills').delete().eq('id', id);
    });
  }
}
