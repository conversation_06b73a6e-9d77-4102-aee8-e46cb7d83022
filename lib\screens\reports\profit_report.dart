import 'package:flutter/material.dart';
import '../../widgets/navigation/app_drawer.dart';
import '../../services/service_locator.dart';
import '../../models/payment/payment_model.dart';
import '../../models/expense/expense_model.dart';
import '../../utils/currency_formatter.dart';
import 'date_range_selector.dart';
import '../../utils/logger.dart';
import 'dart:math' as math;

class ProfitReport extends StatefulWidget {
  const ProfitReport({super.key});

  @override
  State<ProfitReport> createState() => _ProfitReportState();
}

class _ProfitReportState extends State<ProfitReport> {
  bool _isLoading = true;
  bool _hasError = false;
  String _errorMessage = '';
  // We don't need to store these lists since we extract the necessary data from them
  // and store it in other variables (_totalIncome, _totalExpenses, etc.)
  String _selectedPeriod = 'this_month'; // Removed 'final' to allow updates
  String _selectedView = 'summary'; // 'summary', 'property', 'monthly'
  Map<String, double> _profitByProperty = {};
  Map<int, Map<String, double>> _monthlyProfits = {};
  DateTimeRange? _selectedDateRange;
  double _totalIncome = 0;
  double _totalExpenses = 0;
  double _totalProfit = 0;
  
  // Add lists to store monthly payments and expenses for detailed view
  Map<int, List<Payment>> _monthlyPayments = {};
  Map<int, List<ExpenseModel>> _monthlyExpenses = {};
  
  @override
  void initState() {
    super.initState();
    // Initialize with current year date range
    final now = DateTime.now();
    _selectedDateRange = DateTimeRange(
      start: DateTime(now.year, 1, 1),
      end: DateTime(now.year, 12, 31),
    );
    _loadData();
  }
  
  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
      _hasError = false;
      _errorMessage = '';
    });
    
    try {
      // Load payments and expenses for the selected date range
      AppLogger.info('Loading data for profit report: ${_selectedDateRange?.start} to ${_selectedDateRange?.end}');
      
      final payments = await serviceLocator.paymentService.getAllPayments(
        startDate: _selectedDateRange?.start,
        endDate: _selectedDateRange?.end,
      );
      
      final expenses = await serviceLocator.expenseService.getExpenses(
        startDate: _selectedDateRange?.start,
        endDate: _selectedDateRange?.end,
      );
      
      AppLogger.info('Loaded ${payments.length} payments and ${expenses.length} expenses');
      
      // Calculate totals
      _totalIncome = payments.fold(0, (sum, payment) => sum + payment.amount);
      _totalExpenses = expenses.fold(0, (sum, expense) => sum + expense.amount);
      _totalProfit = _totalIncome - _totalExpenses;
      
      // Calculate profit by property
      await _calculateProfitByProperty(payments, expenses);
      
      // Calculate monthly profits
      _calculateMonthlyProfits(payments, expenses);
      
      // Organize payments and expenses by month for detailed view
      _organizeByMonth(payments, expenses);
      
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    } catch (e) {
      AppLogger.error('Failed to load data for profit report: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
          _hasError = true;
          _errorMessage = e.toString();
        });
        
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading profit report data: ${e.toString()}'),
            backgroundColor: Colors.red,
            action: SnackBarAction(
              label: 'Retry',
              onPressed: _loadData,
              textColor: Colors.white,
            ),
          ),
        );
      }
    }
  }
  
  // New method to organize payments and expenses by month
  void _organizeByMonth(List<Payment> payments, List<ExpenseModel> expenses) {
    final monthlyPayments = <int, List<Payment>>{};
    final monthlyExpenses = <int, List<ExpenseModel>>{};
    
    // Group payments by month
    for (final payment in payments) {
      final month = payment.paymentDate.month;
      monthlyPayments[month] = [...(monthlyPayments[month] ?? []), payment];
    }
    
    // Group expenses by month
    for (final expense in expenses) {
      final month = expense.date.month;
      monthlyExpenses[month] = [...(monthlyExpenses[month] ?? []), expense];
    }
    
    setState(() {
      _monthlyPayments = monthlyPayments;
      _monthlyExpenses = monthlyExpenses;
    });
  }
  
  Future<void> _calculateProfitByProperty(List<Payment> payments, List<ExpenseModel> expenses) async {
    final incomeByProperty = <String, double>{};
    final expensesByProperty = <String, double>{};
    final profitByProperty = <String, double>{};
    
    // First get all properties to ensure we have their names
    final allProperties = await serviceLocator.propertyService.getAllProperties();
    final propertyNameMap = {for (var p in allProperties) p.id: p.name};
    
    // Calculate income by property
    for (final payment in payments) {
      String? propertyId;
      
      try {
        // Try to get property ID from bill relation
        if (payment.billIds.isNotEmpty) {
          bool foundValidBill = false;

          // Try each bill ID until we find a valid one
          for (final billId in payment.billIds) {
            final bill = await serviceLocator.billService.getBillSafe(billId);

            if (bill != null) {
              if (bill.propertyId != null) {
                propertyId = bill.propertyId;
                foundValidBill = true;
                break;
              } else if (bill.tenantId != null) {
                // Try to get property through tenant's room
                final tenant = await serviceLocator.tenantService.getTenantById(bill.tenantId!);
                if (tenant != null && tenant.roomId != null) {
                  final room = await serviceLocator.roomService.getRoomById(tenant.roomId!);
                  if (room != null) {
                    propertyId = room.propertyId;
                    foundValidBill = true;
                    break;
                  }
                }
              }
            } else {
              AppLogger.warning('Bill not found with ID: $billId');
            }
          }

          if (foundValidBill && propertyId != null) {
            final propertyName = propertyNameMap[propertyId] ?? 'Unknown Property';
            incomeByProperty[propertyName] = (incomeByProperty[propertyName] ?? 0) + payment.amount;
          } else {
            // If we can't determine the property from any bill, put in "Other"
            AppLogger.warning('Could not determine property for payment ${payment.id} with bill IDs: ${payment.billIds}');
            incomeByProperty['Other'] = (incomeByProperty['Other'] ?? 0) + payment.amount;
          }
        } else {
          // Payment not associated with a bill
          incomeByProperty['Other'] = (incomeByProperty['Other'] ?? 0) + payment.amount;
        }
      } catch (e) {
        AppLogger.error('Error processing payment for property calculation: $e');
        // If there's an error, put in "Other"
        incomeByProperty['Other'] = (incomeByProperty['Other'] ?? 0) + payment.amount;
      }
    }
    
    // Calculate expenses by property
    for (final expense in expenses) {
      final propertyName = expense.propertyId != null 
          ? propertyNameMap[expense.propertyId] ?? 'Unknown Property'
          : 'General Expenses';
      
      expensesByProperty[propertyName] = (expensesByProperty[propertyName] ?? 0) + expense.amount;
    }
    
    // Calculate profit for each property
    final allPropertyNames = {...incomeByProperty.keys, ...expensesByProperty.keys};
    
    for (final propertyName in allPropertyNames) {
      final income = incomeByProperty[propertyName] ?? 0;
      final expense = expensesByProperty[propertyName] ?? 0;
      profitByProperty[propertyName] = income - expense;
    }
    
    setState(() {
      _profitByProperty = profitByProperty;
    });
  }
  
  void _calculateMonthlyProfits(List<Payment> payments, List<ExpenseModel> expenses) {
    final monthlyIncome = <int, double>{};
    final monthlyExpenses = <int, double>{};
    final monthlyProfits = <int, Map<String, double>>{};
    
    // Calculate monthly income
    for (final payment in payments) {
      final month = payment.paymentDate.month;
      monthlyIncome[month] = (monthlyIncome[month] ?? 0) + payment.amount;
    }
    
    // Calculate monthly expenses
    for (final expense in expenses) {
      final month = expense.date.month;
      monthlyExpenses[month] = (monthlyExpenses[month] ?? 0) + expense.amount;
    }
    
    // Calculate monthly profits
    for (int month = 1; month <= 12; month++) {
      final income = monthlyIncome[month] ?? 0;
      final expense = monthlyExpenses[month] ?? 0;
      final profit = income - expense;
      
      monthlyProfits[month] = {
        'income': income,
        'expenses': expense,
        'profit': profit,
      };
    }
    
    setState(() {
      _monthlyProfits = monthlyProfits;
    });
  }
  
  void _onDateRangeChanged(DateTimeRange dateRange) {
    setState(() {
      _selectedDateRange = dateRange;
    });
    _loadData();
  }
  
  void _onPeriodChanged(String period) {
    setState(() {
      _selectedPeriod = period;
    });
  }
  
  void _onViewChanged(String? view) {
    if (view != null) {
      setState(() {
        _selectedView = view;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Profit Report'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      drawer: const AppDrawer(),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _hasError
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(Icons.error_outline, size: 48, color: Colors.red),
                      const SizedBox(height: 16),
                      Text(
                        'Error loading data: $_errorMessage',
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: _loadData,
                        child: const Text('Retry'),
                      ),
                    ],
                  ),
                )
              : SingleChildScrollView(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      DateRangeSelector(
                        selectedRange: _selectedDateRange!,
                        onRangeSelected: _onDateRangeChanged,
                        selectedPeriod: _selectedPeriod,
                        onPeriodChanged: _onPeriodChanged,
                      ),
                      const SizedBox(height: 16),
                      _buildViewSelector(),
                      const SizedBox(height: 24),
                      _buildProfitSummary(),
                      const SizedBox(height: 24),
                      _selectedView == 'summary'
                          ? _buildSummaryView()
                          : _selectedView == 'property'
                              ? _buildPropertyView()
                              : _buildMonthlyView(),
                    ],
                  ),
                ),
    );
  }
  
  // Improve the view selector with a better design
  Widget _buildViewSelector() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(4),
        child: SegmentedButton<String>(
          style: ButtonStyle(
            backgroundColor: WidgetStateProperty.resolveWith<Color>(
              (Set<WidgetState> states) {
                if (states.contains(WidgetState.selected)) {
                  return Theme.of(context).colorScheme.primary;
                }
                return Colors.transparent;
              },
            ),
            foregroundColor: WidgetStateProperty.resolveWith<Color>(
              (Set<WidgetState> states) {
                if (states.contains(WidgetState.selected)) {
                  return Colors.white;
                }
                return Theme.of(context).colorScheme.onSurface;
              },
            ),
          ),
          segments: const [
            ButtonSegment<String>(
              value: 'summary',
              label: Text('Summary'),
              icon: Icon(Icons.summarize),
            ),
            ButtonSegment<String>(
              value: 'property',
              label: Text('By Property'),
              icon: Icon(Icons.home_work),
            ),
            ButtonSegment<String>(
              value: 'monthly',
              label: Text('Monthly'),
              icon: Icon(Icons.calendar_month),
            ),
          ],
          selected: {_selectedView},
          onSelectionChanged: (Set<String> selection) {
            _onViewChanged(selection.first);
          },
        ),
      ),
    );
  }
  
  Widget _buildEnhancedSummaryItem(String label, double amount, Color color, IconData icon) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: color.withAlpha((0.1 * 255).round()),
            shape: BoxShape.circle,
          ),
          child: Icon(icon, color: color, size: 24),
        ),
        const SizedBox(height: 8),
        Text(
          label,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 4),
        Tooltip(
          message: CurrencyFormatter.formatCurrency(amount),
          child: FittedBox(
            fit: BoxFit.scaleDown,
            child: Text(
              CurrencyFormatter.formatCurrency(amount),
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
          ),
        ),
      ],
    );
  }
  
  Widget _buildProfitSummary() {
    final profitColor = _totalProfit >= 0 ? Colors.green : Colors.red;
    
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.primary.withAlpha((0.1 * 255).round()),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.account_balance_wallet,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
                const SizedBox(width: 12),
                const Text(
                  'Profit Summary',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            const Divider(),
            const SizedBox(height: 8),
            IntrinsicHeight(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  Expanded(
                    child: _buildEnhancedSummaryItem(
                      'Total Income',
                      _totalIncome,
                      Colors.blue,
                      Icons.arrow_upward,
                    ),
                  ),
                  VerticalDivider(
                    thickness: 1,
                    width: 24,
                    color: Theme.of(context).dividerColor.withAlpha((0.5 * 255).round()),
                  ),
                  Expanded(
                    child: _buildEnhancedSummaryItem(
                      'Total Expenses',
                      _totalExpenses,
                      Colors.orange,
                      Icons.arrow_downward,
                    ),
                  ),
                  VerticalDivider(
                    thickness: 1,
                    width: 24,
                    color: Theme.of(context).dividerColor.withAlpha((0.5 * 255).round()),
                  ),
                  Expanded(
                    child: _buildEnhancedSummaryItem(
                      'Net Profit',
                      _totalProfit,
                      profitColor,
                      _totalProfit >= 0 ? Icons.trending_up : Icons.trending_down,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildSummaryView() {
    final profitMargin = _totalIncome > 0 
        ? (_totalProfit / _totalIncome * 100).toStringAsFixed(1) 
        : '0.0';
        
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.primary.withAlpha((0.1 * 255).round()),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.insights,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
                const SizedBox(width: 12),
                const Text(
                  'Financial Metrics',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            const Divider(),
            const SizedBox(height: 8),
            // Use a Table instead of rows to ensure proper alignment and no truncation
            Table(
              columnWidths: const {
                0: FlexColumnWidth(3),
                1: FlexColumnWidth(2),
              },
              children: [
                _buildMetricTableRow(
                  'Income to Expense Ratio', 
                  _totalExpenses > 0 ? (_totalIncome / _totalExpenses).toStringAsFixed(2) : 'N/A',
                  context,
                ),
                _buildMetricTableRow('Profit Margin', '$profitMargin%', context),
                _buildMetricTableRow('Most Profitable Property', _getMostProfitableProperty(), context),
                _buildMetricTableRow('Least Profitable Property', _getLeastProfitableProperty(), context),
                _buildMetricTableRow('Most Profitable Month', _getMostProfitableMonth(), context),
                _buildMetricTableRow('Least Profitable Month', _getLeastProfitableMonth(), context),
              ],
            ),
          ],
        ),
      ),
    );
  }
  
  TableRow _buildMetricTableRow(String label, String value, BuildContext context) {
    return TableRow(
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).dividerColor.withAlpha((0.2 * 255).round()),
            width: 1,
          ),
        ),
      ),
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 12),
          child: Text(
            label,
            style: TextStyle(
              fontSize: 14,
              color: Theme.of(context).textTheme.bodyMedium?.color,
            ),
          ),
        ),
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 12),
          child: Text(
            value,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: Theme.of(context).textTheme.bodyMedium?.color,
            ),
            textAlign: TextAlign.end,
          ),
        ),
      ],
    );
  }
  
  String _getMostProfitableProperty() {
    if (_profitByProperty.isEmpty) return 'No data';
    
    final sortedProperties = _profitByProperty.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));
    
    if (sortedProperties.isEmpty) return 'No data';
    
    final topProperty = sortedProperties.first;
    return '${topProperty.key} (${CurrencyFormatter.formatCurrency(topProperty.value)})';
  }
  
  String _getLeastProfitableProperty() {
    if (_profitByProperty.isEmpty) return 'No data';
    
    final sortedProperties = _profitByProperty.entries.toList()
      ..sort((a, b) => a.value.compareTo(b.value));
    
    if (sortedProperties.isEmpty) return 'No data';
    
    final bottomProperty = sortedProperties.first;
    return '${bottomProperty.key} (${CurrencyFormatter.formatCurrency(bottomProperty.value)})';
  }
  
  String _getMostProfitableMonth() {
    if (_monthlyProfits.isEmpty) return 'No data';
    
    final sortedMonths = _monthlyProfits.entries.toList()
      ..sort((a, b) => (b.value['profit'] ?? 0).compareTo(a.value['profit'] ?? 0));
    
    if (sortedMonths.isEmpty) return 'No data';
    
    final topMonth = sortedMonths.first;
    final monthName = _getMonthName(topMonth.key);
    return '$monthName (${CurrencyFormatter.formatCurrency(topMonth.value['profit'] ?? 0)})';
  }
  
  String _getLeastProfitableMonth() {
    if (_monthlyProfits.isEmpty) return 'No data';
    
    final sortedMonths = _monthlyProfits.entries.toList()
      ..sort((a, b) => (a.value['profit'] ?? 0).compareTo(b.value['profit'] ?? 0));
    
    if (sortedMonths.isEmpty) return 'No data';
    
    final bottomMonth = sortedMonths.first;
    final monthName = _getMonthName(bottomMonth.key);
    return '$monthName (${CurrencyFormatter.formatCurrency(bottomMonth.value['profit'] ?? 0)})';
  }
  
  String _getMonthName(int month) {
    const monthNames = [
      'January', 'February', 'March', 'April', 'May', 'June',
      'July', 'August', 'September', 'October', 'November', 'December'
    ];
    
    if (month >= 1 && month <= 12) {
      return monthNames[month - 1];
    }
    return 'Unknown';
  }
  
  Widget _buildPropertyView() {
    if (_profitByProperty.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.business,
              size: 48,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            const Text('No property data available for the selected period'),
          ],
        ),
      );
    }
    
    final sortedProperties = _profitByProperty.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));
    
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.primary.withAlpha((0.1 * 255).round()),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.home_work,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
                const SizedBox(width: 12),
                const Text(
                  'Profit by Property',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            const Divider(),
            const SizedBox(height: 8),
            ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: sortedProperties.length,
              separatorBuilder: (context, index) => const Divider(height: 24),
              itemBuilder: (context, index) {
                final property = sortedProperties[index];
                final isPositive = property.value >= 0;
                
                return Tooltip(
                  message: '${property.key}: ${CurrencyFormatter.formatCurrency(property.value)}',
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(
                            child: Text(
                              property.key,
                              style: const TextStyle(
                                fontWeight: FontWeight.w500,
                                fontSize: 15,
                              ),
                            ),
                          ),
                          const SizedBox(width: 8),
                          Text(
                            CurrencyFormatter.formatCurrency(property.value),
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: isPositive ? Colors.green : Colors.red,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Stack(
                        children: [
                          Container(
                            height: 8,
                            decoration: BoxDecoration(
                              color: Colors.grey[200],
                              borderRadius: BorderRadius.circular(4),
                            ),
                          ),
                          Container(
                            height: 8,
                            width: MediaQuery.of(context).size.width * 
                                (0.5 + (property.value / (_totalIncome > 0 ? _totalIncome * 2 : 1)))
                                .clamp(0.0, 1.0) * 
                                (MediaQuery.of(context).size.width - 64),
                            decoration: BoxDecoration(
                              color: isPositive ? Colors.green : Colors.red,
                              borderRadius: BorderRadius.circular(4),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }
  
  // Show detailed month dialog
  void _showMonthDetailDialog(BuildContext context, int month, Map<String, double> monthData) {
    final monthName = _getMonthName(month);
    final income = monthData['income'] ?? 0.0;
    final expenses = monthData['expenses'] ?? 0.0;
    final profit = monthData['profit'] ?? 0.0;
    final isPositive = profit >= 0;
    
    final payments = _monthlyPayments[month] ?? [];
    final expensesList = _monthlyExpenses[month] ?? [];
    
    showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        child: Container(
          width: double.maxFinite,
          constraints: BoxConstraints(
            maxWidth: 600,
            maxHeight: MediaQuery.of(context).size.height * 0.8,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Header
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primary,
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(16),
                    topRight: Radius.circular(16),
                  ),
                ),
                child: Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.white.withAlpha(51), // 0.2 * 255 = 51
                        shape: BoxShape.circle,
                      ),
                      child: Icon(
                        Icons.calendar_month,
                        color: Colors.white,
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        '$monthName Financial Details',
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                    ),
                    IconButton(
                      icon: const Icon(Icons.close, color: Colors.white),
                      onPressed: () => Navigator.of(context).pop(),
                    ),
                  ],
                ),
              ),
              
              // Summary Cards
              Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    Expanded(
                      child: _buildSummaryCard(
                        'Income',
                        income,
                        Colors.blue,
                        Icons.arrow_upward,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: _buildSummaryCard(
                        'Expenses',
                        expenses,
                        Colors.orange,
                        Icons.arrow_downward,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: _buildSummaryCard(
                        'Profit',
                        profit,
                        isPositive ? Colors.green : Colors.red,
                        isPositive ? Icons.trending_up : Icons.trending_down,
                      ),
                    ),
                  ],
                ),
              ),
              
              // Tabs for Income and Expenses
              Expanded(
                child: DefaultTabController(
                  length: 2,
                  child: Column(
                    children: [
                      TabBar(
                        tabs: [
                          Tab(
                            icon: const Icon(Icons.arrow_upward),
                            text: 'Income (${payments.length})',
                          ),
                          Tab(
                            icon: const Icon(Icons.arrow_downward),
                            text: 'Expenses (${expensesList.length})',
                          ),
                        ],
                        labelColor: Theme.of(context).colorScheme.primary,
                      ),
                      Expanded(
                        child: TabBarView(
                          children: [
                            // Income Tab
                            payments.isEmpty
                                ? _buildEmptyState('No income recorded for this month')
                                : ListView.builder(
                                    itemCount: payments.length,
                                    padding: const EdgeInsets.all(12),
                                    itemBuilder: (context, index) {
                                      final payment = payments[index];
                                      return Card(
                                        margin: const EdgeInsets.only(bottom: 8),
                                        child: InkWell(
                                          onTap: () => _openPaymentDetails(context, payment),
                                          borderRadius: BorderRadius.circular(8),
                                          child: Padding(
                                            padding: const EdgeInsets.all(12),
                                            child: Row(
                                              children: [
                                                CircleAvatar(
                                                  radius: 18,
                                                  backgroundColor: Colors.blue.withAlpha(30),
                                                  child: Icon(
                                                    payment.status == PaymentStatus.verified 
                                                        ? Icons.check_circle
                                                        : payment.status == PaymentStatus.rejected
                                                            ? Icons.cancel
                                                            : Icons.pending,
                                                    color: payment.status == PaymentStatus.verified
                                                        ? Colors.green
                                                        : payment.status == PaymentStatus.rejected
                                                            ? Colors.red
                                                            : Colors.orange,
                                                    size: 18,
                                                  ),
                                                ),
                                                const SizedBox(width: 12),
                                                Expanded(
                                                  child: Column(
                                                    crossAxisAlignment: CrossAxisAlignment.start,
                                                    children: [
                                                      Text(
                                                        payment.receiptReference ?? 
                                                            'Payment #${payment.id.substring(0, math.min(8, payment.id.length))}',
                                                        style: const TextStyle(
                                                          fontWeight: FontWeight.bold,
                                                          fontSize: 14,
                                                        ),
                                                      ),
                                                      const SizedBox(height: 4),
                                                      Text(
                                                        _formatDate(payment.paymentDate),
                                                        style: TextStyle(
                                                          fontSize: 12,
                                                          color: Colors.grey[600],
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                                Text(
                                                  CurrencyFormatter.formatCurrency(payment.amount),
                                                  style: const TextStyle(
                                                    fontWeight: FontWeight.bold,
                                                    color: Colors.blue,
                                                    fontSize: 14,
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                        ),
                                      );
                                    },
                                  ),
                            
                            // Expenses Tab
                            expensesList.isEmpty
                                ? _buildEmptyState('No expenses recorded for this month')
                                : ListView.builder(
                                    itemCount: expensesList.length,
                                    padding: const EdgeInsets.all(12),
                                    itemBuilder: (context, index) {
                                      final expense = expensesList[index];
                                      return Card(
                                        margin: const EdgeInsets.only(bottom: 8),
                                        child: InkWell(
                                          onTap: () => _openExpenseDetails(context, expense),
                                          borderRadius: BorderRadius.circular(8),
                                          child: Padding(
                                            padding: const EdgeInsets.all(12),
                                            child: Row(
                                              children: [
                                                CircleAvatar(
                                                  radius: 18,
                                                  backgroundColor: Colors.orange.withAlpha(30),
                                                  child: const Icon(
                                                    Icons.receipt_long,
                                                    color: Colors.orange,
                                                    size: 18,
                                                  ),
                                                ),
                                                const SizedBox(width: 12),
                                                Expanded(
                                                  child: Column(
                                                    crossAxisAlignment: CrossAxisAlignment.start,
                                                    children: [
                                                      Text(
                                                        expense.receiptNumber ?? 
                                                            expense.formattedExpenseNumber,
                                                        style: const TextStyle(
                                                          fontWeight: FontWeight.bold,
                                                          fontSize: 14,
                                                        ),
                                                      ),
                                                      const SizedBox(height: 4),
                                                      Text(
                                                        _formatDate(expense.date),
                                                        style: TextStyle(
                                                          fontSize: 12,
                                                          color: Colors.grey[600],
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                                Text(
                                                  CurrencyFormatter.formatCurrency(expense.amount),
                                                  style: const TextStyle(
                                                    fontWeight: FontWeight.bold,
                                                    color: Colors.orange,
                                                    fontSize: 14,
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                        ),
                                      );
                                    },
                                  ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              
              // Action buttons
              Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    TextButton(
                      onPressed: () => Navigator.of(context).pop(),
                      child: const Text('Close'),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
  
  // Helper for date formatting
  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
  
  // Helper for empty state
  Widget _buildEmptyState(String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.info_outline,
            size: 48,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            message,
            style: TextStyle(color: Colors.grey[600]),
          ),
        ],
      ),
    );
  }
  
  // Helper for summary cards in dialog
  Widget _buildSummaryCard(String title, double amount, Color color, IconData icon) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: color.withAlpha(30),
                shape: BoxShape.circle,
              ),
              child: Icon(icon, color: color),
            ),
            const SizedBox(height: 8),
            Text(
              title,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 4),
            FittedBox(
              fit: BoxFit.scaleDown,
              child: Text(
                CurrencyFormatter.formatCurrency(amount),
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Improve the monthly view to include the View button
  Widget _buildMonthlyView() {
    if (_monthlyProfits.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.calendar_today,
              size: 48,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            const Text('No monthly data available for the selected period'),
          ],
        ),
      );
    }
    
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.primary.withAlpha((0.1 * 255).round()),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.calendar_month,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
                const SizedBox(width: 12),
                const Expanded(
                  child: Text(
                    'Monthly Profit Breakdown',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            const Divider(),
            const SizedBox(height: 8),
            
            // Update the header to remove Income and Expenses columns
            Container(
              padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 8),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surfaceContainerHighest,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Expanded(
                    flex: 4,
                    child: Text(
                      'Month',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).colorScheme.onSurface,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  Expanded(
                    flex: 3,
                    child: Text(
                      'Profit',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).colorScheme.onSurface,
                      ),
                      textAlign: TextAlign.end,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  SizedBox(width: 80), // Wider space for the enhanced View button
                ],
              ),
            ),
            
            const SizedBox(height: 8),
            
            // Enhanced list with alternating background colors
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: 12,
              itemBuilder: (context, index) {
                final month = index + 1;
                final monthData = _monthlyProfits[month] ?? {
                  'income': 0.0,
                  'expenses': 0.0,
                  'profit': 0.0,
                };
                
                final income = monthData['income'] ?? 0.0;
                final expenses = monthData['expenses'] ?? 0.0;
                final profit = monthData['profit'] ?? 0.0;
                final isPositive = profit >= 0;
                final hasActivity = income > 0 || expenses > 0;
                
                // Highlight current month
                final isCurrentMonth = DateTime.now().month == month;
                final backgroundColor = isCurrentMonth 
                    ? Theme.of(context).colorScheme.primary.withAlpha(15)
                    : (index % 2 == 0 ? Colors.transparent : Theme.of(context).colorScheme.surfaceContainerHighest.withAlpha(50));
                
                return Container(
                  decoration: BoxDecoration(
                    color: backgroundColor,
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 8),
                    child: Row(
                      children: [
                        Expanded(
                          flex: 4,
                          child: Row(
                            children: [
                              Container(
                                width: 22,
                                height: 22,
                                decoration: BoxDecoration(
                                  color: hasActivity 
                                      ? Theme.of(context).colorScheme.primary.withAlpha(40)
                                      : Colors.grey.withAlpha(40),
                                  shape: BoxShape.circle,
                                ),
                                child: Center(
                                  child: Text(
                                    month.toString(),
                                    style: TextStyle(
                                      fontSize: 10,
                                      fontWeight: FontWeight.bold,
                                      color: hasActivity 
                                          ? Theme.of(context).colorScheme.primary
                                          : Colors.grey,
                                    ),
                                  ),
                                ),
                              ),
                              const SizedBox(width: 6),
                              Expanded(
                                child: Text(
                                  _getMonthName(month),
                                  style: TextStyle(
                                    fontSize: 13,
                                    fontWeight: isCurrentMonth ? FontWeight.bold : FontWeight.normal,
                                  ),
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                              if (isCurrentMonth)
                                Container(
                                  margin: const EdgeInsets.only(left: 2),
                                  padding: const EdgeInsets.symmetric(horizontal: 3, vertical: 1),
                                  decoration: BoxDecoration(
                                    color: Theme.of(context).colorScheme.primary,
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  child: const Text(
                                    'Now',
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontSize: 8,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                            ],
                          ),
                        ),
                        Expanded(
                          flex: 3,
                          child: Container(
                            padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                            decoration: profit != 0 ? BoxDecoration(
                              color: isPositive ? Colors.green.withAlpha(30) : Colors.red.withAlpha(30),
                              borderRadius: BorderRadius.circular(4),
                            ) : null,
                            child: Text(
                              CurrencyFormatter.formatCurrency(profit),
                              style: TextStyle(
                                fontSize: 13,
                                fontWeight: FontWeight.bold,
                                color: profit == 0 ? Colors.grey : (isPositive ? Colors.green : Colors.red),
                              ),
                              textAlign: TextAlign.end,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ),
                        SizedBox(
                          width: 80,
                          child: hasActivity ? ElevatedButton.icon(
                            onPressed: () => _showMonthDetailDialog(context, month, monthData),
                            icon: const Icon(Icons.visibility, size: 16),
                            label: const Text('View'),
                            style: ElevatedButton.styleFrom(
                              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
                              minimumSize: const Size(80, 32),
                              backgroundColor: Theme.of(context).colorScheme.primaryContainer,
                              foregroundColor: Theme.of(context).colorScheme.onPrimaryContainer,
                              elevation: 0,
                            ),
                          ) : TextButton(
                            onPressed: null,
                            style: TextButton.styleFrom(
                              padding: EdgeInsets.zero,
                              minimumSize: const Size(80, 32),
                              tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                              backgroundColor: Colors.grey.withAlpha(30),
                            ),
                            child: const Text(
                              'No Data',
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.grey,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
            
            // Summary section at bottom - update to match new column layout
            const SizedBox(height: 16),
            const Divider(),
            Padding(
              padding: const EdgeInsets.only(top: 12),
              child: Row(
                children: [
                  Expanded(
                    flex: 4,
                    child: Text(
                      'Year Total',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  Expanded(
                    flex: 3,
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                      decoration: BoxDecoration(
                        color: _totalProfit >= 0 ? Colors.green.withAlpha(30) : Colors.red.withAlpha(30),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        CurrencyFormatter.formatCurrency(_totalProfit),
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 13,
                          color: _totalProfit >= 0 ? Colors.green : Colors.red,
                        ),
                        textAlign: TextAlign.end,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ),
                  const SizedBox(width: 80), // Space for alignment with View buttons
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Add methods to handle opening payment and expense details
  // First, add a method to determine payment reason based on bill types
  Future<String> _getPaymentReason(Payment payment) async {
    if (payment.billIds.isEmpty) {
      return "General Payment";
    }
    
    try {
      final List<String> billTypes = [];
      
      for (final billId in payment.billIds) {
        final bill = await serviceLocator.billService.getBill(billId);
        if (!billTypes.contains(bill.type.name)) {
          billTypes.add(_capitalizeFirst(bill.type.name));
        }
      }
      
      if (billTypes.isEmpty) {
        return "General Payment";
      } else if (billTypes.length == 1) {
        return billTypes.first;
      } else {
        return "Multiple: ${billTypes.join(', ')}";
      }
    } catch (e) {
      return "General Payment";
    }
  }

  // Then update the _openPaymentDetails method to include payment reason
  void _openPaymentDetails(BuildContext context, Payment payment) async {
    // Get the payment reason
    final paymentReason = await _getPaymentReason(payment);
    
    // Navigate to payment details page or show a dialog with full details
    if (!context.mounted) return;
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(
              payment.status == PaymentStatus.verified 
                  ? Icons.check_circle
                  : payment.status == PaymentStatus.rejected
                      ? Icons.cancel
                      : Icons.pending,
              color: payment.status == PaymentStatus.verified
                  ? Colors.green
                  : payment.status == PaymentStatus.rejected
                      ? Colors.red
                      : Colors.orange,
            ),
            const SizedBox(width: 8),
            Text('Payment Details'),
          ],
        ),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              // Add Ticket Number field using receipt reference or formatted ID
              _buildDetailRow('Ticket Number', 
                payment.receiptReference ?? 'TKT-${payment.id.substring(0, math.min(8, payment.id.length))}'),
              _buildDetailRow('Amount', CurrencyFormatter.formatCurrency(payment.amount)),
              _buildDetailRow('Payment For', paymentReason),
              _buildDetailRow('Date', _formatDate(payment.paymentDate)),
              _buildDetailRow('Status', _capitalizeFirst(payment.status.name)),
              _buildDetailRow('Method', _capitalizeFirst(payment.method.name)),
              if (payment.notes != null && payment.notes!.isNotEmpty)
                _buildDetailRow('Notes', payment.notes!),
              if (payment.verifiedBy != null)
                _buildDetailRow('Verified By', 'Admin User'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
          if (payment.status == PaymentStatus.pending)
            ElevatedButton(
              onPressed: () {
                // Here you would implement the logic to navigate to payment verification screen
                Navigator.of(context).pop();
                // Example: Navigator.pushNamed(context, '/payments/verify/${payment.id}');
              },
              child: const Text('Verify Payment'),
            ),
        ],
      ),
    );
  }

  void _openExpenseDetails(BuildContext context, ExpenseModel expense) {
    // Navigate to expense details page or show a dialog with full details
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(4),
              decoration: BoxDecoration(
                color: Colors.orange.withAlpha(30),
                shape: BoxShape.circle,
              ),
              child: const Icon(Icons.receipt_long, color: Colors.orange, size: 20),
            ),
            const SizedBox(width: 8),
            const Text('Expense Details'),
          ],
        ),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildDetailRow('Title', expense.title),
              _buildDetailRow('Amount', CurrencyFormatter.formatCurrency(expense.amount)),
              _buildDetailRow('Date', _formatDate(expense.date)),
              if (expense.receiptNumber != null)
                _buildDetailRow('Receipt Number', expense.receiptNumber!),
              if (expense.categoryName != null)
                _buildDetailRow('Category', expense.categoryName!),
              if (expense.propertyName != null)
                _buildDetailRow('Property', expense.propertyName!),
              if (expense.vendorName != null)
                _buildDetailRow('Vendor', expense.vendorName!),
              _buildDetailRow('Description', expense.description),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
          ElevatedButton(
            onPressed: () {
              // Here you would implement the logic to navigate to expense edit screen
              Navigator.of(context).pop();
              // Example: Navigator.pushNamed(context, '/expenses/edit', arguments: expense.id);
            },
            child: const Text('View Full Details'),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: const TextStyle(
              fontSize: 12,
              color: Colors.grey,
            ),
          ),
          const SizedBox(height: 2),
          Text(
            value,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  // Add helper method to capitalize first letter of a string
  String _capitalizeFirst(String text) {
    if (text.isEmpty) return text;
    return text[0].toUpperCase() + text.substring(1);
  }
} 