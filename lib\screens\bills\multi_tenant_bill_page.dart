﻿import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../models/bill/bill.dart';
import '../../models/tenant/tenant.dart';
import '../../models/property/property_model.dart';
import '../../services/service_locator.dart';
import '../../utils/form_validators.dart';
import '../../utils/logger.dart';
import '../../widgets/loading_indicator.dart';
import '../../widgets/common/section_header.dart';
import '../../widgets/error_dialog.dart';
import 'dart:math';

// Helper function to show error dialog
void showErrorDialog(BuildContext context, String message) {
  ErrorDialog.show(
    context,
    title: 'Error',
    message: message,
  );
}

class MultiTenantBillPage extends StatefulWidget {
  final Bill? existingBill;
  
  const MultiTenantBillPage({
    super.key,
    this.existingBill,
  });

  @override
  State<MultiTenantBillPage> createState() => _MultiTenantBillPageState();
}

class _MultiTenantBillPageState extends State<MultiTenantBillPage> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _amountController = TextEditingController();
  final _notesController = TextEditingController();
  final _billNumberController = TextEditingController();
  
  DateTime _dueDate = DateTime.now().add(const Duration(days: 30));
  BillType _type = BillType.utility;
  RecurrenceType _recurrence = RecurrenceType.monthly;
  bool _isLoading = false;
  bool _isSplitEvenly = true;
  
  // For tenant selection
  List<Tenant> _availableTenants = [];
  List<Tenant> _selectedTenants = [];
  
  // For tenant filtering and search
  String _searchQuery = '';
  String _filterProperty = 'all';
  String _sortBy = 'name';
  bool _sortAscending = true;
  
  // For property filtering
  List<Property> _properties = [];
  Map<String, Property> _roomToPropertyMap = {};
  Map<String, String> _tenantPropertyNames = {};
  
  // For bill details
  Bill? _createdBill;

  @override
  void initState() {
    super.initState();
    
    // Initialize amount to 0.00 for clean display
    if (_amountController.text.isEmpty) {
      _amountController.text = '0.00';
    }
    
    _generateBillNumber();
    _loadTenants();
    _loadProperties();
  }

  // Generate a unique bill number with "BILL_G-" prefix for group bills
  void _generateBillNumber() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final random = Random().nextInt(1000).toString().padLeft(3, '0');
    _billNumberController.text = 'BILL_G-$timestamp-$random';
  }

  // Load all active tenants
  Future<void> _loadTenants() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final tenantService = serviceLocator.tenantService;
      final tenants = await tenantService.getAllTenants();
      
      // Only include active tenants with assigned rooms
      final activeTenants = tenants
          .where((tenant) => 
              tenant.status == TenantStatus.active && 
              tenant.roomId != null)
          .toList();
      
      setState(() {
        _availableTenants = activeTenants;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ErrorDialog.show(
          context,
          title: 'Error',
          message: 'Failed to load tenants: ${e.toString()}'
        );
      }
    }
  }
  
  // Load all properties and create a mapping between rooms and properties
  Future<void> _loadProperties() async {
    try {
      final propertyService = serviceLocator.propertyService;
      final roomService = serviceLocator.roomService;
      
      // Load all properties
      final properties = await propertyService.getAllProperties();
      
      // Create a map of roomId to property
      final roomToPropertyMap = <String, Property>{};
      final tenantPropertyNames = <String, String>{};
      
      // For each property, load its rooms and map them
      for (final property in properties) {
        final rooms = await roomService.getRoomsByPropertyId(property.id);
        for (final room in rooms) {
          roomToPropertyMap[room.id] = property;
        }
      }
      
      // Map tenants to property names
      for (final tenant in _availableTenants) {
        if (tenant.roomId != null && roomToPropertyMap.containsKey(tenant.roomId)) {
          final property = roomToPropertyMap[tenant.roomId]!;
          tenantPropertyNames[tenant.id] = property.name;
        }
      }
      
      setState(() {
        _properties = properties;
        _roomToPropertyMap = roomToPropertyMap;
        _tenantPropertyNames = tenantPropertyNames;
      });
    } catch (e) {
      if (mounted) {
        showErrorDialog(context, 'Failed to load properties: ${e.toString()}');
      }
    }
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _amountController.dispose();
    _notesController.dispose();
    _billNumberController.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.existingBill != null ? 'Edit Group Bill' : 'Create Group Bill'),
      ),
      body: _isLoading
          ? const Center(child: LoadingIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildBillDetailsSection(),
                    const SizedBox(height: 24),
                    _buildTenantSelectionSection(),
                    const SizedBox(height: 24),
                    _buildSplitOptionsSection(),
                    const SizedBox(height: 32),
                    _buildSubmitButton(),
                  ],
                ),
              ),
            ),
    );
  }
  
  Widget _buildBillDetailsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SectionHeader(title: 'Bill Details'),
        const SizedBox(height: 16),
        
        // Bill Number (Read-only)
        TextFormField(
          controller: _billNumberController,
          decoration: const InputDecoration(
            labelText: 'Bill Number',
            border: OutlineInputBorder(),
          ),
          readOnly: true,
          enabled: false,
        ),
        const SizedBox(height: 16),
        
        // Title
        TextFormField(
          controller: _titleController,
          decoration: const InputDecoration(
            labelText: 'Title*',
            border: OutlineInputBorder(),
          ),
          validator: FormValidators.required,
        ),
        const SizedBox(height: 16),
        
        // Description
        TextFormField(
          controller: _descriptionController,
          decoration: const InputDecoration(
            labelText: 'Description*',
            border: OutlineInputBorder(),
          ),
          validator: FormValidators.required,
          maxLines: 2,
        ),
        const SizedBox(height: 16),
        
        // Amount
        TextFormField(
          controller: _amountController,
          decoration: const InputDecoration(
            labelText: 'Amount*',
            border: OutlineInputBorder(),
            prefixText: '\$ ',
          ),
          keyboardType: TextInputType.number,
          inputFormatters: [
            FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}')),
          ],
          validator: FormValidators.required,
          onChanged: (value) {
            // Update view when amount changes
            setState(() {});
          },
        ),
        const SizedBox(height: 16),
        
        // Due Date
        InkWell(
          onTap: () => _selectDueDate(context),
          child: InputDecorator(
            decoration: const InputDecoration(
              labelText: 'Due Date*',
              border: OutlineInputBorder(),
            ),
            child: Text(
              '${_dueDate.day}/${_dueDate.month}/${_dueDate.year}',
            ),
          ),
        ),
        const SizedBox(height: 16),
        
        // Bill Type
        DropdownButtonFormField<BillType>(
          value: _type,
          decoration: const InputDecoration(
            labelText: 'Bill Type*',
            border: OutlineInputBorder(),
          ),
          items: [
            DropdownMenuItem(value: BillType.utility, child: Text('Utility')),
            DropdownMenuItem(value: BillType.maintenance, child: Text('Maintenance')),
            DropdownMenuItem(value: BillType.service, child: Text('Service')),
            DropdownMenuItem(value: BillType.other, child: Text('Other')),
          ],
          onChanged: (value) {
            if (value != null) {
              setState(() {
                _type = value;
              });
            }
          },
        ),
        const SizedBox(height: 16),
        
        // Recurrence Type
        DropdownButtonFormField<RecurrenceType>(
          value: _recurrence,
          decoration: const InputDecoration(
            labelText: 'Recurrence*',
            border: OutlineInputBorder(),
          ),
          items: [
            DropdownMenuItem(value: RecurrenceType.oneTime, child: Text('One-time')),
            DropdownMenuItem(value: RecurrenceType.monthly, child: Text('Monthly')),
            DropdownMenuItem(value: RecurrenceType.quarterly, child: Text('Quarterly')),
            DropdownMenuItem(value: RecurrenceType.yearly, child: Text('Yearly')),
          ],
          onChanged: (value) {
            if (value != null) {
              setState(() {
                _recurrence = value;
              });
            }
          },
        ),
        const SizedBox(height: 16),
        
        // Notes
        TextFormField(
          controller: _notesController,
          decoration: const InputDecoration(
            labelText: 'Notes (Optional)',
            border: OutlineInputBorder(),
          ),
          maxLines: 3,
        ),
      ],
    );
  }
  
  Widget _buildTenantSelectionSection() {
    // Filter and sort tenants based on current criteria
    List<Tenant> filteredTenants = _filterAndSortTenants();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SectionHeader(title: 'Select Tenants'),
        const SizedBox(height: 8),
        
        // Search and filter bar
        Card(
          elevation: 2,
          margin: const EdgeInsets.only(bottom: 16),
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Search field
                TextField(
                  decoration: InputDecoration(
                    hintText: 'Search tenants...',
                    prefixIcon: const Icon(Icons.search),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    contentPadding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
                  ),
                  onChanged: (value) {
                    setState(() {
                      _searchQuery = value.toLowerCase();
                    });
                  },
                ),
                const SizedBox(height: 16),
                
                Row(
                  children: [
                    // Filter dropdown
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: const InputDecoration(
                          labelText: 'Filter by Property',
                          border: OutlineInputBorder(),
                          contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 12),
                        ),
                        value: _filterProperty,
                        items: [
                          const DropdownMenuItem(value: 'all', child: Text('All Properties')),
                          ..._properties.map((property) => 
                            DropdownMenuItem(value: property.id, child: Text(property.name))
                          ),
                        ],
                        onChanged: (value) {
                          if (value != null) {
                            setState(() {
                              _filterProperty = value;
                            });
                          }
                        },
                      ),
                    ),
                    const SizedBox(width: 12),
                    
                    // Sort dropdown
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: const InputDecoration(
                          labelText: 'Sort by',
                          border: OutlineInputBorder(),
                          contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 12),
                        ),
                        value: _sortBy,
                        items: const [
                          DropdownMenuItem(value: 'name', child: Text('Name')),
                          DropdownMenuItem(value: 'email', child: Text('Email')),
                        ],
                        onChanged: (value) {
                          if (value != null) {
                            setState(() {
                              if (_sortBy == value) {
                                _sortAscending = !_sortAscending;
                              } else {
                                _sortBy = value;
                                _sortAscending = true;
                              }
                            });
                          }
                        },
                      ),
                    ),
                  ],
                ),
                
                const SizedBox(height: 8),
                
                // Sort direction toggle
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    Text('Sort direction: ${_sortAscending ? 'Ascending' : 'Descending'}'),
                    const SizedBox(width: 8),
                    IconButton(
                      icon: Icon(_sortAscending ? Icons.arrow_upward : Icons.arrow_downward),
                      onPressed: () {
                        setState(() {
                          _sortAscending = !_sortAscending;
                        });
                      },
                      tooltip: 'Toggle sort direction',
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
        
        // Selection actions bar
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Selected: ${_selectedTenants.length} ${_selectedTenants.length == 1 ? 'tenant' : 'tenants'}',
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            Row(
              children: [
                TextButton.icon(
                  onPressed: _availableTenants.isNotEmpty ? () {
                    setState(() {
                      _selectedTenants = List.from(_availableTenants);
                    });
                  } : null,
                  icon: const Icon(Icons.select_all),
                  label: const Text('Select All'),
                ),
                TextButton.icon(
                  onPressed: _selectedTenants.isNotEmpty ? () {
                    setState(() {
                      _selectedTenants.clear();
                    });
                  } : null,
                  icon: const Icon(Icons.deselect),
                  label: const Text('Clear'),
                ),
              ],
            ),
          ],
        ),
        const SizedBox(height: 8),
        
        // Divider
        const Divider(),
        const SizedBox(height: 8),
        
        // Available Tenants List
        if (_availableTenants.isEmpty)
          const Center(
            child: Padding(
              padding: EdgeInsets.all(16.0),
              child: Text('No active tenants found'),
            ),
          )
        else if (filteredTenants.isEmpty)
          Center(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Text(_searchQuery.isNotEmpty 
                ? 'No tenants match your search for "$_searchQuery"'
                : _filterProperty != 'all'
                  ? 'No tenants found in the selected property'
                  : 'No tenants match your criteria'
              ),
            ),
          )
        else
          Container(
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300),
              borderRadius: BorderRadius.circular(8),
            ),
            height: 350, // Fixed height for scrollable container
            child: ListView.separated(
              shrinkWrap: true,
              itemCount: filteredTenants.length,
              separatorBuilder: (context, index) => const Divider(height: 1),
              itemBuilder: (context, index) {
                final tenant = filteredTenants[index];
                final isSelected = _selectedTenants.any((t) => t.id == tenant.id);
                
                return CheckboxListTile(
                  title: Text(
                    '${tenant.firstName} ${tenant.lastName}',
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                  subtitle: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(tenant.email),
                      if (tenant.phoneNumber != null && tenant.phoneNumber!.isNotEmpty)
                        Text('Phone: ${tenant.phoneNumber}'),
                      if (_tenantPropertyNames.containsKey(tenant.id))
                        Text(
                          'Property: ${_tenantPropertyNames[tenant.id]}',
                          style: TextStyle(
                            fontWeight: FontWeight.w500,
                            color: Theme.of(context).colorScheme.secondary,
                          ),
                        ),
                    ],
                  ),
                  secondary: CircleAvatar(
                    backgroundColor: Theme.of(context).colorScheme.primary,
                    child: Text(
                      '${tenant.firstName[0]}${tenant.lastName[0]}',
                      style: const TextStyle(color: Colors.white),
                    ),
                  ),
                  value: isSelected,
                  onChanged: (selected) {
                    setState(() {
                      if (selected == true) {
                        if (!_selectedTenants.any((t) => t.id == tenant.id)) {
                          _selectedTenants.add(tenant);
                        }
                      } else {
                        _selectedTenants.removeWhere((t) => t.id == tenant.id);
                      }
                    });
                  },
                );
              },
            ),
          ),
          
        const SizedBox(height: 16),
        
        // Selected tenants chips
        if (_selectedTenants.isNotEmpty)
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Selected Tenants:',
                style: Theme.of(context).textTheme.titleSmall,
              ),
              const SizedBox(height: 8),
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: _selectedTenants.map((tenant) {
                  return Chip(
                    avatar: CircleAvatar(
                      backgroundColor: Theme.of(context).colorScheme.secondary,
                      child: Text(
                        '${tenant.firstName[0]}${tenant.lastName[0]}',
                        style: const TextStyle(color: Colors.white, fontSize: 12),
                      ),
                    ),
                    label: Text('${tenant.firstName} ${tenant.lastName}'),
                    deleteIcon: const Icon(Icons.cancel, size: 18),
                    onDeleted: () {
                      setState(() {
                        _selectedTenants.removeWhere((t) => t.id == tenant.id);
                      });
                    },
                  );
                }).toList(),
              ),
            ],
          ),
      ],
    );
  }
  
  // Helper method to filter and sort tenants
  List<Tenant> _filterAndSortTenants() {
    // First apply filters
    List<Tenant> filtered = _availableTenants.where((tenant) {
      // Apply search query filter
      if (_searchQuery.isNotEmpty) {
        final fullName = '${tenant.firstName} ${tenant.lastName}'.toLowerCase();
        final email = tenant.email.toLowerCase();
        final phone = tenant.phoneNumber?.toLowerCase() ?? '';
        
        if (!fullName.contains(_searchQuery) && 
            !email.contains(_searchQuery) && 
            !phone.contains(_searchQuery)) {
          return false;
        }
      }
      
      // Apply property filter if not "all"
      if (_filterProperty != 'all') {
        // Check if tenant has a room and if that room belongs to the selected property
        if (tenant.roomId == null) return false;
        
        final property = _roomToPropertyMap[tenant.roomId];
        return property != null && property.id == _filterProperty;
      }
      
      return true;
    }).toList();
    
    // Then apply sorting
    filtered.sort((a, b) {
      int result;
      switch (_sortBy) {
        case 'name':
          result = '${a.firstName} ${a.lastName}'.compareTo('${b.firstName} ${b.lastName}');
          break;
        case 'email':
          result = a.email.compareTo(b.email);
          break;
        default:
          result = '${a.firstName} ${a.lastName}'.compareTo('${b.firstName} ${b.lastName}');
      }
      
      return _sortAscending ? result : -result;
    });
    
    return filtered;
  }
  
  Widget _buildSplitOptionsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SectionHeader(title: 'Bill Split Options'),
        const SizedBox(height: 16),
        
        // Split evenly switch
        SwitchListTile(
          title: const Text('Split amount evenly among tenants'),
          value: _isSplitEvenly,
          onChanged: (value) {
            setState(() {
              _isSplitEvenly = value;
            });
          },
        ),
        
        // Split amount preview
        if (_isSplitEvenly && _selectedTenants.isNotEmpty && _amountController.text.isNotEmpty)
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Text(
              'Each tenant will be billed: \$${_calculateSplitAmount().toStringAsFixed(2)}',
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
      ],
    );
  }
  
  Widget _buildSubmitButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: _selectedTenants.isEmpty ? null : _saveBill,
        style: ElevatedButton.styleFrom(
          padding: const EdgeInsets.symmetric(vertical: 16),
        ),
        child: Text(widget.existingBill != null ? 'Update Bill' : 'Create Bill'),
      ),
    );
  }
  
  Future<void> _selectDueDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _dueDate,
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365 * 10)),
    );
    
    if (picked != null && picked != _dueDate) {
      setState(() {
        _dueDate = picked;
      });
    }
  }
  
  double _calculateSplitAmount() {
    if (_selectedTenants.isEmpty || _amountController.text.isEmpty) {
      return 0.0;
    }
    
    final totalAmount = double.tryParse(_amountController.text) ?? 0.0;
    return totalAmount / _selectedTenants.length;
  }
  
  Future<void> _saveBill() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }
    
    if (_selectedTenants.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please select at least one tenant')),
      );
      return;
    }
    
    setState(() {
      _isLoading = true;
    });
    
    try {
      // Parse bill data
      final title = _titleController.text.trim();
      final description = _descriptionController.text.trim();
      final amount = double.parse(_amountController.text);
      final notes = _notesController.text.trim();
      
      // Create or update the bill
      if (widget.existingBill != null) {
        // Update existing bill
        final updatedBill = widget.existingBill!.copyWith(
          title: title,
          description: description,
          amount: amount,
          dueDate: _dueDate,
          type: _type,
          recurrence: _recurrence,
          notes: notes,
          updatedAt: DateTime.now(),
        );
        
        _createdBill = await serviceLocator.billService.updateBill(updatedBill);
        
        // Delete all existing tenant relations and create new ones
        await serviceLocator.billTenantService.deleteRelationsForBill(_createdBill!.id);
      } else {
        // Create new bill
        final newBill = Bill(
          title: title,
          description: description,
          amount: amount,
          dueDate: _dueDate,
          type: _type,
          recurrence: _recurrence,
          notes: notes.isNotEmpty ? notes : null,
          billNumber: _billNumberController.text,
        );
        
        _createdBill = await serviceLocator.billService.createBill(newBill);
      }
      
      // Create tenant relations
      final tenantIds = _selectedTenants.map((t) => t.id).toList();
      
      await serviceLocator.billTenantService.createMultipleRelations(
        _createdBill!.id,
        tenantIds,
        splitEvenly: _isSplitEvenly,
        billAmount: amount,
      );
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(widget.existingBill != null 
              ? 'Bill updated successfully' 
              : 'Bill created and assigned to ${_selectedTenants.length} tenants')),
        );
        
        // Close the page
        Navigator.pop(context, _createdBill);
      }
    } catch (e) {
      AppLogger.error('Error saving bill: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
