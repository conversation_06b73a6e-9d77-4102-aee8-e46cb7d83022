class Property {
  final String id;
  final String name;
  final int totalUnits;
  final int vacantUnits;
  final int occupiedUnits;
  final double occupancyRate;
  final int currentMonth;
  final int currentYear;

  Property({
    required this.id,
    required this.name,
    required this.totalUnits,
    required this.vacantUnits,
    required this.occupiedUnits,
    required this.occupancyRate,
    required this.currentMonth,
    required this.currentYear,
  });

  factory Property.fromJson(Map<String, dynamic> json) {
    return Property(
      id: json['property_id'] as String,
      name: json['property_name'] as String,
      totalUnits: json['total_units'] as int,
      vacantUnits: json['vacant_units'] as int,
      occupiedUnits: json['occupied_units'] as int,
      occupancyRate: (json['occupancy_rate'] as num).toDouble(),
      currentMonth: json['current_month'] as int,
      currentYear: json['current_year'] as int,
    );
  }

  Map<String, dynamic> toJson() => {
    'property_id': id,
    'property_name': name,
    'total_units': totalUnits,
    'vacant_units': vacantUnits,
    'occupied_units': occupiedUnits,
    'occupancy_rate': occupancyRate,
    'current_month': currentMonth,
    'current_year': currentYear,
  };
} 