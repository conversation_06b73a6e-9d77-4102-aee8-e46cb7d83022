import 'package:flutter/material.dart';

/// A custom error dialog that provides a more detailed and user-friendly
/// presentation of errors in the application.
class ErrorDialog extends StatelessWidget {
  /// The main error message to display
  final String title;
  
  /// A more detailed explanation of the error
  final String message;
  
  /// The technical error details (usually the exception message)
  final String? details;
  
  /// Callback when the retry button is pressed
  final VoidCallback? onRetry;
  
  /// Custom icon to show in the dialog
  final IconData icon;
  
  /// Color of the icon and title
  final Color color;

  const ErrorDialog({
    super.key,
    required this.title,
    required this.message,
    this.details,
    this.onRetry,
    this.icon = Icons.error_outline,
    this.color = Colors.red,
  });

  /// Factory constructor for tenant not found error
  factory ErrorDialog.tenantNotFound({
    required String tenantId,
    VoidCallback? onDismiss,
  }) {
    return ErrorDialog(
      title: 'Tenant Not Found',
      message: 'The tenant record could not be found in the database. It may have been deleted or modified.',
      details: 'Tenant ID: $tenantId',
      icon: Icons.person_off,
      onRetry: onDismiss,
    );
  }

  /// Factory constructor for room not found error
  factory ErrorDialog.roomNotFound({
    required String roomId,
    VoidCallback? onRetry,
  }) {
    return ErrorDialog(
      title: 'Room Not Found',
      message: 'The room record could not be found in the database. It may have been deleted or modified.',
      details: 'Room ID: $roomId',
      icon: Icons.meeting_room_outlined,
      onRetry: onRetry,
    );
  }

  /// Factory constructor for general database errors
  factory ErrorDialog.databaseError({
    required String message,
    String? details,
    VoidCallback? onRetry,
  }) {
    return ErrorDialog(
      title: 'Database Error',
      message: message,
      details: details,
      icon: Icons.storage,
      onRetry: onRetry,
    );
  }

  /// Factory constructor for network errors
  factory ErrorDialog.networkError({
    String? details,
    VoidCallback? onRetry,
  }) {
    return ErrorDialog(
      title: 'Network Error',
      message: 'Unable to connect to the server. Please check your internet connection and try again.',
      details: details,
      icon: Icons.signal_wifi_off,
      onRetry: onRetry,
    );
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Row(
        children: [
          Icon(icon, color: color),
          const SizedBox(width: 8),
          Text(title),
        ],
      ),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(message),
            if (details != null) ...[
              const SizedBox(height: 16),
              const Text(
                'Technical Details:',
                style: TextStyle(fontWeight: FontWeight.bold, fontSize: 12),
              ),
              const SizedBox(height: 4),
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.grey[200],
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  details!,
                  style: const TextStyle(fontFamily: 'monospace', fontSize: 12),
                ),
              ),
            ],
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('DISMISS'),
        ),
        if (onRetry != null)
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              onRetry!();
            },
            child: Text(onRetry != null && title.contains('Not Found') ? 'GO BACK' : 'RETRY'),
          ),
      ],
    );
  }

  /// Show this error dialog
  static Future<void> show(
    BuildContext context, {
    required String title,
    required String message,
    String? details,
    VoidCallback? onRetry,
    IconData icon = Icons.error_outline,
    Color color = Colors.red,
  }) {
    return showDialog(
      context: context,
      builder: (context) => ErrorDialog(
        title: title,
        message: message,
        details: details,
        onRetry: onRetry,
        icon: icon,
        color: color,
      ),
    );
  }

  /// Show a tenant not found error dialog
  static Future<void> showTenantNotFound(
    BuildContext context, {
    required String tenantId,
    VoidCallback? onDismiss,
  }) {
    return showDialog(
      context: context,
      builder: (context) => ErrorDialog.tenantNotFound(
        tenantId: tenantId,
        onDismiss: onDismiss,
      ),
    );
  }

  /// Show a room not found error dialog
  static Future<void> showRoomNotFound(
    BuildContext context, {
    required String roomId,
    VoidCallback? onRetry,
  }) {
    return showDialog(
      context: context,
      builder: (context) => ErrorDialog.roomNotFound(
        roomId: roomId,
        onRetry: onRetry,
      ),
    );
  }
} 