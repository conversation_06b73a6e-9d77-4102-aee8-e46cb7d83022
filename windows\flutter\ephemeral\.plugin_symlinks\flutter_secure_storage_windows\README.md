# flutter_secure_storage_windows

The windows implementation of [`flutter_secure_storage`][1].

## Usage

This package is [endorsed][2], which means you can simply use `flutter_secure_storage`
normally. This package will be automatically included in your app when you do.

[1]: https://pub.dev/packages/flutter_secure_storage
[2]: https://flutter.dev/docs/development/packages-and-plugins/developing-packages#endorsed-federated-plugin

