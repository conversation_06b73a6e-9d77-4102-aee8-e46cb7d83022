import 'package:supabase_flutter/supabase_flutter.dart';
import '../../models/bill/bill.dart';
import '../../models/bill/bill_template.dart';
import '../../models/property/property_model.dart';
import '../../models/tenant/tenant.dart';
import '../../utils/logger.dart';
import '../service_locator.dart';

class BillTemplateService {
  final SupabaseClient _client = Supabase.instance.client;
  final String _tableName = 'bill_templates';

  // Create a new bill template
  Future<BillTemplate> createTemplate(BillTemplate template) async {
    try {
      final response = await _client
          .from(_tableName)
          .insert(template.toJson())
          .select()
          .single();

      return BillTemplate.fromJson(response);
    } catch (e) {
      AppLogger.error('Failed to create bill template: $e');
      throw Exception('Failed to create bill template: $e');
    }
  }

  // Get a bill template by ID
  Future<BillTemplate> getTemplate(String id) async {
    try {
      final response =
          await _client.from(_tableName).select().eq('id', id).single();

      return BillTemplate.fromJson(response);
    } catch (e) {
      AppLogger.error('Failed to get bill template: $e');
      throw Exception('Failed to get bill template: $e');
    }
  }

  // Get all bill templates
  Future<List<BillTemplate>> getAllTemplates() async {
    try {
      final response = await _client
          .from(_tableName)
          .select()
          .order('name', ascending: true);

      return response.map<BillTemplate>((data) => BillTemplate.fromJson(data)).toList();
    } catch (e) {
      AppLogger.error('Failed to get bill templates: $e');
      throw Exception('Failed to get bill templates: $e');
    }
  }

  // Get count of active bill templates
  Future<int> getActiveTemplatesCount() async {
    try {
      final response = await _client
          .from(_tableName)
          .select('id')
          .eq('auto_generate', true);
      
      return response.length;
    } catch (e) {
      AppLogger.error('Failed to get active templates count: $e');
      return 0;
    }
  }

  // Get templates by property ID
  Future<List<BillTemplate>> getTemplatesByProperty(String propertyId) async {
    try {
      final response = await _client
          .from(_tableName)
          .select()
          .eq('property_id', propertyId)
          .order('name', ascending: true);

      return response.map<BillTemplate>((data) => BillTemplate.fromJson(data)).toList();
    } catch (e) {
      AppLogger.error('Failed to get property templates: $e');
      throw Exception('Failed to get property templates: $e');
    }
  }

  // Update a bill template
  Future<BillTemplate> updateTemplate(BillTemplate template) async {
    try {
      final response = await _client
          .from(_tableName)
          .update(template.toJson())
          .eq('id', template.id)
          .select()
          .single();

      return BillTemplate.fromJson(response);
    } catch (e) {
      AppLogger.error('Failed to update bill template: $e');
      throw Exception('Failed to update bill template: $e');
    }
  }

  // Delete a bill template
  Future<void> deleteTemplate(String id) async {
    try {
      await _client.from(_tableName).delete().eq('id', id);
    } catch (e) {
      AppLogger.error('Failed to delete bill template: $e');
      throw Exception('Failed to delete bill template: $e');
    }
  }

  // Generate bills from a template for a specific month and year
  Future<List<Bill>> generateBillsFromTemplate({
    required BillTemplate template,
    required int month,
    required int year,
    List<String>? specificTenantIds,
  }) async {
    try {
      List<Tenant> tenants = [];
      
      // If specific tenant IDs are provided, get those tenants
      if (specificTenantIds != null && specificTenantIds.isNotEmpty) {
        for (final tenantId in specificTenantIds) {
          final tenant = await serviceLocator.tenantService.getTenantById(tenantId);
          if (tenant != null) {
            tenants.add(tenant);
          }
        }
      } 
      // Otherwise, get tenants based on template settings
      else {
        // If template is for all tenants in a property
        if (template.propertyId != null && template.applyToAllTenants) {
          tenants = await _getTenantsInProperty(template.propertyId!);
        } 
        // If no property or not for all tenants, don't generate any bills
        else {
          return [];
        }
      }

      // Generate bills for each tenant
      List<Bill> generatedBills = [];
      final dueDate = template.generateDueDate(month, year);
      
      for (final tenant in tenants) {
        // Skip tenants without a room if they need one
        if (tenant.roomId == null && template.type == BillType.rent) {
          continue;
        }
        
        // Check if a similar bill already exists for this tenant and month
        final existingBill = await _checkExistingBill(
          tenantId: tenant.id,
          month: month,
          year: year,
          type: template.type,
        );
        
        // Skip if a similar bill already exists
        if (existingBill != null) {
          continue;
        }
        
        // Get room rental price if this is a rent bill
        double? roomRentalPrice;
        if (template.type == BillType.rent && tenant.roomId != null) {
          try {
            final room = await serviceLocator.roomService.getRoomById(tenant.roomId!);
            if (room != null) {
              roomRentalPrice = room.rentalPrice;
            }
          } catch (e) {
            AppLogger.error('Error fetching room rental price for tenant ${tenant.id}: $e');
          }
        }
        
        // Get property utility rate if this is a utility bill
        double? propertyUtilityRate;
        if (template.type == BillType.utility && template.propertyId != null && template.utilityType != null) {
          try {
            final property = await serviceLocator.propertyService.getPropertyById(template.propertyId!);
            if (property != null && property.utilityBills.isNotEmpty) {
              // Find matching utility bill by type
              final utilityTypeDisplayName = _getUtilityTypeDisplayName(template.utilityType!);
              final matchingUtilityBill = property.utilityBills.cast<UtilityBill?>().firstWhere(
                (bill) => bill?.name.toLowerCase().contains(utilityTypeDisplayName.toLowerCase()) == true,
                orElse: () => null,
              );
              
              if (matchingUtilityBill != null) {
                propertyUtilityRate = matchingUtilityBill.rate;
                AppLogger.info('Using property utility rate for ${template.utilityType}: ${propertyUtilityRate}');
              }
            }
          } catch (e) {
            AppLogger.error('Error fetching property utility rate for template ${template.id}: $e');
          }
        }
        
        // Generate and save the bill
        final bill = template.generateBill(
          tenantId: tenant.id,
          roomId: tenant.roomId,
          customDueDate: dueDate,
          roomRentalPrice: roomRentalPrice,
          propertyUtilityRate: propertyUtilityRate,
        );
        
        final createdBill = await serviceLocator.billService.createBill(bill);
        generatedBills.add(createdBill);
      }

      return generatedBills;
    } catch (e) {
      AppLogger.error('Failed to generate bills from template: $e');
      throw Exception('Failed to generate bills from template: $e');
    }
  }

  // Check for existing bills of the same type for a tenant in a specific month
  Future<Bill?> _checkExistingBill({
    required String tenantId,
    required int month,
    required int year,
    required BillType type,
  }) async {
    try {
      // Calculate first and last day of the month
      final firstDay = DateTime(year, month, 1);
      final lastDay = DateTime(year, month + 1, 0);
      
      // Query for bills of the same type in that month for the tenant
      final response = await _client
          .from('bills')
          .select()
          .eq('tenant_id', tenantId)
          .eq('type', type.name)
          .gte('due_date', firstDay.toIso8601String())
          .lte('due_date', lastDay.toIso8601String());
      
      final bills = response.map<Bill>((data) => Bill.fromJson(data)).toList();
      
      // Return the first bill found, or null if none exists
      return bills.isNotEmpty ? bills.first : null;
    } catch (e) {
      AppLogger.error('Error checking for existing bill: $e');
      return null;
    }
  }

  // Get all active tenants in a property
  Future<List<Tenant>> _getTenantsInProperty(String propertyId) async {
    try {
      // Get all rooms in the property
      final rooms = await serviceLocator.roomService.getRoomsByProperty(propertyId);
      
      // Get tenants with rooms in this property
      List<Tenant> tenants = [];
      for (final room in rooms) {
        // Find tenants assigned to this room
        final roomTenants = await serviceLocator.tenantService.getTenantsByRoomId(room.id);
        tenants.addAll(roomTenants);
      }
      
      // Filter to only active tenants
      return tenants.where((tenant) => tenant.status == TenantStatus.active).toList();
    } catch (e) {
      AppLogger.error('Error getting tenants in property: $e');
      return [];
    }
  }

  // Generate bills from a specific template immediately (manual generation)
  Future<List<Bill>> generateBillsFromTemplateNow({
    required BillTemplate template,
    List<String>? specificTenantIds,
  }) async {
    try {
      // Use the template's logic to determine the appropriate due date
      final nextDueDate = template.generateNextDueDate();
      
      AppLogger.info('Generating bills from template "${template.name}" for due date: ${nextDueDate.toString().split(' ')[0]}');
      
      return await generateBillsFromTemplate(
        template: template,
        month: nextDueDate.month,
        year: nextDueDate.year,
        specificTenantIds: specificTenantIds,
      );
    } catch (e) {
      AppLogger.error('Failed to generate bills from template now: $e');
      throw Exception('Failed to generate bills from template: $e');
    }
  }

  // Run automated bill generation for all templates
  Future<int> runAutomatedBillGeneration() async {
    try {
      // Get all templates with autoGenerate enabled
      final response = await _client
          .from(_tableName)
          .select()
          .eq('auto_generate', true);
      
      final templates = response.map<BillTemplate>((data) => BillTemplate.fromJson(data)).toList();
      
      // Track the number of bills generated
      int billsGenerated = 0;
      
      // Current date
      final now = DateTime.now();
      
      // For each template, check if it's time to generate bills
      for (final template in templates) {
        // Get the next appropriate due date from the template
        final nextDueDate = template.generateNextDueDate();
        final generateDate = nextDueDate.subtract(Duration(days: template.daysBeforeDue));
        
        // If today is the day to generate the bill (or we've passed it)
        if (now.isAfter(generateDate) || now.isAtSameMomentAs(generateDate)) {
          // Generate bills for the month of the next due date
          final bills = await generateBillsFromTemplate(
            template: template,
            month: nextDueDate.month,
            year: nextDueDate.year,
          );
          
          billsGenerated += bills.length;
          AppLogger.info('Generated ${bills.length} bills from template "${template.name}" for ${nextDueDate.month}/${nextDueDate.year}');
        }
      }
      
      AppLogger.info('Total bills generated in automated run: $billsGenerated');
      return billsGenerated;
    } catch (e) {
      AppLogger.error('Error running automated bill generation: $e');
      return 0;
    }
  }
  
  /// Get display name for utility type
  String _getUtilityTypeDisplayName(UtilityType type) {
    switch (type) {
      case UtilityType.water:
        return 'Water';
      case UtilityType.electricity:
        return 'Electricity';
      case UtilityType.gas:
        return 'Gas';
      case UtilityType.other:
        return 'Other Utility';
    }
  }
}
