-- Drop existing policies if they exist
DROP POLICY IF EXISTS bill_templates_select_policy ON bill_templates;
DROP POLICY IF EXISTS bill_templates_insert_policy ON bill_templates;
DROP POLICY IF EXISTS bill_templates_update_policy ON bill_templates;
DROP POLICY IF EXISTS bill_templates_delete_policy ON bill_templates;

-- Drop existing indexes if they exist
DROP INDEX IF EXISTS idx_bill_templates_property_id;
DROP INDEX IF EXISTS idx_bill_templates_auto_generate;

-- Drop existing table if it exists
DROP TABLE IF EXISTS bill_templates;

-- Create bill templates table for automated bill generation
CREATE TABLE IF NOT EXISTS bill_templates (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL,
    description TEXT NOT NULL,
    type TEXT NOT NULL,
    recurrence TEXT NOT NULL,
    amount DECIMAL(10, 2) NOT NULL,
    due_day_of_month INTEGER NOT NULL,
    property_id UUID REFERENCES properties(id),
    apply_to_all_tenants BOOLEAN DEFAULT FALSE,
    auto_generate BOOLEAN DEFAULT FALSE,
    days_before_due INTEGER DEFAULT 7,
    notes TEXT,
    utility_type TEXT,
    rate_per_unit DECIMAL(10, 2),
    include_in_rent BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE,
    
    -- Constraint to ensure due_day_of_month is between 1 and 31
    CONSTRAINT valid_due_day CHECK (due_day_of_month BETWEEN 1 AND 31)
);

-- Add RLS policies for bill_templates
ALTER TABLE bill_templates ENABLE ROW LEVEL SECURITY;

-- Policy for selecting bill templates (only for authenticated users)
CREATE POLICY bill_templates_select_policy ON bill_templates
    FOR SELECT
    USING ((SELECT auth.role()) = 'authenticated');

-- Policy for inserting bill templates (only for authenticated users)
CREATE POLICY bill_templates_insert_policy ON bill_templates
    FOR INSERT
    WITH CHECK ((SELECT auth.role()) = 'authenticated');

-- Policy for updating bill templates (only for authenticated users)
CREATE POLICY bill_templates_update_policy ON bill_templates
    FOR UPDATE
    USING ((SELECT auth.role()) = 'authenticated');

-- Policy for deleting bill templates (only for authenticated users)
CREATE POLICY bill_templates_delete_policy ON bill_templates
    FOR DELETE
    USING ((SELECT auth.role()) = 'authenticated');

-- Create index for faster lookups
CREATE INDEX idx_bill_templates_property_id ON bill_templates(property_id);
CREATE INDEX idx_bill_templates_auto_generate ON bill_templates(auto_generate); 