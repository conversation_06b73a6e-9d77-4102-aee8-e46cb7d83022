-- Create activity_logs table
CREATE TABLE IF NOT EXISTS activity_logs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  type TEXT NOT NULL,
  user_id UUID REFERENCES auth.users(id),
  tenant_id UUID REFERENCES tenants(id),
  room_id UUID REFERENCES rooms(id),
  property_id UUID REFERENCES properties(id),
  action TEXT NOT NULL,
  details JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL
);

-- Create indexes for faster lookups
CREATE INDEX IF NOT EXISTS activity_logs_type_idx ON activity_logs(type);
CREATE INDEX IF NOT EXISTS activity_logs_tenant_id_idx ON activity_logs(tenant_id);
CREATE INDEX IF NOT EXISTS activity_logs_room_id_idx ON activity_logs(room_id);
CREATE INDEX IF NOT EXISTS activity_logs_property_id_idx ON activity_logs(property_id);
CREATE INDEX IF NOT EXISTS activity_logs_created_at_idx ON activity_logs(created_at DESC);

-- Set RLS policies
ALTER TABLE activity_logs ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS activity_logs_select_policy ON activity_logs;
DROP POLICY IF EXISTS activity_logs_insert_policy ON activity_logs;
DROP POLICY IF EXISTS activity_logs_update_policy ON activity_logs;
DROP POLICY IF EXISTS activity_logs_delete_policy ON activity_logs;

-- Allow authenticated users to view logs
CREATE POLICY activity_logs_select_policy
  ON activity_logs
  FOR SELECT
  TO authenticated
  USING (true);

-- Allow authenticated users to insert logs
CREATE POLICY activity_logs_insert_policy
  ON activity_logs
  FOR INSERT
  TO authenticated
  WITH CHECK (true);

-- Only allow the owner to update their own logs
CREATE POLICY activity_logs_update_policy
  ON activity_logs
  FOR UPDATE
  TO authenticated
  USING (user_id = (SELECT auth.uid()));

-- Only allow the owner to delete their own logs
CREATE POLICY activity_logs_delete_policy
  ON activity_logs
  FOR DELETE
  TO authenticated
  USING (user_id = (SELECT auth.uid()));