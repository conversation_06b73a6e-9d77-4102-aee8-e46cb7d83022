import 'package:flutter/material.dart';
import '../../models/community_notice/community_notice.dart';
import '../../services/community_notice/community_notice_service.dart';
import '../../screens/community_notices/community_notices_screen.dart';
import '../../screens/community_notices/notice_detail_screen.dart';

class UrgentNoticesWidget extends StatefulWidget {
  const UrgentNoticesWidget({super.key});

  @override
  State<UrgentNoticesWidget> createState() => _UrgentNoticesWidgetState();
}

class _UrgentNoticesWidgetState extends State<UrgentNoticesWidget> {
  final CommunityNoticeService _noticeService = CommunityNoticeService();
  List<CommunityNotice> _urgentNotices = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadUrgentNotices();
  }

  Future<void> _loadUrgentNotices() async {
    try {
      final urgentNotices = await _noticeService.getUrgentNotices();
      setState(() {
        _urgentNotices = urgentNotices.take(5).toList(); // Limit to 5 notices
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Card(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              Row(
                children: [
                  const Icon(Icons.priority_high, color: Colors.red),
                  const SizedBox(width: 8),
                  const Text(
                    'Urgent Notices',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              const CircularProgressIndicator(),
            ],
          ),
        ),
      );
    }

    if (_urgentNotices.isEmpty) {
      return Card(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              Row(
                children: [
                  const Icon(Icons.priority_high, color: Colors.red),
                  const SizedBox(width: 8),
                  const Text(
                    'Urgent Notices',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  TextButton(
                    onPressed: () => Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const CommunityNoticesScreen(),
                      ),
                    ),
                    child: const Text('View All'),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Icon(
                Icons.check_circle_outline,
                size: 48,
                color: Colors.green[400],
              ),
              const SizedBox(height: 8),
              Text(
                'No urgent notices',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 16,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                'All caught up!',
                style: TextStyle(
                  color: Colors.grey[500],
                  fontSize: 14,
                ),
              ),
            ],
          ),
        ),
      );
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.priority_high, color: Colors.red),
                const SizedBox(width: 8),
                const Text(
                  'Urgent Notices',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                TextButton(
                  onPressed: () => Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const CommunityNoticesScreen(),
                    ),
                  ),
                  child: const Text('View All'),
                ),
              ],
            ),
            const SizedBox(height: 12),
            ..._urgentNotices.map((notice) => _buildUrgentNoticeItem(notice)),
          ],
        ),
      ),
    );
  }

  Widget _buildUrgentNoticeItem(CommunityNotice notice) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: InkWell(
        onTap: () => Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => NoticeDetailScreen(notice: notice),
          ),
        ),
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: _getPriorityColor(notice.priority).withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: _getPriorityColor(notice.priority).withValues(alpha: 0.3),
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Text(
                      notice.title,
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  const SizedBox(width: 8),
                  _buildPriorityChip(notice.priority),
                ],
              ),
              const SizedBox(height: 6),
              
              Row(
                children: [
                  _buildTypeChip(notice.type),
                  const SizedBox(width: 8),
                  if (notice.propertyName != null) ...[
                    Icon(Icons.location_on, size: 12, color: Colors.grey[600]),
                    const SizedBox(width: 2),
                    Expanded(
                      child: Text(
                        notice.propertyName!,
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: 12,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                  const SizedBox(width: 8),
                  Text(
                    _formatDate(notice.createdAt),
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 6),
              
              Text(
                notice.content,
                style: TextStyle(
                  color: Colors.grey[700],
                  fontSize: 13,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              
              if (notice.expiresAt != null && !notice.isExpired) ...[
                const SizedBox(height: 6),
                Row(
                  children: [
                    Icon(
                      Icons.schedule,
                      size: 14,
                      color: Colors.orange,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      'Expires ${_formatDate(notice.expiresAt!)}',
                      style: const TextStyle(
                        color: Colors.orange,
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTypeChip(NoticeType type) {
    Color color;
    switch (type) {
      case NoticeType.emergency:
        color = Colors.red;
        break;
      case NoticeType.maintenance:
        color = Colors.orange;
        break;
      case NoticeType.event:
        color = Colors.blue;
        break;
      case NoticeType.policy:
        color = Colors.purple;
        break;
      default:
        color = Colors.grey;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Text(
        type.displayName,
        style: TextStyle(
          color: color,
          fontSize: 10,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildPriorityChip(NoticePriority priority) {
    final color = _getPriorityColor(priority);

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            priority == NoticePriority.urgent 
                ? Icons.warning 
                : Icons.priority_high,
            size: 12,
            color: color,
          ),
          const SizedBox(width: 2),
          Text(
            priority.displayName,
            style: TextStyle(
              color: color,
              fontSize: 10,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Color _getPriorityColor(NoticePriority priority) {
    switch (priority) {
      case NoticePriority.urgent:
        return Colors.red;
      case NoticePriority.high:
        return Colors.orange;
      case NoticePriority.medium:
        return Colors.blue;
      default:
        return Colors.grey;
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
