-- Create receipts table to track payment receipts
CREATE TABLE IF NOT EXISTS receipts (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  payment_id UUID REFERENCES payments(id) ON DELETE CASCADE,
  bill_ids UUID[] NOT NULL, -- Array of bill IDs this receipt is for
  tenant_id UUID REFERENCES tenants(id) ON DELETE SET NULL,
  tenant_name TEXT,
  property_name TEXT,
  room_name TEXT,
  amount DECIMAL(10, 2) NOT NULL,
  payment_date TIMESTAMPTZ NOT NULL,
  printed_date TIMESTAMPTZ NOT NULL,
  status VARCHAR(20) NOT NULL DEFAULT 'verified', -- pending, verified, rejected
  method VARCHAR(20), -- cash, bankTransfer, mobileMoney, check, other
  receipt_reference VARCHAR(100) NOT NULL,
  notes TEXT,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ,
  verified_by UUID REFERENCES auth.users(id) ON DELETE SET NULL
);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_receipts_payment_id ON receipts(payment_id);
CREATE INDEX IF NOT EXISTS idx_receipts_tenant_id ON receipts(tenant_id);
CREATE INDEX IF NOT EXISTS idx_receipts_payment_date ON receipts(payment_date);
CREATE INDEX IF NOT EXISTS idx_receipts_status ON receipts(status);
CREATE INDEX IF NOT EXISTS idx_receipts_receipt_reference ON receipts(receipt_reference);

-- Create a GIN index for the bill_ids array for efficient searching
CREATE INDEX IF NOT EXISTS idx_receipts_bill_ids ON receipts USING GIN (bill_ids);

-- Create view for tenant receipt history
CREATE OR REPLACE VIEW tenant_receipts_view 
WITH (security_invoker=true)
AS
SELECT 
  r.id as receipt_id,
  r.payment_id,
  r.bill_ids,
  r.tenant_id,
  r.tenant_name,
  r.property_name,
  r.room_name,
  r.amount,
  r.payment_date,
  r.printed_date,
  r.status,
  r.method,
  r.receipt_reference,
  r.notes,
  r.created_at,
  r.updated_at,
  r.verified_by,
  t.first_name || ' ' || t.last_name as tenant_full_name,
  t.email as tenant_email
FROM 
  receipts r
LEFT JOIN 
  tenants t ON r.tenant_id = t.id;

-- Add RLS policies for receipts table
ALTER TABLE receipts ENABLE ROW LEVEL SECURITY;

-- Policy for users to see receipts
CREATE POLICY receipt_select_policy ON receipts
  FOR SELECT 
  USING (
    -- Allow authenticated users to see receipts
    (SELECT auth.role()) = 'authenticated'
  );

-- Policy for users to insert receipts
CREATE POLICY receipt_insert_policy ON receipts
  FOR INSERT 
  WITH CHECK (
    -- Allow authenticated users to create receipts
    (SELECT auth.role()) = 'authenticated'
  );

-- Policy for users to update receipts
CREATE POLICY receipt_update_policy ON receipts
  FOR UPDATE 
  USING (
    -- Allow authenticated users to update receipts
    (SELECT auth.role()) = 'authenticated'
  );

-- Policy for users to delete receipts
CREATE POLICY receipt_delete_policy ON receipts
  FOR DELETE 
  USING (
    -- Allow authenticated users to delete receipts
    (SELECT auth.role()) = 'authenticated'
  ); 