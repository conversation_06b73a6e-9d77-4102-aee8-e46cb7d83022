import 'package:supabase_flutter/supabase_flutter.dart';
import '../utils/logger.dart';

/// <PERSON>ript to initialize billing metadata for systems that already have bills
/// This should be run ONCE when upgrading from the old system to the new metadata-based system
class BillingMetadataInitializer {
  final SupabaseClient _client = Supabase.instance.client;
  
  Future<void> initializeBillingMetadata() async {
    try {
      AppLogger.info('Starting billing metadata initialization...');
      
      // Check if metadata already exists
      final existingMetadata = await _client
          .from('billing_metadata')
          .select('id')
          .eq('id', 'bill_number_tracker')
          .limit(1);
      
      if (existingMetadata.isNotEmpty) {
        AppLogger.info('Billing metadata already exists. Skipping initialization.');
        return;
      }
      
      // Find the highest individual bill number
      int highestBillNumber = 1000000;
      try {
        final billResponse = await _client
            .from('bills')
            .select('bill_number')
            .not('bill_number', 'is', null)
            .not('bill_number', 'like', 'Bill_G-%') // Exclude group bills
            .not('bill_number', 'like', 'REC-%') // Exclude receipt numbers
            .order('bill_number', ascending: false)
            .limit(1);
        
        if (billResponse.isNotEmpty && billResponse[0]['bill_number'] != null) {
          final String lastBillNumber = billResponse[0]['bill_number'];
          
          // Parse only pure numeric bill numbers
          final RegExp regex = RegExp(r'^(\d+)$');
          final match = regex.firstMatch(lastBillNumber);
          
          if (match != null && match.group(1) != null) {
            highestBillNumber = int.parse(match.group(1)!);
            AppLogger.info('Found highest bill number: $highestBillNumber');
          }
        }
      } catch (e) {
        AppLogger.warning('Could not find existing bill numbers: $e');
      }
      
      // Find the highest group bill number
      int highestGroupBillNumber = 1000000;
      try {
        final groupBillResponse = await _client
            .from('bills')
            .select('bill_number')
            .like('bill_number', 'Bill_G-%')
            .order('bill_number', ascending: false)
            .limit(1);
        
        if (groupBillResponse.isNotEmpty && groupBillResponse[0]['bill_number'] != null) {
          final String lastGroupBillNumber = groupBillResponse[0]['bill_number'];
          final RegExp regex = RegExp(r'Bill_G-(\d+)');
          final match = regex.firstMatch(lastGroupBillNumber);
          
          if (match != null && match.group(1) != null) {
            highestGroupBillNumber = int.parse(match.group(1)!);
            AppLogger.info('Found highest group bill number: $highestGroupBillNumber');
          }
        }
      } catch (e) {
        AppLogger.warning('Could not find existing group bill numbers: $e');
      }
      
      // Create the metadata record
      await _client.from('billing_metadata').insert({
        'id': 'bill_number_tracker',
        'last_bill_number': highestBillNumber,
        'last_group_bill_number': highestGroupBillNumber,
      });
      
      AppLogger.info('Billing metadata initialized successfully!');
      AppLogger.info('Last bill number set to: $highestBillNumber');
      AppLogger.info('Last group bill number set to: $highestGroupBillNumber');
      AppLogger.info('Next bill numbers will be: ${highestBillNumber + 1}, Bill_G-${highestGroupBillNumber + 1}');
      
    } catch (e) {
      AppLogger.error('Failed to initialize billing metadata: $e');
      throw Exception('Failed to initialize billing metadata: $e');
    }
  }
}
