import 'dart:convert';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:uuid/uuid.dart';
import 'package:jwt_decoder/jwt_decoder.dart';
import 'package:crypto/crypto.dart';
import '../utils/logger.dart';

class SessionService {
  final FlutterSecureStorage _secureStorage = const FlutterSecureStorage();
  final String _sessionKey = 'session_token';
  final String _refreshKey = 'refresh_token';
  final String _jwtSecret = 'your-secret-key'; // In production, use environment variables
  
  // Session lifetime in minutes
  final int _sessionLifetime = 30;
  final int _refreshLifetime = 60 * 24; // 1 day
  
  // Check if session is active
  Future<bool> hasActiveSession() async {
    final token = await _secureStorage.read(key: _sessionKey);
    if (token == null) return false;
    
    try {
      Map<String, dynamic> decodedToken = JwtDecoder.decode(token);
      final expiryDate = DateTime.fromMillisecondsSinceEpoch(decodedToken['exp'] * 1000);
      
      // Return true if token is valid and not expired
      return DateTime.now().isBefore(expiryDate);
    } catch (e) {
      AppLogger.error('Error validating session: $e');
      return false;
    }
  }
  
  // Create a new session
  Future<String?> createSession(String userId) async {
    try {
      // Generate a secure random session ID
      final sessionId = const Uuid().v4();
      
      // Set expiration time
      final expiry = DateTime.now().add(Duration(minutes: _sessionLifetime));
      final refreshExpiry = DateTime.now().add(Duration(minutes: _refreshLifetime));
      
      // Create JWT payload with minimal required data
      final payload = {
        'sub': userId,
        'jti': sessionId,
        'iat': DateTime.now().millisecondsSinceEpoch ~/ 1000,
        'exp': expiry.millisecondsSinceEpoch ~/ 1000,
      };
      
      // Create refresh token
      final refreshPayload = {
        'sub': userId,
        'jti': const Uuid().v4(),
        'exp': refreshExpiry.millisecondsSinceEpoch ~/ 1000,
      };
      
      // Sign the JWT tokens
      final token = _encodeJwt(payload);
      final refreshToken = _encodeJwt(refreshPayload);
      
      // Store tokens securely
      await _secureStorage.write(key: _sessionKey, value: token);
      await _secureStorage.write(key: _refreshKey, value: refreshToken);
      
      return token;
    } catch (e) {
      AppLogger.error('Error creating session: $e');
      return null;
    }
  }
  
  // Refresh session
  Future<String?> refreshSession() async {
    try {
      final refreshToken = await _secureStorage.read(key: _refreshKey);
      if (refreshToken == null) return null;
      
      final decoded = JwtDecoder.decode(refreshToken);
      final userId = decoded['sub'];
      
      // Check if refresh token is still valid
      if (JwtDecoder.isExpired(refreshToken)) {
        await invalidateSession();
        return null;
      }
      
      // Create new session token with new expiration
      return await createSession(userId);
    } catch (e) {
      AppLogger.error('Error refreshing session: $e');
      return null;
    }
  }
  
  // Invalidate session
  Future<void> invalidateSession() async {
    try {
      await _secureStorage.delete(key: _sessionKey);
      await _secureStorage.delete(key: _refreshKey);
    } catch (e) {
      AppLogger.error('Error invalidating session: $e');
    }
  }
  
  // Get current session info
  Future<Map<String, dynamic>?> getSessionInfo() async {
    final token = await _secureStorage.read(key: _sessionKey);
    if (token == null) return null;
    
    try {
      return JwtDecoder.decode(token);
    } catch (e) {
      AppLogger.error('Error decoding token: $e');
      return null;
    }
  }
  
  // Rotate session ID (to prevent session fixation attacks)
  Future<String?> rotateSession() async {
    try {
      final info = await getSessionInfo();
      if (info == null) return null;
      
      // Create new session with same user but new ID
      return await createSession(info['sub']);
    } catch (e) {
      AppLogger.error('Error rotating session: $e');
      return null;
    }
  }
  
  // JWT encoding that actually uses the secret key
  String _encodeJwt(Map<String, dynamic> payload) {
    final header = base64Url.encode(utf8.encode(json.encode({'alg': 'HS256', 'typ': 'JWT'})));
    final encodedPayload = base64Url.encode(utf8.encode(json.encode(payload)));
    final signature = _generateSignature('$header.$encodedPayload', _jwtSecret);
    
    return '$header.$encodedPayload.$signature';
  }
  
  // Generate HMAC signature using the secret
  String _generateSignature(String input, String secret) {
    final key = utf8.encode(secret);
    final bytes = utf8.encode(input);
    final hmacSha256 = Hmac(sha256, key);
    final digest = hmacSha256.convert(bytes);
    return base64Url.encode(digest.bytes);
  }
} 