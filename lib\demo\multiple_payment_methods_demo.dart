import 'package:flutter/material.dart';
import '../models/property/property_model.dart';
import '../screens/properties/property_detail_page.dart';

/// Demo showing how multiple payment methods work
class MultiplePaymentMethodsDemo extends StatelessWidget {
  const MultiplePaymentMethodsDemo({super.key});

  @override
  Widget build(BuildContext context) {
    // Create sample property with multiple payment methods
    final property = Property(
      id: 'demo-property-1',
      name: 'Sunset Apartments',
      address: '123 Main Street',
      city: 'Nairobi',
      state: 'Nairobi County',
      zipCode: '00100',
      description: 'Modern apartments with multiple payment options for tenants',
      paymentDetails: [
        // Bank Transfer Method
        PaymentDetails(
          method: PaymentMethod.bank,
          bankName: 'Equity Bank',
          accountNumber: '****************',
          notes: 'Please include your unit number in the reference',
        ),
        
        // Mobile Payment - Till
        PaymentDetails(
          method: PaymentMethod.mobilePayment,
          tillNumber: '247247',
          notes: 'Available 24/7 for instant payments',
        ),
        
        // Mobile Payment - Paybill
        PaymentDetails(
          method: PaymentMethod.mobilePayment,
          paybillNumber: '400200',
          notes: 'Use your unit number as account number',
        ),
        
        // Cash Payment
        PaymentDetails(
          method: PaymentMethod.cash,
          notes: 'Visit the office Monday-Friday 9AM-5PM',
        ),
      ],
      utilityBills: [
        UtilityBill(
          name: 'Water',
          rate: 50.0,
          unit: 'per unit',
          notes: 'Metered usage',
        ),
        UtilityBill(
          name: 'Electricity',
          rate: 25.0,
          unit: 'per kWh',
        ),
      ],
    );

    return Scaffold(
      appBar: AppBar(
        title: const Text('Multiple Payment Methods Demo'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: Column(
        children: [
          // Header explanation
          Container(
            margin: const EdgeInsets.all(16),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.blue.shade50,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.blue.shade200),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(Icons.info, color: Colors.blue.shade700),
                    const SizedBox(width: 8),
                    Text(
                      'Multiple Payment Methods',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                        color: Colors.blue.shade700,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Text(
                  'Properties can now accept multiple payment methods:\n'
                  '• Bank transfers with different accounts\n'
                  '• Multiple M-Pesa options (Till & Paybill)\n'
                  '• Cash payments with instructions\n'
                  '• Each method can have custom notes',
                  style: TextStyle(color: Colors.blue.shade600),
                ),
              ],
            ),
          ),
          
          // Payment Details Card Preview
          Expanded(
            child: SingleChildScrollView(
              child: PaymentDetailsCard(
                paymentDetails: property.paymentDetails,
                onTap: () {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Edit payment methods functionality would go here'),
                    ),
                  );
                },
                onAddPaymentMethod: () {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Add new payment method functionality would go here'),
                    ),
                  );
                },
              ),
            ),
          ),
          
          // Usage information
          Container(
            margin: const EdgeInsets.all(16),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.green.shade50,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.green.shade200),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(Icons.check_circle, color: Colors.green.shade700),
                    const SizedBox(width: 8),
                    Text(
                      'Benefits for Tenants',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                        color: Colors.green.shade700,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Text(
                  '• Multiple convenient payment options\n'
                  '• Quick copy-to-clipboard for details\n'
                  '• Clear instructions for each method\n'
                  '• 24/7 mobile payment availability',
                  style: TextStyle(
                    color: Colors.green.shade600,
                    fontSize: 13,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
