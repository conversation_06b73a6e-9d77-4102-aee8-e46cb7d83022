import 'package:intl/intl.dart';
import '../services/service_locator.dart';

class CurrencyFormatter {
  // Format number with currency symbol and thousands separator
  static String formatCurrency(double amount) {
    return serviceLocator.settingsService.formatCurrency(amount);
  }

  // Format number with just thousands separator (no currency symbol)
  static String formatNumber(double amount) {
    return serviceLocator.settingsService.formatNumber(amount);
  }

  // Get current currency symbol
  static String getCurrencySymbol() {
    return serviceLocator.settingsService.selectedCurrency.symbol;
  }

  // Get current currency code
  static String getCurrencyCode() {
    return serviceLocator.settingsService.selectedCurrency.code;
  }

  static String formatAmount(double amount) {
    try {
      final currencyCode = serviceLocator.settingsService.selectedCurrency.code;
      
      final formatter = NumberFormat.currency(
        symbol: '',
        decimalDigits: 2,
      );
      
      return '$currencyCode ${formatter.format(amount)}';
    } catch (e) {
      // Fallback formatting if settings service fails
      return 'KSH ${amount.toStringAsFixed(2)}';
    }
  }

  // Format with both symbol and code - keeping them on the same line
  static String formatAmountWithCode(double amount) {
    try {
      final currencyCode = serviceLocator.settingsService.selectedCurrency.code;
      
      final formatter = NumberFormat.currency(
        symbol: '',
        decimalDigits: 2,
      );
      
      return '$currencyCode ${formatter.format(amount)}';
    } catch (e) {
      // Fallback formatting if settings service fails
      return 'KSH ${amount.toStringAsFixed(2)}';
    }
  }
  
  static String formatCompactAmount(double amount) {
    try {
      final currencyCode = serviceLocator.settingsService.selectedCurrency.code;
      
      final formatter = NumberFormat.compact();
      
      return '$currencyCode ${formatter.format(amount)}';
    } catch (e) {
      // Fallback formatting if settings service fails
      if (amount >= 1000000) {
        return 'KSH ${(amount / 1000000).toStringAsFixed(1)}M';
      } else if (amount >= 1000) {
        return 'KSH ${(amount / 1000).toStringAsFixed(1)}K';
      } else {
        return 'KSH ${amount.toStringAsFixed(0)}';
      }
    }
  }

  static double parseAmount(String amount) {
    // Remove currency symbol and any whitespace
    final cleanAmount = amount.replaceAll(RegExp(r'[^\d.]'), '');
    return double.tryParse(cleanAmount) ?? 0.0;
  }
}
