import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'service_locator.dart';
import '../utils/logger.dart';

/// Service responsible for handling automated bill generation
class BillGenerationService {
  static const String _lastRunKey = 'last_bill_generation_run';
  
  /// Run automated bill generation if it hasn't been run today
  static Future<int> runDailyBillGeneration() async {
    try {
      // Check if we've already run today
      if (await _hasRunToday()) {
        AppLogger.info('Automated bill generation already run today');
        return 0;
      }
      
      // Run the bill generation
      final billsGenerated = await _runBillGeneration();
      
      // Update the last run timestamp
      await _updateLastRunTimestamp();
      
      return billsGenerated;
    } catch (e) {
      AppLogger.error('Error running automated bill generation: $e');
      return 0;
    }
  }
  
  /// Force run bill generation regardless of when it was last run
  static Future<int> forceRunBillGeneration() async {
    try {
      final billsGenerated = await _runBillGeneration();
      
      // Update the last run timestamp
      await _updateLastRunTimestamp();
      
      return billsGenerated;
    } catch (e) {
      AppLogger.error('Error running forced bill generation: $e');
      return 0;
    }
  }
  
  /// Run the bill generation process
  static Future<int> _runBillGeneration() async {
    AppLogger.info('Running automated bill generation');
    
    // Run the bill template service's automated generation
    final billsGenerated = await serviceLocator.billTemplateService.runAutomatedBillGeneration();
    
    AppLogger.info('Generated $billsGenerated bills');
    return billsGenerated;
  }
  
  /// Check if bill generation has already run today
  static Future<bool> _hasRunToday() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final lastRunTimestamp = prefs.getInt(_lastRunKey);
      
      if (lastRunTimestamp == null) {
        return false;
      }
      
      final lastRun = DateTime.fromMillisecondsSinceEpoch(lastRunTimestamp);
      final now = DateTime.now();
      
      // Check if the last run was today
      return lastRun.year == now.year && 
             lastRun.month == now.month && 
             lastRun.day == now.day;
    } catch (e) {
      AppLogger.error('Error checking last bill generation run: $e');
      return false;
    }
  }
  
  /// Update the timestamp of the last run
  static Future<void> _updateLastRunTimestamp() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt(_lastRunKey, DateTime.now().millisecondsSinceEpoch);
    } catch (e) {
      AppLogger.error('Error updating last bill generation timestamp: $e');
    }
  }
  
  /// Schedule bill generation to run periodically
  static void scheduleDailyCheck() {
    // Only schedule in release mode to avoid multiple timers during development
    if (kReleaseMode) {
      // Check once when the app starts
      runDailyBillGeneration();
      
      // Then check every 6 hours
      Timer.periodic(const Duration(hours: 6), (_) {
        runDailyBillGeneration();
      });
    }
  }
} 