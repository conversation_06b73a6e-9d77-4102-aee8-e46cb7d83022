-- Drop existing tables if they exist (in reverse order of dependencies)
DROP TABLE IF EXISTS public.room_amenities CASCADE;
DROP TABLE IF EXISTS public.rooms CASCADE;
DROP TABLE IF EXISTS public.amenities CASCADE;
DROP TABLE IF EXISTS public.room_types CASCADE;

-- Create room_types table (for predefined room types)
CREATE TABLE public.room_types (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL UNIQUE,
    description TEXT,
    is_predefined BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Create amenities table (for predefined amenities)
CREATE TABLE public.amenities (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL UNIQUE,
    description TEXT,
    icon TEXT,
    is_predefined BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Create rooms table
CREATE TABLE public.rooms (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL,
    property_id UUID NOT NULL REFERENCES public.properties(id) ON DELETE CASCADE,
    occupancy_status TEXT NOT NULL CHECK (occupancy_status IN ('vacant', 'occupied', 'reserved', 'maintenance')),
    room_type_id UUID REFERENCES public.room_types(id),
    custom_room_type TEXT,
    rental_price DECIMAL(10, 2) NOT NULL,
    size DECIMAL(10, 2),
    floor INT,
    is_furnished BOOLEAN NOT NULL DEFAULT FALSE,
    description TEXT,
    image_url TEXT,
    notes TEXT,
    additional_info JSONB,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    -- Either room_type_id OR custom_room_type must be set, but not both can be NULL
    CONSTRAINT valid_room_type CHECK (
        (room_type_id IS NOT NULL AND custom_room_type IS NULL) OR
        (room_type_id IS NULL AND custom_room_type IS NOT NULL)
    )
);

-- Create room_amenities junction table
CREATE TABLE public.room_amenities (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    room_id UUID NOT NULL REFERENCES public.rooms(id) ON DELETE CASCADE,
    amenity_id UUID REFERENCES public.amenities(id) ON DELETE CASCADE,
    custom_amenity_name TEXT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    -- Either amenity_id OR custom_amenity_name must be set, but not both
    CONSTRAINT valid_amenity CHECK (
        (amenity_id IS NOT NULL AND custom_amenity_name IS NULL) OR
        (amenity_id IS NULL AND custom_amenity_name IS NOT NULL)
    ),
    -- Ensure uniqueness for room-amenity combinations
    CONSTRAINT unique_room_amenity UNIQUE (room_id, amenity_id, custom_amenity_name)
);

-- Add indexes
CREATE INDEX idx_rooms_property_id ON public.rooms(property_id);
CREATE INDEX idx_rooms_room_type_id ON public.rooms(room_type_id);
CREATE INDEX idx_room_amenities_room_id ON public.room_amenities(room_id);
CREATE INDEX idx_room_amenities_amenity_id ON public.room_amenities(amenity_id);

-- Enable Row-Level Security
ALTER TABLE public.rooms ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.room_amenities ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.amenities ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.room_types ENABLE ROW LEVEL SECURITY;

-- Create policies for rooms
-- Users can view rooms for their own properties
CREATE POLICY "Users can view their own rooms" 
    ON public.rooms FOR SELECT
    USING (EXISTS (
        SELECT 1 FROM public.properties 
        WHERE properties.id = rooms.property_id 
        AND properties.user_id = auth.uid()
    ));

-- Users can insert rooms for their own properties
CREATE POLICY "Users can insert rooms for their own properties" 
    ON public.rooms FOR INSERT
    WITH CHECK (EXISTS (
        SELECT 1 FROM public.properties 
        WHERE properties.id = rooms.property_id 
        AND properties.user_id = auth.uid()
    ));

-- Users can update rooms for their own properties
CREATE POLICY "Users can update rooms for their own properties" 
    ON public.rooms FOR UPDATE
    USING (EXISTS (
        SELECT 1 FROM public.properties 
        WHERE properties.id = rooms.property_id 
        AND properties.user_id = auth.uid()
    ));

-- Users can delete rooms for their own properties
CREATE POLICY "Users can delete rooms for their own properties" 
    ON public.rooms FOR DELETE
    USING (EXISTS (
        SELECT 1 FROM public.properties 
        WHERE properties.id = rooms.property_id 
        AND properties.user_id = auth.uid()
    ));

-- Create policies for room_amenities
-- Users can view amenities for their own rooms
CREATE POLICY "Users can view amenities for their own rooms" 
    ON public.room_amenities FOR SELECT
    USING (EXISTS (
        SELECT 1 FROM public.rooms 
        JOIN public.properties ON rooms.property_id = properties.id
        WHERE rooms.id = room_amenities.room_id 
        AND properties.user_id = auth.uid()
    ));

-- Users can insert amenities for their own rooms
CREATE POLICY "Users can insert amenities for their own rooms" 
    ON public.room_amenities FOR INSERT
    WITH CHECK (EXISTS (
        SELECT 1 FROM public.rooms 
        JOIN public.properties ON rooms.property_id = properties.id
        WHERE rooms.id = room_amenities.room_id 
        AND properties.user_id = auth.uid()
    ));

-- Users can update amenities for their own rooms
CREATE POLICY "Users can update amenities for their own rooms" 
    ON public.room_amenities FOR UPDATE
    USING (EXISTS (
        SELECT 1 FROM public.rooms 
        JOIN public.properties ON rooms.property_id = properties.id
        WHERE rooms.id = room_amenities.room_id 
        AND properties.user_id = auth.uid()
    ));

-- Users can delete amenities for their own rooms
CREATE POLICY "Users can delete amenities for their own rooms" 
    ON public.room_amenities FOR DELETE
    USING (EXISTS (
        SELECT 1 FROM public.rooms 
        JOIN public.properties ON rooms.property_id = properties.id
        WHERE rooms.id = room_amenities.room_id 
        AND properties.user_id = auth.uid()
    ));

-- Create policies for amenities (predefined amenities are visible to all authenticated users)
CREATE POLICY "Authenticated users can view amenities" 
    ON public.amenities FOR SELECT
    USING (auth.role() IS NOT NULL);

-- Only admins can insert/update/delete predefined amenities
CREATE POLICY "Only admins can insert amenities" 
    ON public.amenities FOR INSERT
    WITH CHECK (auth.role() = 'authenticated' AND is_predefined = FALSE);

CREATE POLICY "Only admins can update amenities" 
    ON public.amenities FOR UPDATE
    USING (auth.role() = 'authenticated' AND is_predefined = FALSE);

CREATE POLICY "Only admins can delete amenities" 
    ON public.amenities FOR DELETE
    USING (auth.role() = 'authenticated' AND is_predefined = FALSE);

-- Create policies for room types (predefined types are visible to all authenticated users)
CREATE POLICY "Authenticated users can view room types" 
    ON public.room_types FOR SELECT
    USING (auth.role() IS NOT NULL);

-- Only admins can insert/update/delete predefined room types
CREATE POLICY "Only admins can insert room types" 
    ON public.room_types FOR INSERT
    WITH CHECK (auth.role() = 'authenticated' AND is_predefined = FALSE);

CREATE POLICY "Only admins can update room types" 
    ON public.room_types FOR UPDATE
    USING (auth.role() = 'authenticated' AND is_predefined = FALSE);

CREATE POLICY "Only admins can delete room types" 
    ON public.room_types FOR DELETE
    USING (auth.role() = 'authenticated' AND is_predefined = FALSE);

-- Create triggers to automatically update the updated_at timestamp
CREATE TRIGGER update_rooms_updated_at
BEFORE UPDATE ON public.rooms
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_amenities_updated_at
BEFORE UPDATE ON public.amenities
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_room_amenities_updated_at
BEFORE UPDATE ON public.room_amenities
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_room_types_updated_at
BEFORE UPDATE ON public.room_types
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- Insert predefined room types
INSERT INTO public.room_types (name, description, is_predefined) VALUES
('Single Room', 'A room designed for one person with a single bed', TRUE),
('Double Room', 'A room with a double/full-size bed for one or two people', TRUE),
('Twin Room', 'A room with two single beds for two people', TRUE),
('Studio', 'A self-contained unit with bedroom, living space and kitchenette', TRUE),
('Master Bedroom', 'The largest bedroom, often with an en-suite bathroom', TRUE),
('En-suite Room', 'A room with a private attached bathroom', TRUE),
('Shared Room', 'A room shared by multiple occupants', TRUE),
('Apartment', 'A self-contained living unit with multiple rooms', TRUE),
('Loft', 'An open concept room, often converted from industrial spaces', TRUE),
('Basement Room', 'A room located below ground level', TRUE); 