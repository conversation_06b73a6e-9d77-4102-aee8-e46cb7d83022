import 'package:uuid/uuid.dart';

enum BillStatus { paid, pending, overdue }

enum BillType { rent, utility, maintenance, service, other }

enum RecurrenceType { oneTime, monthly, quarterly, yearly }

enum UtilityType { water, electricity, gas, other }

class Bill {
  final String id;
  final String title;
  final String description;
  final double amount;
  final DateTime dueDate;
  final BillStatus status;
  final BillType type;
  final RecurrenceType recurrence;
  final String? tenantId;
  final String? propertyId;
  final String? roomId;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final DateTime? paidAt;
  final double? paidAmount;
  final String? notes;
  final String? billNumber;
  
  // New utility metering fields
  final bool includeInRent;
  final UtilityType? utilityType;
  final double? previousMeterReading;
  final double? currentMeterReading;
  final double? unitConsumed;
  final double? ratePerUnit;

  final List<Map<String, dynamic>>? billComponents;

  Bill({
    String? id,
    required this.title,
    required this.description,
    required this.amount,
    required this.dueDate,
    this.status = BillStatus.pending,
    required this.type,
    required this.recurrence,
    this.tenantId,
    this.propertyId,
    this.roomId,
    DateTime? createdAt,
    this.updatedAt,
    this.paidAt,
    this.paidAmount,
    this.notes,
    this.billNumber,
    this.includeInRent = false,
    this.utilityType,
    this.previousMeterReading,
    this.currentMeterReading,
    this.unitConsumed,
    this.ratePerUnit,
    this.billComponents,
  }) : id = id ?? const Uuid().v4(),
       createdAt = createdAt ?? DateTime.now();

  bool get isShared => tenantId == null;

  // Create a Bill from JSON data
  factory Bill.fromJson(Map<String, dynamic> json) {
    return Bill(
      id: json['id'],
      title: json['title'],
      description: json['description'],
      amount: json['amount'].toDouble(),
      dueDate: DateTime.parse(json['due_date']),
      status: _parseStatus(json['status']),
      type: _parseType(json['type']),
      recurrence: _parseRecurrence(json['recurrence']),
      tenantId: json['tenant_id'],
      propertyId: json['property_id'],
      roomId: json['room_id'],
      createdAt: DateTime.parse(json['created_at']),
      updatedAt:
          json['updated_at'] != null
              ? DateTime.parse(json['updated_at'])
              : null,
      paidAt: json['paid_at'] != null ? DateTime.parse(json['paid_at']) : null,
      paidAmount: json['paid_amount']?.toDouble(),
      notes: json['notes'],
      billNumber: json['bill_number'],
      includeInRent: json['include_in_rent'] ?? false,
      utilityType: json['utility_type'] != null 
          ? _parseUtilityType(json['utility_type']) 
          : null,
      previousMeterReading: json['previous_meter_reading']?.toDouble(),
      currentMeterReading: json['current_meter_reading']?.toDouble(),
      unitConsumed: json['unit_consumed']?.toDouble(),
      ratePerUnit: json['rate_per_unit']?.toDouble(),
      billComponents: json['bill_components'] != null
          ? List<Map<String, dynamic>>.from(json['bill_components'])
          : null,
    );
  }

  // Convert Bill to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'amount': amount,
      'due_date': dueDate.toIso8601String(),
      'status': status.name,
      'type': type.name,
      'recurrence': recurrence.name,
      'tenant_id': tenantId,
      'property_id': propertyId,
      'room_id': roomId,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'paid_at': paidAt?.toIso8601String(),
      'paid_amount': paidAmount,
      'notes': notes,
      'bill_number': billNumber,
      'include_in_rent': includeInRent,
      'utility_type': utilityType?.name,
      'previous_meter_reading': previousMeterReading,
      'current_meter_reading': currentMeterReading,
      'unit_consumed': unitConsumed,
      'rate_per_unit': ratePerUnit,
      'bill_components': billComponents,
    };
  }

  // Parse status from string
  static BillStatus _parseStatus(String? status) {
    if (status == null) return BillStatus.pending;

    switch (status) {
      case 'paid':
        return BillStatus.paid;
      case 'overdue':
        return BillStatus.overdue;
      case 'pending':
      default:
        return BillStatus.pending;
    }
  }

  // Parse type from string
  static BillType _parseType(String? type) {
    if (type == null) return BillType.other;

    switch (type) {
      case 'rent':
        return BillType.rent;
      case 'utility':
        return BillType.utility;
      case 'maintenance':
        return BillType.maintenance;
      case 'service':
        return BillType.service;
      case 'other':
      default:
        return BillType.other;
    }
  }

  // Parse recurrence type from string
  static RecurrenceType _parseRecurrence(String? recurrence) {
    if (recurrence == null) return RecurrenceType.oneTime;

    switch (recurrence) {
      case 'monthly':
        return RecurrenceType.monthly;
      case 'quarterly':
        return RecurrenceType.quarterly;
      case 'yearly':
        return RecurrenceType.yearly;
      case 'oneTime':
      default:
        return RecurrenceType.oneTime;
    }
  }

  // Parse utility type from string
  static UtilityType _parseUtilityType(String? type) {
    if (type == null) return UtilityType.other;

    switch (type) {
      case 'water':
        return UtilityType.water;
      case 'electricity':
        return UtilityType.electricity;
      case 'gas':
        return UtilityType.gas;
      case 'other':
      default:
        return UtilityType.other;
    }
  }

  // Create a copy of Bill with updated fields
  Bill copyWith({
    String? id,
    String? title,
    String? description,
    double? amount,
    DateTime? dueDate,
    BillStatus? status,
    BillType? type,
    RecurrenceType? recurrence,
    String? tenantId,
    String? propertyId,
    String? roomId,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? paidAt,
    double? paidAmount,
    String? notes,
    String? billNumber,
    bool? includeInRent,
    UtilityType? utilityType,
    double? previousMeterReading,
    double? currentMeterReading,
    double? unitConsumed,
    double? ratePerUnit,
    List<Map<String, dynamic>>? billComponents,
  }) {
    return Bill(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      amount: amount ?? this.amount,
      dueDate: dueDate ?? this.dueDate,
      status: status ?? this.status,
      type: type ?? this.type,
      recurrence: recurrence ?? this.recurrence,
      tenantId: tenantId ?? this.tenantId,
      propertyId: propertyId ?? this.propertyId,
      roomId: roomId ?? this.roomId,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      paidAt: paidAt ?? this.paidAt,
      paidAmount: paidAmount ?? this.paidAmount,
      notes: notes ?? this.notes,
      billNumber: billNumber ?? this.billNumber,
      includeInRent: includeInRent ?? this.includeInRent,
      utilityType: utilityType ?? this.utilityType,
      previousMeterReading: previousMeterReading ?? this.previousMeterReading,
      currentMeterReading: currentMeterReading ?? this.currentMeterReading,
      unitConsumed: unitConsumed ?? this.unitConsumed,
      ratePerUnit: ratePerUnit ?? this.ratePerUnit,
      billComponents: billComponents ?? this.billComponents,
    );
  }

  // Check if bill is overdue
  bool isOverdue() {
    return status != BillStatus.paid && dueDate.isBefore(DateTime.now());
  }

  // Get remaining amount to be paid
  double getRemainingAmount() {
    if (paidAmount == null) return amount;
    return amount - paidAmount!;
  }

  // Check if bill is fully paid
  bool isFullyPaid() {
    return status == BillStatus.paid ||
        (paidAmount != null && paidAmount! >= amount);
  }

  // Check if bill is partially paid
  bool isPartiallyPaid() {
    return paidAmount != null && paidAmount! > 0 && paidAmount! < amount;
  }

  // Add a method to calculate total amount including bill components
  double calculateTotalAmount() {
    double total = amount;
    if (billComponents != null) {
      for (var component in billComponents!) {
        total += component['amount'] ?? 0.0;
      }
    }
    return total;
  }
}
