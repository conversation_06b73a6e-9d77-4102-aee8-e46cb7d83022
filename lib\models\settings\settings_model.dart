class UserSettings {
  final String id; // User ID
  final String currencyCode;
  final String languageCode;
  final DateTime updatedAt;

  UserSettings({
    required this.id,
    required this.currencyCode,
    required this.languageCode,
    required this.updatedAt,
  });

  // Create from JSON (from database)
  factory UserSettings.fromJson(Map<String, dynamic> json) {
    return UserSettings(
      id: json['id'] as String,
      currencyCode: json['currency_code'] as String? ?? 'USD',
      languageCode: json['language_code'] as String? ?? 'en',
      updatedAt:
          json['updated_at'] != null
              ? DateTime.parse(json['updated_at'] as String)
              : DateTime.now(),
    );
  }

  // Convert to JSON (for database)
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'currency_code': currencyCode,
      'language_code': languageCode,
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  // Create a copy with modified fields
  UserSettings copyWith({String? currencyCode, String? languageCode}) {
    return UserSettings(
      id: id,
      currencyCode: currencyCode ?? this.currencyCode,
      languageCode: languageCode ?? this.languageCode,
      updatedAt: DateTime.now(),
    );
  }
}
