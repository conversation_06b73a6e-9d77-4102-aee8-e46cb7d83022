import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:intl/intl.dart';
import '../../models/property/property_model.dart';
import '../../models/room/room_model.dart';
import '../../models/tenant/tenant.dart';
import '../../services/service_locator.dart';
import '../../services/tenant/tenant_service.dart';
import '../../widgets/navigation/app_drawer.dart';
import '../../utils/currency_formatter.dart';
import '../../utils/auth_utils.dart';
import '../properties/property_detail_page.dart';
import '../rooms/room_detail_page.dart';
import '../../main.dart';

class DashboardPage extends StatefulWidget {
  const DashboardPage({super.key});

  @override
  State<DashboardPage> createState() => _DashboardPageState();
}

class _DashboardPageState extends State<DashboardPage>
    with WidgetsBindingObserver {
  List<Property> _properties = [];
  List<Room> _rooms = [];
  List<Tenant> _tenants = [];
  bool _isLoading = true;
  DateTime _lastRefreshed = DateTime.now();
  Timer? _refreshTimer; // Timer for updating the "Updated X ago" text
  Timer? _clockTimer; // Timer for updating the current time display

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _checkAuthAndLoadData();

    // Set up timer to update the "Updated X ago" text every 180 seconds
    _refreshTimer = Timer.periodic(const Duration(seconds: 180), (_) {
      if (mounted) {
        setState(() {
          // Just trigger a rebuild to update the time display
          if (kDebugMode)
            debugPrint(
              'Timer triggered UI refresh for time display: ${_getTimeAgo(_lastRefreshed)}',
            );
        });
      }
    });

    // Set up timer to update the current time every minute
    _clockTimer = Timer.periodic(const Duration(seconds: 60), (_) {
      if (mounted) {
        setState(() {
          // Trigger a rebuild to update the current time display
        });
      }
    });
  }

  @override
  void dispose() {
    _refreshTimer?.cancel(); // Cancel the timer when disposing
    _clockTimer?.cancel(); // Cancel the clock timer when disposing
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      // Reload data when app is resumed
      _loadData();
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Always reload data when dependencies change (e.g., when navigating back to this page)
    _loadData();
  }

  Future<void> _checkAuthAndLoadData() async {
    // Check if user is authenticated
    if (!AuthUtils.isAuthenticated()) {
      // Using postFrameCallback is the correct way to navigate during initialization
      // This is not an async gap issue since we're using addPostFrameCallback
      if (mounted) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          navigatorKey.currentState?.pushReplacementNamed('/login');
        });
      }
      return;
    }

    // If authenticated, load data
    _loadData();
  }

  Future<void> _loadData() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
    });

    try {
      // Get properties and rooms from services
      final properties =
          await serviceLocator.propertyService.getAllProperties();
      final rooms = await serviceLocator.roomService.getAllRooms();

      // Debug log for rooms (only in debug mode)
      if (kDebugMode) debugPrint('Loaded ${rooms.length} rooms from service');

      // Get tenants using direct tenant service
      List<Tenant> tenants = [];
      try {
        final client = Supabase.instance.client;
        final tenantService = TenantService(client);
        tenants = await tenantService.getAllTenants();
      } catch (tenantError) {
        // Handle case where tenants table might not exist yet
        // Just continue with empty tenants list
        // Using Material's print replacement which is stripped in release mode
        if (kDebugMode)
          debugPrint(
            'Tenants table may not exist yet: ${tenantError.toString()}',
          );
      }

      if (mounted) {
        setState(() {
          _properties = properties;
          _rooms = rooms;
          _tenants = tenants;
          _isLoading = false;
          _lastRefreshed = DateTime.now();
        });
      }
    } catch (e) {
      // Store the error message before the async gap
      final errorMessage = 'Error loading data: ${e.toString()}';

      if (mounted) {
        setState(() {
          _properties = [];
          _rooms = [];
          _tenants = [];
          _isLoading = false;
        });

        // Now it's safe to use the context because we're using the error message captured before the async gap
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(errorMessage), backgroundColor: Colors.red),
        );
      }
    }
  }

  Future<void> _handleRefresh() async {
    // Clear current data before loading new data
    setState(() {
      _properties = [];
      _rooms = [];
      _tenants = [];
    });

    // Load fresh data
    return _loadData();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Tenanta'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        actions: [
          IconButton(
            icon: const Icon(Icons.person),
            onPressed: () => Navigator.pushNamed(context, '/profile'),
            tooltip: 'Profile',
          ),
        ],
      ),
      drawer: const AppDrawer(),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : RefreshIndicator(
                onRefresh: _handleRefresh,
                child: SingleChildScrollView(
                  physics: const AlwaysScrollableScrollPhysics(),
                  padding: const EdgeInsets.all(12.0), // Reduced padding
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Welcome card with current date and time
                      _buildWelcomeCard(),
                      const SizedBox(height: 16), // Reduced spacing
                      // Summary cards
                      Row(
                        children: [
                          Expanded(
                            child: _buildSummaryCard(
                              'Properties',
                              _properties.length.toString(),
                              Icons.home_work,
                              Colors.blue,
                            ),
                          ),
                          const SizedBox(width: 12), // Reduced spacing
                          Expanded(
                            child: _buildSummaryCard(
                              'Rooms',
                              _rooms.length.toString(),
                              Icons.meeting_room,
                              Colors.green,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 12), // Reduced spacing
                      Row(
                        children: [
                          Expanded(
                            child: _buildSummaryCard(
                              'Tenants',
                              _tenants.length.toString(),
                              Icons.people,
                              Colors.amber,
                            ),
                          ),
                          const SizedBox(width: 12), // Reduced spacing
                          Expanded(
                            child: _buildSummaryCard(
                              'Vacant',
                              _getVacantRoomsCount().toString(),
                              Icons.check_circle,
                              Colors.purple,
                            ),
                          ),
                        ],
                      ),

                      // Recent properties section
                      const SizedBox(height: 20), // Reduced spacing
                      _buildSectionContainer(
                        'Recent Properties',
                        '/properties',
                        Colors.blue.withValues(alpha: 0.05),
                        _properties.isEmpty
                            ? const Center(
                              child: Padding(
                                padding: EdgeInsets.symmetric(vertical: 24),
                                child: Text('No properties yet'),
                              ),
                            )
                            : SizedBox(
                              height: 100, // Reduced height
                              child: ListView.builder(
                                scrollDirection: Axis.horizontal,
                                itemCount:
                                    _properties.length > 5
                                        ? 5
                                        : _properties.length,
                                itemBuilder: (context, index) {
                                  final property = _properties[index];
                                  return _buildPropertyCard(property);
                                },
                              ),
                            ),
                      ),

                      // Recent rooms section
                      const SizedBox(height: 16), // Reduced spacing
                      _buildSectionContainer(
                        'Recent Rooms',
                        '/rooms',
                        Colors.green.withValues(alpha: 0.05),
                        _rooms.isEmpty
                            ? const Center(
                              child: Padding(
                                padding: EdgeInsets.symmetric(vertical: 24),
                                child: Text('No rooms yet'),
                              ),
                            )
                            : SizedBox(
                              height: 160, // Reduced height
                              child: ListView.builder(
                                scrollDirection: Axis.horizontal,
                                itemCount:
                                    _rooms.length > 5 ? 5 : _rooms.length,
                                itemBuilder: (context, index) {
                                  final room = _rooms[index];
                                  return SizedBox(
                                    width: 220, // Reduced width
                                    child: _buildRoomCard(room),
                                  );
                                },
                              ),
                            ),
                      ),

                      // Recent tenants section
                      const SizedBox(height: 16), // Reduced spacing
                      _buildSectionContainer(
                        'Recent Tenants',
                        '/tenants',
                        Colors.amber.withValues(alpha: 0.05),
                        _tenants.isEmpty
                            ? const Center(
                              child: Padding(
                                padding: EdgeInsets.symmetric(vertical: 24),
                                child: Text('No tenants yet'),
                              ),
                            )
                            : Column(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                // Display up to 3 tenants
                                for (
                                  var i = 0;
                                  i <
                                      (_tenants.length > 3
                                          ? 3
                                          : _tenants.length);
                                  i++
                                )
                                  Padding(
                                    padding: const EdgeInsets.only(
                                      bottom: 4,
                                    ), // Reduced padding
                                    child: _buildTenantCard(_tenants[i]),
                                  ),
                                // View All button if there are more than 3 tenants
                                if (_tenants.length > 3)
                                  Padding(
                                    padding: const EdgeInsets.only(top: 8),
                                    child: OutlinedButton(
                                      onPressed: () {
                                        Navigator.pushNamed(
                                          context,
                                          '/tenants',
                                        );
                                      },
                                      style: OutlinedButton.styleFrom(
                                        minimumSize: const Size(
                                          double.infinity,
                                          32,
                                        ),
                                      ),
                                      child: const Text('View All Tenants'),
                                    ),
                                  ),
                              ],
                            ),
                      ),
                      const SizedBox(height: 16), // Bottom padding
                    ],
                  ),
                ),
              ),
    );
  }

  Widget _buildSummaryCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: color, size: 24),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Text(
              value,
              style: TextStyle(
                fontSize: 28,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPropertyCard(Property property) {
    // Get the count of rooms for this property
    final roomCount =
        _rooms.where((room) => room.propertyId == property.id).length;

    // Calculate total income for this property
    final propertyRooms =
        _rooms.where((room) => room.propertyId == property.id).toList();
    double totalIncome = 0;
    for (final room in propertyRooms) {
      totalIncome += room.rentalPrice;
    }
    final formattedIncome = CurrencyFormatter.formatAmountWithCode(totalIncome);

    return Card(
      margin: const EdgeInsets.only(right: 12),
      clipBehavior: Clip.antiAlias,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      elevation: 2,
      child: InkWell(
        onTap: () => _viewPropertyDetails(property),
        child: SizedBox(
          width: 280,
          height: 100,
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Image on the left side
              SizedBox(
                width: 90,
                child: ClipRRect(
                  child: Image.asset(
                    'assets/images/Apartment.png',
                    height: double.infinity,
                    fit: BoxFit.cover,
                  ),
                ),
              ),
              // Content on the right side
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.all(8),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // Property name
                      Text(
                        property.name,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.black87,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),

                      // Property address
                      Text(
                        property.address,
                        style: const TextStyle(
                          fontSize: 12,
                          color: Colors.black54,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),

                      // Room count row
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          const Icon(
                            Icons.meeting_room,
                            size: 14,
                            color: Colors.black87,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            '$roomCount ${roomCount == 1 ? 'Room' : 'Rooms'}',
                            style: const TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                              color: Colors.black87,
                            ),
                          ),
                        ],
                      ),

                      // Price on a new line with some space
                      const Spacer(),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          const Icon(
                            Icons.attach_money,
                            size: 14,
                            color: Colors.green,
                          ),
                          const SizedBox(width: 2),
                          Text(
                            formattedIncome,
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 14,
                              color: Colors.green,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildRoomCard(Room room) {
    // Get color based on occupancy status
    Color statusColor;
    String statusText;
    IconData statusIcon;

    switch (room.occupancyStatus) {
      case RoomOccupancyStatus.vacant:
        statusColor = Colors.green;
        statusText = 'Vacant';
        statusIcon = Icons.check_circle;
        break;
      case RoomOccupancyStatus.occupied:
        statusColor = Colors.blue;
        statusText = 'Occupied';
        statusIcon = Icons.person;
        break;
      case RoomOccupancyStatus.reserved:
        statusColor = Colors.orange;
        statusText = 'Reserved';
        statusIcon = Icons.bookmark;
        break;
      case RoomOccupancyStatus.maintenance:
        statusColor = Colors.red;
        statusText = 'Maintenance';
        statusIcon = Icons.home_repair_service;
        break;
    }

    return Card(
      clipBehavior: Clip.antiAlias,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(color: statusColor.withAlpha(30), width: 1),
      ),
      elevation: 3,
      shadowColor: statusColor.withAlpha(20),
      margin: const EdgeInsets.only(right: 12, bottom: 8),
      child: InkWell(
        onTap: () => _viewRoomDetails(room),
        child: Stack(
          children: [
            // Room status ribbon in the corner
            Positioned(
              top: 0,
              right: 0,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                decoration: BoxDecoration(
                  color: statusColor,
                  borderRadius: const BorderRadius.only(
                    bottomLeft: Radius.circular(12),
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(statusIcon, size: 12, color: Colors.white),
                    const SizedBox(width: 4),
                    Text(
                      statusText,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 11,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
            ),

            // Content
            Padding(
              padding: const EdgeInsets.fromLTRB(12, 12, 12, 10),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Room name with indicator
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Container(
                        width: 4,
                        height: 18,
                        decoration: BoxDecoration(
                          color: statusColor,
                          borderRadius: BorderRadius.circular(2),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          room.name,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Colors.black87,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 8),

                  // Property info
                  FutureBuilder<Property?>(
                    future: serviceLocator.propertyService.getPropertyById(
                      room.propertyId,
                    ),
                    builder: (context, snapshot) {
                      return Row(
                        children: [
                          const Icon(
                            Icons.home_work,
                            size: 12,
                            color: Colors.black54,
                          ),
                          const SizedBox(width: 6),
                          Expanded(
                            child: Text(
                              snapshot.hasData && snapshot.data != null
                                  ? snapshot.data!.name
                                  : 'Loading...',
                              style: const TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.w500,
                                color: Colors.black87,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      );
                    },
                  ),

                  const SizedBox(height: 8),

                  // Price with currency
                  Row(
                    children: [
                      const Icon(
                        Icons.currency_exchange,
                        size: 12,
                        color: Colors.black54,
                      ),
                      const SizedBox(width: 6),
                      Text(
                        CurrencyFormatter.formatAmountWithCode(
                          room.rentalPrice,
                        ),
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 13,
                          color: Colors.black87,
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 8),

                  // Amenities and view button in one row
                  Row(
                    children: [
                      // Furnished indicator
                      _buildSmallFeatureChip(
                        icon: Icons.chair,
                        isActive: room.isFurnished,
                        color: Colors.teal,
                      ),
                      const SizedBox(width: 6),
                      // Bathroom indicator
                      _buildSmallFeatureChip(
                        icon: Icons.bathroom,
                        isActive:
                            room.name.toLowerCase().contains('master') ||
                            room.rentalPrice > 30000,
                        color: Colors.purple,
                      ),
                      const Spacer(),
                      // View details button
                      TextButton(
                        onPressed: () => _viewRoomDetails(room),
                        style: TextButton.styleFrom(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 10,
                            vertical: 4,
                          ),
                          backgroundColor: statusColor.withAlpha(30),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(6),
                          ),
                          minimumSize: const Size(0, 0),
                          tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text(
                              'Details',
                              style: TextStyle(
                                fontSize: 12,
                                color: statusColor,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(width: 4),
                            Icon(
                              Icons.arrow_forward_ios,
                              size: 10,
                              color: statusColor,
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  int _getVacantRoomsCount() {
    return _rooms
        .where((room) => room.occupancyStatus == RoomOccupancyStatus.vacant)
        .length;
  }

  void _viewPropertyDetails(Property property) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => PropertyDetailPage(propertyId: property.id),
      ),
    );
  }

  void _viewRoomDetails(Room room) {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => RoomDetailPage(roomId: room.id)),
    );
  }

  Widget _buildTenantCard(Tenant tenant) {
    return Card(
      margin: EdgeInsets.zero,
      elevation: 1,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
        side: BorderSide(
          color: _getStatusColor(tenant.status).withAlpha(30),
          width: 1,
        ),
      ),
      child: Container(
        padding: const EdgeInsets.all(10), // Reduced padding
        child: Row(
          children: [
            // Avatar
            CircleAvatar(
              radius: 16, // Smaller avatar
              backgroundColor: _getStatusColor(tenant.status),
              child: Text(
                '${tenant.firstName[0]}${tenant.lastName[0]}',
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                  fontSize: 14, // Smaller font
                ),
              ),
            ),
            const SizedBox(width: 10),
            // Main content
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Name and status in one row
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          '${tenant.firstName} ${tenant.lastName}',
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 14,
                            color: Colors.black87,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      const SizedBox(width: 8),
                      // Compact status indicator
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 6,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: _getStatusColor(tenant.status).withAlpha(30),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              _getStatusIcon(tenant.status),
                              size: 10,
                              color: _getStatusColor(tenant.status),
                            ),
                            const SizedBox(width: 2),
                            Text(
                              _getStatusLabel(tenant.status),
                              style: TextStyle(
                                color: _getStatusColor(tenant.status),
                                fontSize: 10,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  // Property and room info in compact format
                  Row(
                    children: [
                      Icon(Icons.home_work, size: 12, color: Colors.grey[600]),
                      const SizedBox(width: 4),
                      Expanded(
                        child: FutureBuilder<Room?>(
                          future: _getRoomForTenant(tenant),
                          builder: (context, snapshot) {
                            if (snapshot.connectionState ==
                                ConnectionState.waiting) {
                              return Text(
                                'Loading...',
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Colors.grey[600],
                                ),
                              );
                            }

                            if (!snapshot.hasData || snapshot.data == null) {
                              return Text(
                                'No room assigned',
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Colors.grey[600],
                                ),
                              );
                            }

                            final room = snapshot.data!;
                            return FutureBuilder<Property?>(
                              future: serviceLocator.propertyService
                                  .getPropertyById(room.propertyId),
                              builder: (context, propertySnapshot) {
                                final propertyName =
                                    propertySnapshot.hasData &&
                                            propertySnapshot.data != null
                                        ? propertySnapshot.data!.name
                                        : 'Unknown Property';

                                return Text(
                                  '$propertyName • ${room.name}',
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: Colors.grey[600],
                                  ),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                );
                              },
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            // Compact view details button
            IconButton(
              onPressed: () {
                Navigator.pushNamed(context, '/tenants');
              },
              icon: Icon(
                Icons.arrow_forward_ios,
                size: 14,
                color: _getStatusColor(tenant.status),
              ),
              constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
              padding: const EdgeInsets.all(4),
              style: IconButton.styleFrom(
                backgroundColor: _getStatusColor(tenant.status).withAlpha(30),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(6),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Helper method to get room for tenant
  Future<Room?> _getRoomForTenant(Tenant tenant) async {
    try {
      // If tenant doesn't have a roomId, they're not assigned to any room
      if (tenant.roomId == null) {
        return null;
      }

      // Get the room by ID from the tenant's roomId
      return await serviceLocator.roomService.getRoomById(tenant.roomId!);
    } catch (e) {
      debugPrint('Error getting room for tenant ${tenant.id}: ${e.toString()}');
      return null;
    }
  }

  Color _getStatusColor(TenantStatus status) {
    switch (status) {
      case TenantStatus.active:
        return Colors.green;
      case TenantStatus.pending:
        return Colors.orange;
      case TenantStatus.movedOut:
        return Colors.grey;
    }
  }

  // Helper method to get status icon
  IconData _getStatusIcon(TenantStatus status) {
    switch (status) {
      case TenantStatus.active:
        return Icons.check_circle;
      case TenantStatus.pending:
        return Icons.pending;
      case TenantStatus.movedOut:
        return Icons.exit_to_app;
    }
  }

  // Helper method to get status label
  String _getStatusLabel(TenantStatus status) {
    switch (status) {
      case TenantStatus.active:
        return 'Active';
      case TenantStatus.pending:
        return 'Pending';
      case TenantStatus.movedOut:
        return 'Moved Out';
    }
  }

  // Smaller feature chip without text, just icon
  Widget _buildSmallFeatureChip({
    required IconData icon,
    required bool isActive,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(3),
      decoration: BoxDecoration(
        color: isActive ? color.withAlpha(24) : Colors.grey.withAlpha(15),
        borderRadius: BorderRadius.circular(4),
        border: Border.all(
          color: isActive ? color.withAlpha(38) : Colors.grey.withAlpha(20),
          width: 1,
        ),
      ),
      child: Icon(icon, size: 10, color: isActive ? color : Colors.grey),
    );
  }

  String _getTimeAgo(DateTime lastRefreshed) {
    final now = DateTime.now();
    final difference = now.difference(lastRefreshed);

    // For debugging
    debugPrint('Time difference in seconds: ${difference.inSeconds}');

    // Handle "just now" case more generously (within 2 minutes)
    if (difference.inSeconds < 120) {
      return 'just now';
    } else if (difference.inMinutes < 60) {
      final minutes = difference.inMinutes;
      return '$minutes ${minutes == 1 ? 'minute' : 'minutes'} ago';
    } else if (difference.inHours < 24) {
      final hours = difference.inHours;
      return '$hours ${hours == 1 ? 'hour' : 'hours'} ago';
    } else if (difference.inDays < 7) {
      final days = difference.inDays;
      return '$days ${days == 1 ? 'day' : 'days'} ago';
    } else if (difference.inDays < 30) {
      final weeks = (difference.inDays / 7).floor();
      return '$weeks ${weeks == 1 ? 'week' : 'weeks'} ago';
    } else if (difference.inDays < 365) {
      final months = (difference.inDays / 30).floor();
      return '$months ${months == 1 ? 'month' : 'months'} ago';
    } else {
      final years = (difference.inDays / 365).floor();
      return '$years ${years == 1 ? 'year' : 'years'} ago';
    }
  }

  Widget _buildSectionContainer(
    String title,
    String route,
    Color backgroundColor,
    Widget content,
  ) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 4),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: backgroundColor.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Section header
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  title,
                  style: Theme.of(
                    context,
                  ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
                ),
                TextButton(
                  onPressed: () {
                    Navigator.pushNamed(context, route);
                  },
                  child: const Text('View All'),
                ),
              ],
            ),
            const SizedBox(height: 8),
            // Section content
            content,
          ],
        ),
      ),
    );
  }

  Widget _buildWelcomeCard() {
    final now = DateTime.now();
    final formattedDate = DateFormat('MMMM d, yyyy').format(now);
    final formattedTime = DateFormat('h:mm a').format(now);
    final formattedDay = DateFormat('EEEE').format(now);

    return Card(
      elevation: 3,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          gradient: LinearGradient(
            colors: [
              Theme.of(
                context,
              ).colorScheme.primaryContainer.withAlpha(179), // 0.7 opacity
              Theme.of(
                context,
              ).colorScheme.primaryContainer.withAlpha(77), // 0.3 opacity
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Flexible(
                    child: Row(
                      children: [
                        Icon(
                          Icons.waving_hand,
                          color: Theme.of(context).colorScheme.primary,
                          size: 24,
                        ),
                        const SizedBox(width: 8),
                        Flexible(
                          child: Text(
                            'Welcome..',
                            style: Theme.of(
                              context,
                            ).textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: Theme.of(context).colorScheme.onSurface,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                  ),
                  // Last refreshed indicator
                  const SizedBox(width: 4),
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Icon(Icons.refresh, size: 14, color: Colors.grey),
                      const SizedBox(width: 4),
                      Text(
                        'Updated ${_getTimeAgo(_lastRefreshed)}',
                        style: const TextStyle(
                          fontSize: 12,
                          color: Colors.grey,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    flex: 3,
                    child: Row(
                      children: [
                        Icon(
                          Icons.calendar_today,
                          size: 16,
                          color: Theme.of(context).colorScheme.primary,
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                formattedDay,
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  color:
                                      Theme.of(context).colorScheme.onSurface,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                              Text(
                                formattedDate,
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Theme.of(context).colorScheme.onSurface
                                      .withAlpha(179), // 0.7 opacity
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    height: 24,
                    width: 1,
                    color: Theme.of(
                      context,
                    ).colorScheme.onSurface.withAlpha(51), // 0.2 opacity
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    flex: 2,
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.access_time,
                          size: 16,
                          color: Theme.of(context).colorScheme.primary,
                        ),
                        const SizedBox(width: 8),
                        Flexible(
                          child: Text(
                            formattedTime,
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: Theme.of(context).colorScheme.onSurface,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
