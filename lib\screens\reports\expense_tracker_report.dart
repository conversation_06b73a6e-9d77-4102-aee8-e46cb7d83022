import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../models/expense/expense_category_model.dart';
import '../../models/expense/expense_model.dart';
import '../../models/property/property_model.dart';
import '../../models/room/room_model.dart';
import '../../services/service_locator.dart';
import '../../utils/currency_formatter.dart';
import '../../widgets/common/app_error_widget.dart';
import '../../widgets/loading_indicator.dart';
import '../../widgets/navigation/app_drawer.dart';
import 'date_range_selector.dart';

class ExpenseTrackerReport extends StatefulWidget {
  const ExpenseTrackerReport({super.key});

  @override
  State<ExpenseTrackerReport> createState() => _ExpenseTrackerReportState();
}

class _ExpenseTrackerReportState extends State<ExpenseTrackerReport> {
  DateTimeRange? _selectedDateRange;
  String _selectedPeriod = 'this_month';
  String? _selectedCategoryId;
  String? _selectedPropertyId;
  String? _selectedRoomId;
  String? _selectedVendorName;
  List<ExpenseModel> _expenses = [];
  List<ExpenseCategoryModel> _categories = [];
  List<Property> _properties = [];
  List<Room> _rooms = [];
  List<String> _vendorNames = [];
  bool _isLoading = false;
  bool _isLoadingFilters = false;
  bool _isRefreshing = false;
  String? _error;

  @override
  void initState() {
    super.initState();
    // Set default date range to current month
    final now = DateTime.now();
    _selectedDateRange = DateTimeRange(
      start: DateTime(now.year, now.month, 1),
      end: DateTime(now.year, now.month + 1, 0),
    );
    _loadFilters();
    _loadExpenses();
  }

  Future<void> _loadFilters() async {
    setState(() {
      _isLoadingFilters = true;
    });

    try {
      // Load categories
      final categoriesFuture = serviceLocator.expenseCategoryService.getCategories();
      
      // Load properties
      final propertiesFuture = serviceLocator.propertyService.getAllProperties();
      
      // Load rooms (we'll filter these based on selected property later)
      final roomsFuture = serviceLocator.roomService.getAllRooms();

      // Load unique vendor names from expenses
      final vendorsFuture = serviceLocator.expenseService.getExpenses().then((expenses) {
        final uniqueVendors = expenses
            .where((e) => e.vendorName != null)
            .map((e) => e.vendorName!)
            .toSet()
            .toList()
          ..sort();
        return uniqueVendors;
      });

      final results = await Future.wait([
        categoriesFuture,
        propertiesFuture,
        roomsFuture,
        vendorsFuture,
      ]);

      if (mounted) {
        setState(() {
          _categories = results[0] as List<ExpenseCategoryModel>;
          _properties = results[1] as List<Property>;
          _rooms = results[2] as List<Room>;
          _vendorNames = results[3] as List<String>;
          _isLoadingFilters = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoadingFilters = false;
        });
      }
    }
  }

  Future<void> _loadExpenses() async {
    if (_selectedDateRange == null) return;

    setState(() {
      _isLoading = !_isRefreshing;
      _isRefreshing = false;
      _error = null;
    });

    try {
      final expenses = await serviceLocator.expenseService.getExpenses(
        startDate: _selectedDateRange!.start,
        endDate: _selectedDateRange!.end,
        categoryId: _selectedCategoryId,
        propertyId: _selectedPropertyId,
        roomId: _selectedRoomId,
        useExpenseSummary: true,
      );

      // Filter by vendor name if selected (since the backend doesn't support this filter yet)
      final filteredExpenses = _selectedVendorName != null
          ? expenses.where((e) => e.vendorName == _selectedVendorName).toList()
          : expenses;

      setState(() {
        _expenses = filteredExpenses;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  Future<void> _refreshData() async {
    setState(() {
      _isRefreshing = true;
    });
    await _loadExpenses();
  }

  Map<String, double> _calculateCategoryTotals() {
    final totals = <String, double>{};
    for (final expense in _expenses) {
      final category = expense.categoryName ?? 'Uncategorized';
      totals[category] = (totals[category] ?? 0) + expense.amount;
    }
    return totals;
  }

  Map<String, double> _calculatePropertyTotals() {
    final totals = <String, double>{};
    for (final expense in _expenses) {
      final property = expense.propertyName ?? 'No Property';
      totals[property] = (totals[property] ?? 0) + expense.amount;
    }
    return totals;
  }

  Map<String, double> _calculateVendorTotals() {
    final totals = <String, double>{};
    for (final expense in _expenses) {
      final vendor = expense.vendorName ?? 'No Vendor';
      totals[vendor] = (totals[vendor] ?? 0) + expense.amount;
    }
    return totals;
  }

  double _calculateTotalExpenses() {
    return _expenses.fold(0, (sum, expense) => sum + expense.amount);
  }

  void _resetFilters() {
    setState(() {
      _selectedCategoryId = null;
      _selectedPropertyId = null;
      _selectedRoomId = null;
      _selectedVendorName = null;
    });
    _loadExpenses();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Expense Tracker'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: _showFilterDialog,
            tooltip: 'Filter expenses',
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _refreshData,
            tooltip: 'Refresh data',
          ),
        ],
      ),
      drawer: const AppDrawer(),
      body: _isLoading
          ? const LoadingIndicator()
          : _error != null
              ? AppErrorWidget(
                  message: _error!,
                  onRetry: _loadExpenses,
                  title: 'Failed to Load Expenses',
                )
              : RefreshIndicator(
                  onRefresh: _refreshData,
                  child: _expenses.isEmpty
                      ? _buildEmptyState()
                      : _buildReportContent(),
                ),
    );
  }

  Future<void> _showFilterDialog() async {
    await showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) {
          // Get filtered rooms based on selected property
          final List<Room> filteredRooms = _selectedPropertyId != null 
              ? _rooms.where((room) => room.propertyId == _selectedPropertyId).toList()
              : [];
              
          return AlertDialog(
            title: const Text('Filter Expenses'),
            content: _isLoadingFilters
                ? const SizedBox(
                    height: 100,
                    child: Center(
                      child: CircularProgressIndicator(),
                    ),
                  )
                : SingleChildScrollView(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text('Category'),
                        const SizedBox(height: 8),
                        DropdownButtonFormField<String?>(
                          isExpanded: true,
                          decoration: const InputDecoration(
                            border: OutlineInputBorder(),
                            contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                          ),
                          value: _selectedCategoryId,
                          hint: const Text('All Categories'),
                          items: [
                            const DropdownMenuItem<String?>(
                              value: null,
                              child: Text('All Categories'),
                            ),
                            ..._categories.map((category) => DropdownMenuItem<String?>(
                              value: category.id,
                              child: Text(category.name),
                            )),
                          ],
                          onChanged: (value) {
                            setDialogState(() {
                              _selectedCategoryId = value;
                            });
                          },
                        ),
                        const SizedBox(height: 16),
                        const Text('Vendor'),
                        const SizedBox(height: 8),
                        DropdownButtonFormField<String?>(
                          isExpanded: true,
                          decoration: const InputDecoration(
                            border: OutlineInputBorder(),
                            contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                          ),
                          value: _selectedVendorName,
                          hint: const Text('All Vendors'),
                          items: [
                            const DropdownMenuItem<String?>(
                              value: null,
                              child: Text('All Vendors'),
                            ),
                            ..._vendorNames.map((vendor) => DropdownMenuItem<String?>(
                              value: vendor,
                              child: Text(vendor),
                            )),
                          ],
                          onChanged: (value) {
                            setDialogState(() {
                              _selectedVendorName = value;
                            });
                          },
                        ),
                        const SizedBox(height: 16),
                        const Text('Property'),
                        const SizedBox(height: 8),
                        DropdownButtonFormField<String?>(
                          isExpanded: true,
                          decoration: const InputDecoration(
                            border: OutlineInputBorder(),
                            contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                          ),
                          value: _selectedPropertyId,
                          hint: const Text('All Properties'),
                          items: [
                            const DropdownMenuItem<String?>(
                              value: null,
                              child: Text('All Properties'),
                            ),
                            ..._properties.map((property) => DropdownMenuItem<String?>(
                              value: property.id,
                              child: Text(property.name),
                            )),
                          ],
                          onChanged: (value) {
                            setDialogState(() {
                              _selectedPropertyId = value;
                              // Reset room selection when property changes
                              _selectedRoomId = null;
                            });
                          },
                        ),
                        const SizedBox(height: 16),
                        const Text('Room'),
                        const SizedBox(height: 8),
                        DropdownButtonFormField<String?>(
                          isExpanded: true,
                          decoration: InputDecoration(
                            border: const OutlineInputBorder(),
                            contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                            enabled: _selectedPropertyId != null,
                            hintText: _selectedPropertyId == null 
                                ? 'Select a property first'
                                : 'All Rooms in this Property',
                          ),
                          value: _selectedRoomId,
                          hint: Text(_selectedPropertyId == null 
                              ? 'Select a property first'
                              : 'All Rooms in this Property'),
                          items: _selectedPropertyId == null
                              ? []
                              : [
                                  const DropdownMenuItem<String?>(
                                    value: null,
                                    child: Text('All Rooms in this Property'),
                                  ),
                                  ...filteredRooms.map((room) => DropdownMenuItem<String?>(
                                        value: room.id,
                                        child: Text(room.name),
                                      )),
                                ],
                          onChanged: _selectedPropertyId == null
                              ? null
                              : (value) {
                                  setDialogState(() {
                                    _selectedRoomId = value;
                                  });
                                },
                        ),
                      ],
                    ),
                  ),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  _resetFilters();
                },
                child: const Text('RESET'),
              ),
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                },
                child: const Text('CANCEL'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  _loadExpenses();
                },
                child: const Text('APPLY'),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildEmptyState() {
    return ListView(
      children: [
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const SizedBox(height: 40),
              DateRangeSelector(
                selectedRange: _selectedDateRange!,
                onRangeSelected: (range) {
                  setState(() {
                    _selectedDateRange = range;
                  });
                  _loadExpenses();
                },
                selectedPeriod: _selectedPeriod,
                onPeriodChanged: (period) {
                  setState(() {
                    _selectedPeriod = period;
                  });
                },
              ),
              const SizedBox(height: 40),
              _buildActiveFiltersChips(),
              const SizedBox(height: 20),
              Icon(
                Icons.receipt_long,
                size: 80,
                color: Colors.grey[400],
              ),
              const SizedBox(height: 16),
              Text(
                'No Expenses Found',
                style: Theme.of(context).textTheme.headlineSmall,
              ),
              const SizedBox(height: 8),
              Text(
                'There are no expenses recorded for the selected filters.',
                textAlign: TextAlign.center,
                style: TextStyle(color: Colors.grey[600]),
              ),
              const SizedBox(height: 24),
              ElevatedButton.icon(
                onPressed: () {
                  Navigator.pushNamed(context, '/expenses/add');
                },
                icon: const Icon(Icons.add),
                label: const Text('Add New Expense'),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildActiveFiltersChips() {
    final List<Widget> chips = [];
    
    if (_selectedCategoryId != null) {
      final category = _categories.firstWhere(
        (c) => c.id == _selectedCategoryId,
        orElse: () => ExpenseCategoryModel(
          name: 'Unknown Category', 
          color: const Color(0xFFFFFFFF),
          userId: '',
          createdAt: DateTime.now(),
        ),
      );
      
      chips.add(
        Chip(
          label: Text(category.name),
          deleteIcon: const Icon(Icons.close, size: 18),
          onDeleted: () {
            setState(() {
              _selectedCategoryId = null;
            });
            _loadExpenses();
          },
          backgroundColor: category.color,
          labelStyle: TextStyle(
            color: category.color.computeLuminance() > 0.5 ? Colors.black : Colors.white,
          ),
        ),
      );
    }
    
    if (_selectedVendorName != null) {
      chips.add(
        Chip(
          label: Text(_selectedVendorName!),
          deleteIcon: const Icon(Icons.close, size: 18),
          onDeleted: () {
            setState(() {
              _selectedVendorName = null;
            });
            _loadExpenses();
          },
          backgroundColor: Theme.of(context).primaryColor.withAlpha(51),
        ),
      );
    }
    
    if (_selectedPropertyId != null) {
      final property = _properties.firstWhere(
        (p) => p.id == _selectedPropertyId,
        orElse: () => Property(
          id: '', 
          name: 'Unknown Property', 
          address: '', 
          city: '', 
          state: '', 
          zipCode: '', 
          utilityBills: [],
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
      );
      
      chips.add(
        Chip(
          label: Text(property.name),
          deleteIcon: const Icon(Icons.close, size: 18),
          onDeleted: () {
            setState(() {
              _selectedPropertyId = null;
              _selectedRoomId = null; // Also clear room when property is cleared
            });
            _loadExpenses();
          },
          backgroundColor: Theme.of(context).primaryColor.withAlpha(51),
        ),
      );
    }
    
    if (_selectedRoomId != null) {
      final room = _rooms.firstWhere(
        (r) => r.id == _selectedRoomId,
        orElse: () => Room(
          id: '', 
          name: 'Unknown Room', 
          propertyId: '', 
          occupancyStatus: RoomOccupancyStatus.vacant,
          rentalPrice: 0,
          amenities: [],
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          isFurnished: false,
        ),
      );
      
      chips.add(
        Chip(
          label: Text(room.name),
          deleteIcon: const Icon(Icons.close, size: 18),
          onDeleted: () {
            setState(() {
              _selectedRoomId = null;
            });
            _loadExpenses();
          },
          backgroundColor: Theme.of(context).primaryColor.withAlpha(51),
        ),
      );
    }
    
    if (chips.isEmpty) {
      return const SizedBox.shrink();
    }
    
    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: chips,
    );
  }

  Widget _buildReportContent() {
    return ListView(
      padding: const EdgeInsets.all(16.0),
      children: [
        DateRangeSelector(
          selectedRange: _selectedDateRange!,
          onRangeSelected: (range) {
            setState(() {
              _selectedDateRange = range;
            });
            _loadExpenses();
          },
          selectedPeriod: _selectedPeriod,
          onPeriodChanged: (period) {
            setState(() {
              _selectedPeriod = period;
            });
          },
        ),
        const SizedBox(height: 16),
        _buildActiveFiltersChips(),
        const SizedBox(height: 24),
        _buildSummaryCard(),
        const SizedBox(height: 24),
        _buildCategoryBreakdown(),
        const SizedBox(height: 24),
        _buildPropertyBreakdown(),
        const SizedBox(height: 24),
        _buildVendorBreakdown(),
        const SizedBox(height: 24),
        _buildExpensesList(),
        const SizedBox(height: 16),
      ],
    );
  }

  Widget _buildSummaryCard() {
    final total = _calculateTotalExpenses();
    final dateFormat = DateFormat('MMM d, yyyy');
    final startDate = dateFormat.format(_selectedDateRange!.start);
    final endDate = dateFormat.format(_selectedDateRange!.end);
    
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Theme.of(context).primaryColor.withAlpha(178),
              Theme.of(context).primaryColor,
            ],
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(20.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.account_balance_wallet,
                        color: Colors.white.withAlpha(230),
                      ),
                      const SizedBox(width: 8),
                      const Text(
                        'Total Expenses',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                    ],
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: Colors.white.withAlpha(51), // 0.2 opacity = 51 in alpha (255 * 0.2)
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Text(
                      '${_expenses.length} Expenses',
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Text(
                CurrencyFormatter.formatCurrency(total),
                style: const TextStyle(
                  fontSize: 28,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                '$startDate - $endDate',
                style: TextStyle(
                  color: Colors.white.withAlpha(230),
                  fontSize: 14,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCategoryBreakdown() {
    final categoryTotals = _calculateCategoryTotals();
    final total = _calculateTotalExpenses();
    final sortedEntries = categoryTotals.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));
    final displayEntries = sortedEntries.take(10).toList();
    final hasMore = sortedEntries.length > 10;
    
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.category,
                      color: Theme.of(context).primaryColor,
                    ),
                    const SizedBox(width: 8),
                    const Text(
                      'Expenses by Category',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                if (hasMore)
                  TextButton(
                    onPressed: () {
                      _showFullBreakdown('Categories', sortedEntries);
                    },
                    child: const Text('View All'),
                  ),
              ],
            ),
            const Divider(height: 24),
            ...displayEntries.map((entry) {
              final percentage = total > 0 ? (entry.value / total * 100) : 0;
              return Padding(
                padding: const EdgeInsets.only(bottom: 12.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                          child: Text(
                            entry.key,
                            style: const TextStyle(fontWeight: FontWeight.w500),
                          ),
                        ),
                        Text(
                          CurrencyFormatter.formatCurrency(entry.value),
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                      ],
                    ),
                    const SizedBox(height: 6),
                    Stack(
                      children: [
                        Container(
                          height: 8,
                          width: double.infinity,
                          decoration: BoxDecoration(
                            color: Colors.grey[200],
                            borderRadius: BorderRadius.circular(5),
                          ),
                        ),
                        FractionallySizedBox(
                          widthFactor: percentage / 100,
                          child: Container(
                            height: 8,
                            decoration: BoxDecoration(
                              color: Theme.of(context).primaryColor,
                              borderRadius: BorderRadius.circular(5),
                            ),
                          ),
                        ),
                      ],
                    ),
                    Align(
                      alignment: Alignment.centerRight,
                      child: Text(
                        '${percentage.toStringAsFixed(1)}%',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey[600],
                        ),
                      ),
                    ),
                  ],
                ),
              );
            }),
          ],
        ),
      ),
    );
  }

  Widget _buildPropertyBreakdown() {
    final propertyTotals = _calculatePropertyTotals();
    final total = _calculateTotalExpenses();
    final sortedEntries = propertyTotals.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));
    final displayEntries = sortedEntries.take(10).toList();
    final hasMore = sortedEntries.length > 10;
    
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.home,
                      color: Theme.of(context).primaryColor,
                    ),
                    const SizedBox(width: 8),
                    const Text(
                      'Expenses by Property',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                if (hasMore)
                  TextButton(
                    onPressed: () {
                      _showFullBreakdown('Properties', sortedEntries);
                    },
                    child: const Text('View All'),
                  ),
              ],
            ),
            const Divider(height: 24),
            ...displayEntries.map((entry) {
              final percentage = total > 0 ? (entry.value / total * 100) : 0;
              return Padding(
                padding: const EdgeInsets.only(bottom: 12.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                          child: Text(
                            entry.key,
                            style: const TextStyle(fontWeight: FontWeight.w500),
                          ),
                        ),
                        Text(
                          CurrencyFormatter.formatCurrency(entry.value),
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                      ],
                    ),
                    const SizedBox(height: 6),
                    Stack(
                      children: [
                        Container(
                          height: 8,
                          width: double.infinity,
                          decoration: BoxDecoration(
                            color: Colors.grey[200],
                            borderRadius: BorderRadius.circular(5),
                          ),
                        ),
                        FractionallySizedBox(
                          widthFactor: percentage / 100,
                          child: Container(
                            height: 8,
                            decoration: BoxDecoration(
                              color: Theme.of(context).primaryColor,
                              borderRadius: BorderRadius.circular(5),
                            ),
                          ),
                        ),
                      ],
                    ),
                    Align(
                      alignment: Alignment.centerRight,
                      child: Text(
                        '${percentage.toStringAsFixed(1)}%',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey[600],
                        ),
                      ),
                    ),
                  ],
                ),
              );
            }),
          ],
        ),
      ),
    );
  }

  Widget _buildVendorBreakdown() {
    final vendorTotals = _calculateVendorTotals();
    final total = _calculateTotalExpenses();
    final sortedEntries = vendorTotals.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));
    final displayEntries = sortedEntries.take(10).toList();
    final hasMore = sortedEntries.length > 10;
    
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.business,
                      color: Theme.of(context).primaryColor,
                    ),
                    const SizedBox(width: 8),
                    const Text(
                      'Expenses by Vendor',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                if (hasMore)
                  TextButton(
                    onPressed: () {
                      _showFullBreakdown('Vendors', sortedEntries);
                    },
                    child: const Text('View All'),
                  ),
              ],
            ),
            const Divider(height: 24),
            ...displayEntries.map((entry) {
              final percentage = total > 0 ? (entry.value / total * 100) : 0;
              return Padding(
                padding: const EdgeInsets.only(bottom: 12.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                          child: Text(
                            entry.key,
                            style: const TextStyle(fontWeight: FontWeight.w500),
                          ),
                        ),
                        Text(
                          CurrencyFormatter.formatCurrency(entry.value),
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                      ],
                    ),
                    const SizedBox(height: 6),
                    Stack(
                      children: [
                        Container(
                          height: 8,
                          width: double.infinity,
                          decoration: BoxDecoration(
                            color: Colors.grey[200],
                            borderRadius: BorderRadius.circular(5),
                          ),
                        ),
                        FractionallySizedBox(
                          widthFactor: percentage / 100,
                          child: Container(
                            height: 8,
                            decoration: BoxDecoration(
                              color: Theme.of(context).primaryColor,
                              borderRadius: BorderRadius.circular(5),
                            ),
                          ),
                        ),
                      ],
                    ),
                    Align(
                      alignment: Alignment.centerRight,
                      child: Text(
                        '${percentage.toStringAsFixed(1)}%',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey[600],
                        ),
                      ),
                    ),
                  ],
                ),
              );
            }),
          ],
        ),
      ),
    );
  }

  void _showFullBreakdown(String title, List<MapEntry<String, double>> entries) {
    final total = entries.fold(0.0, (sum, entry) => sum + entry.value);
    
    showDialog(
      context: context,
      builder: (context) => Dialog(
        child: Container(
          constraints: const BoxConstraints(maxWidth: 500, maxHeight: 600),
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'All $title',
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.close),
                    onPressed: () => Navigator.of(context).pop(),
                  ),
                ],
              ),
              const Divider(),
              Expanded(
                child: ListView.builder(
                  itemCount: entries.length,
                  itemBuilder: (context, index) {
                    final entry = entries[index];
                    final percentage = total > 0 ? (entry.value / total * 100) : 0;
                    
                    return Padding(
                      padding: const EdgeInsets.symmetric(vertical: 8.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Expanded(
                                child: Text(
                                  entry.key,
                                  style: const TextStyle(fontWeight: FontWeight.w500),
                                ),
                              ),
                              Text(
                                CurrencyFormatter.formatCurrency(entry.value),
                                style: const TextStyle(fontWeight: FontWeight.bold),
                              ),
                            ],
                          ),
                          const SizedBox(height: 6),
                          Stack(
                            children: [
                              Container(
                                height: 8,
                                width: double.infinity,
                                decoration: BoxDecoration(
                                  color: Colors.grey[200],
                                  borderRadius: BorderRadius.circular(5),
                                ),
                              ),
                              FractionallySizedBox(
                                widthFactor: percentage / 100,
                                child: Container(
                                  height: 8,
                                  decoration: BoxDecoration(
                                    color: Theme.of(context).primaryColor,
                                    borderRadius: BorderRadius.circular(5),
                                  ),
                                ),
                              ),
                            ],
                          ),
                          Align(
                            alignment: Alignment.centerRight,
                            child: Text(
                              '${percentage.toStringAsFixed(1)}%',
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.grey[600],
                              ),
                            ),
                          ),
                        ],
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildExpensesList() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.receipt_long,
                      color: Theme.of(context).primaryColor,
                    ),
                    const SizedBox(width: 8),
                    const Text(
                      'Recent Expenses',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                TextButton(
                  onPressed: () {
                    Navigator.pushNamed(context, '/expenses');
                  },
                  child: const Text('View All'),
                ),
              ],
            ),
            const Divider(height: 24),
            ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: _expenses.length > 5 ? 5 : _expenses.length,
              separatorBuilder: (context, index) => const Divider(height: 1),
              itemBuilder: (context, index) {
                final expense = _expenses[index];
                final dateFormat = DateFormat('MMM d, yyyy');
                return ListTile(
                  contentPadding: EdgeInsets.zero,
                  leading: CircleAvatar(
                    backgroundColor: expense.categoryColor ?? Theme.of(context).primaryColor.withAlpha(51),
                    child: Icon(
                      Icons.receipt,
                      color: expense.categoryColor != null 
                          ? Colors.white 
                          : Theme.of(context).primaryColor,
                      size: 20,
                    ),
                  ),
                  title: Row(
                    children: [
                      Expanded(
                        child: Text(
                          expense.title,
                          style: const TextStyle(fontWeight: FontWeight.w500),
                        ),
                      ),
                      if (expense.receiptNumber != null) ...[
                        const SizedBox(width: 8),
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                          decoration: BoxDecoration(
                            color: Theme.of(context).primaryColor.withAlpha(26), // 0.1 opacity = 26 in alpha (255 * 0.1)
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Text(
                            'Receipt #${expense.receiptNumber}',
                            style: TextStyle(
                              fontSize: 12,
                              color: Theme.of(context).primaryColor,
                            ),
                          ),
                        ),
                      ],
                    ],
                  ),
                  subtitle: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '${expense.categoryName ?? 'Uncategorized'} • ${expense.propertyName ?? 'No Property'}',
                      ),
                      Row(
                        children: [
                          Text(
                            dateFormat.format(expense.date),
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey[600],
                            ),
                          ),
                          if (expense.vendorName != null) ...[
                            Text(
                              ' • ',
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.grey[600],
                              ),
                            ),
                            Text(
                              expense.vendorName!,
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.grey[600],
                              ),
                            ),
                          ],
                        ],
                      ),
                    ],
                  ),
                  trailing: Text(
                    CurrencyFormatter.formatCurrency(expense.amount),
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  onTap: () {
                    if (expense.id != null) {
                      Navigator.pushNamed(
                        context, 
                        '/expenses/detail',
                        arguments: expense.id,
                      );
                    }
                  },
                );
              },
            ),
            if (_expenses.length > 5)
              Padding(
                padding: const EdgeInsets.only(top: 16.0),
                child: Center(
                  child: OutlinedButton(
                    onPressed: () {
                      Navigator.pushNamed(context, '/expenses');
                    },
                    child: const Text('See All Expenses'),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
} 