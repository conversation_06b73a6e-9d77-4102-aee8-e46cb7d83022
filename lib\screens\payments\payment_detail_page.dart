import 'package:flutter/material.dart';
import '../../models/bill/bill.dart';
import '../../models/payment/payment_model.dart';
import '../../models/tenant/tenant.dart';
import '../../services/service_locator.dart';
import '../../utils/currency_formatter.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

class PaymentDetailPage extends StatefulWidget {
  final String paymentId;

  const PaymentDetailPage({super.key, required this.paymentId});

  @override
  State<PaymentDetailPage> createState() => _PaymentDetailPageState();
}

class _PaymentDetailPageState extends State<PaymentDetailPage> {
  bool _isLoading = true;
  Payment? _payment;
  Tenant? _tenant;
  List<Bill> _bills = [];
  bool _isVerifying = false;
  String? _roomName;
  String? _propertyName;
  String? _propertyAddress;
  String? _verifierName;

  @override
  void initState() {
    super.initState();
    _loadPaymentDetails();
  }

  Future<void> _loadPaymentDetails() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Load payment details
      final payment = await serviceLocator.paymentService.getPayment(widget.paymentId);
      
      // Load tenant details if available
      Tenant? tenant;
      String? roomName;
      String? propertyName;
      String? propertyAddress;
      String? verifierName;
      
      if (payment.tenantId != null) {
        tenant = await serviceLocator.tenantService.getTenantById(payment.tenantId!);
        
        // Load room and property details if tenant has a room
        if (tenant?.roomId != null) {
          try {
            final room = await serviceLocator.roomService.getRoomById(tenant!.roomId!);
            if (room != null) {
              roomName = room.name;
              
              // Load property details
              final property = await serviceLocator.propertyService.getPropertyById(room.propertyId);
              if (property != null) {
                propertyName = property.name;
                propertyAddress = '${property.address}, ${property.city}, ${property.state} ${property.zipCode}';
              }
            }
          } catch (e) {
            // Error loading room/property details - continue without them
          }
        }
      }
      
      // Load verifier details if available
      if (payment.verifiedBy != null) {
        try {
          verifierName = await _getUserFullName(payment.verifiedBy!);
        } catch (e) {
          // Error loading verifier details - continue without them
        }
      }
      
      // Load bill details
      final bills = <Bill>[];
      for (final billId in payment.billIds) {
        try {
          final bill = await serviceLocator.billService.getBill(billId);
          bills.add(bill);
        } catch (e) {
          // Skip bills that can't be loaded
        }
      }
      
      if (mounted) {
        setState(() {
          _payment = payment;
          _tenant = tenant;
          _bills = bills;
          _roomName = roomName;
          _propertyName = propertyName;
          _propertyAddress = propertyAddress;
          _verifierName = verifierName;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading payment details: $e')),
        );
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<String> _getUserFullName(String userId) async {
    try {
      // Query the profiles table to get the user's profile
      final response = await Supabase.instance.client
          .from('profiles')
          .select('full_name')
          .eq('id', userId)
          .single();
      
      final fullName = response['full_name'] as String?;
      if (fullName != null && fullName.isNotEmpty) {
        return fullName;
      }
      
      // If no full name is found, return a formatted version of the ID
      return 'User $userId';
    } catch (e) {
      // If there's an error, return a formatted version of the ID
      return 'User $userId';
    }
  }

  Future<void> _showConfirmationDialog(PaymentStatus status) async {
    final result = await showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(
            status == PaymentStatus.verified ? 'Approve Payment' : 'Reject Payment',
          ),
          content: Text(
            status == PaymentStatus.verified
                ? 'Are you sure you want to approve this payment?'
                : 'Are you sure you want to reject this payment?'
          ),
          actions: <Widget>[
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: Text(status == PaymentStatus.verified ? 'Approve' : 'Reject'),
            ),
          ],
        );
      },
    );

    if (result == true) {
      await _updatePaymentStatus(status);
    }
  }

  Future<void> _updatePaymentStatus(PaymentStatus status) async {
    if (_payment == null) return;
    
    setState(() {
      _isVerifying = true;
    });

    try {
      final userId = serviceLocator.authService.currentUser?.id;
      final notes = status == PaymentStatus.verified
          ? 'Payment verified'
          : 'Payment rejected';
      
      await serviceLocator.paymentService.updatePaymentStatus(
        _payment!.id,
        status,
        verifiedBy: userId,
        notes: notes,
      );
      
      await _loadPaymentDetails();
      
      if (!mounted) return;
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            status == PaymentStatus.verified
                ? 'Payment verified successfully'
                : 'Payment rejected',
          ),
        ),
      );
    } catch (e) {
      if (!mounted) return;
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error updating payment status: $e')),
      );
    } finally {
      if (mounted) {
        setState(() {
          _isVerifying = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Scaffold(
      backgroundColor: theme.colorScheme.surfaceContainerLowest,
      appBar: AppBar(
        title: const Text('Payment Details'),
        elevation: 0,
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _payment == null
              ? const Center(child: Text('Payment not found'))
              : Column(
                  children: [
                    // Top section with payment status and amount
                    Container(
                      width: double.infinity,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: [
                            _getStatusColor(_payment!.status).withAlpha(26),
                            _getStatusColor(_payment!.status).withAlpha(13),
                          ],
                        ),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(24.0),
                        child: Column(
                          children: [
                            // Status indicator
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                              decoration: BoxDecoration(
                                color: _getStatusColor(_payment!.status).withAlpha(128),
                                borderRadius: BorderRadius.circular(20),
                                border: Border.all(
                                  color: _getStatusColor(_payment!.status),
                                  width: 1.5,
                                ),
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Icon(
                                    _getStatusIcon(_payment!.status),
                                    color: _getStatusColor(_payment!.status),
                                    size: 18,
                                  ),
                                  const SizedBox(width: 8),
                                  Text(
                                    _getStatusName(_payment!.status),
                                    style: TextStyle(
                                      color: Colors.black,
                                      fontWeight: FontWeight.bold,
                                      fontSize: 14,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            const SizedBox(height: 16),
                            // Amount
                            Text(
                              CurrencyFormatter.formatCurrency(_payment!.amount),
                              style: theme.textTheme.headlineLarge?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: theme.colorScheme.onSurface,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'Payment Date: ${_formatDate(_payment!.paymentDate)}',
                              style: theme.textTheme.bodyLarge?.copyWith(
                                color: theme.colorScheme.onSurfaceVariant,
                              ),
                            ),
                            if (_isVerifying) ...
                              [
                                const SizedBox(height: 16),
                                const LinearProgressIndicator(),
                              ],
                          ],
                        ),
                      ),
                    ),
                    
                    // Action buttons for pending payments
                    if (_payment!.status == PaymentStatus.pending && !_isVerifying)
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(16.0),
                        child: Row(
                          children: [
                            Expanded(
                              child: ElevatedButton.icon(
                                onPressed: () => _showConfirmationDialog(PaymentStatus.rejected),
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: Colors.red.shade50,
                                  foregroundColor: Colors.red.shade700,
                                  elevation: 0,
                                  padding: const EdgeInsets.symmetric(vertical: 16),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(12),
                                    side: BorderSide(color: Colors.red.shade200),
                                  ),
                                ),
                                icon: const Icon(Icons.close, size: 20),
                                label: const Text(
                                  'Reject',
                                  style: TextStyle(fontWeight: FontWeight.bold),
                                ),
                              ),
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: ElevatedButton.icon(
                                onPressed: () => _showConfirmationDialog(PaymentStatus.verified),
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: Colors.green.shade50,
                                  foregroundColor: Colors.green.shade700,
                                  elevation: 0,
                                  padding: const EdgeInsets.symmetric(vertical: 16),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(12),
                                    side: BorderSide(color: Colors.green.shade200),
                                  ),
                                ),
                                icon: const Icon(Icons.check, size: 20),
                                label: const Text(
                                  'Approve',
                                  style: TextStyle(fontWeight: FontWeight.bold),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    
                    // Scrollable content
                    Expanded(
                      child: SingleChildScrollView(
                        padding: const EdgeInsets.fromLTRB(16.0, 16.0, 16.0, 80.0), // Added bottom padding
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Payment details card
                            _buildModernCard(
                              title: 'Payment Information',
                              icon: Icons.payment,
                              children: [
                                _buildDetailTile(
                                  icon: Icons.account_balance_wallet,
                                  label: 'Payment Method',
                                  value: _getPaymentMethodName(_payment!.method),
                                ),
                                if (_payment!.receiptReference != null)
                                  _buildDetailTile(
                                    icon: Icons.receipt,
                                    label: 'Reference',
                                    value: _payment!.receiptReference!,
                                  ),
                                if (_payment!.notes != null && _payment!.notes!.isNotEmpty)
                                  _buildDetailTile(
                                    icon: Icons.note,
                                    label: 'Notes',
                                    value: _payment!.notes!,
                                  ),
                                _buildDetailTile(
                                  icon: Icons.schedule,
                                  label: 'Created',
                                  value: _formatDateTime(_payment!.createdAt),
                                ),
                                if (_payment!.updatedAt != null)
                                  _buildDetailTile(
                                    icon: Icons.update,
                                    label: 'Last Updated',
                                    value: _formatDateTime(_payment!.updatedAt!),
                                  ),
                                if (_payment!.verifiedBy != null)
                                  _buildDetailTile(
                                    icon: Icons.verified_user,
                                    label: 'Verified By',
                                    value: _verifierName ?? _payment!.verifiedBy!,
                                  ),
                              ],
                            ),
                      
                            // Tenant information
                            if (_tenant != null)
                              _buildModernCard(
                                title: 'Tenant Information',
                                icon: Icons.person,
                                children: [
                                  // Tenant basic info with avatar
                                  Container(
                                    padding: const EdgeInsets.all(16),
                                    decoration: BoxDecoration(
                                      color: theme.colorScheme.primaryContainer.withAlpha(77),
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                    child: Row(
                                      children: [
                                        CircleAvatar(
                                          backgroundColor: theme.colorScheme.primary,
                                          foregroundColor: theme.colorScheme.onPrimary,
                                          radius: 28,
                                          child: Text(
                                            '${_tenant!.firstName[0]}${_tenant!.lastName[0]}',
                                            style: const TextStyle(
                                              fontWeight: FontWeight.bold,
                                              fontSize: 18,
                                            ),
                                          ),
                                        ),
                                        const SizedBox(width: 16),
                                        Expanded(
                                          child: Column(
                                            crossAxisAlignment: CrossAxisAlignment.start,
                                            children: [
                                              Text(
                                                '${_tenant!.firstName} ${_tenant!.lastName}',
                                                style: theme.textTheme.titleLarge?.copyWith(
                                                  fontWeight: FontWeight.bold,
                                                ),
                                              ),
                                              const SizedBox(height: 4),
                                              Text(
                                                _tenant!.email,
                                                style: theme.textTheme.bodyMedium?.copyWith(
                                                  color: theme.colorScheme.onSurfaceVariant,
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  const SizedBox(height: 16),
                                  
                                  // Contact Information
                                  if (_tenant!.phoneNumber != null && _tenant!.phoneNumber!.isNotEmpty)
                                    _buildDetailTile(
                                      icon: Icons.phone,
                                      label: 'Phone',
                                      value: _tenant!.phoneNumber!,
                                    ),
                                  
                                  // Emergency Contact
                                  if (_tenant!.emergencyContactName != null && _tenant!.emergencyContactName!.isNotEmpty)
                                    _buildDetailTile(
                                      icon: Icons.contact_emergency,
                                      label: 'Emergency Contact',
                                      value: _tenant!.emergencyContactName!,
                                    ),
                                  
                                  if (_tenant!.emergencyContactPhone != null && _tenant!.emergencyContactPhone!.isNotEmpty)
                                    _buildDetailTile(
                                      icon: Icons.phone_forwarded,
                                      label: 'Emergency Phone',
                                      value: _tenant!.emergencyContactPhone!,
                                    ),
                                  
                                  // Property Information
                                  if (_propertyName != null)
                                    _buildDetailTile(
                                      icon: Icons.home,
                                      label: 'Property',
                                      value: _propertyName!,
                                    ),
                                  
                                  // Room/House Number
                                  if (_roomName != null)
                                    _buildDetailTile(
                                      icon: Icons.meeting_room,
                                      label: 'Room/House',
                                      value: _roomName!,
                                    ),
                                  
                                  // Property Address
                                  if (_propertyAddress != null)
                                    _buildDetailTile(
                                      icon: Icons.location_on,
                                      label: 'Address',
                                      value: _propertyAddress!,
                                    ),
                                  
                                  // Tenant Status
                                  Container(
                                    margin: const EdgeInsets.only(top: 8),
                                    child: Row(
                                      children: [
                                        Icon(
                                          Icons.info,
                                          size: 18,
                                          color: theme.colorScheme.onSurfaceVariant,
                                        ),
                                        const SizedBox(width: 12),
                                        Expanded(
                                          flex: 2,
                                          child: Text(
                                            'Status',
                                            style: theme.textTheme.bodyMedium?.copyWith(
                                              color: theme.colorScheme.onSurfaceVariant,
                                            ),
                                          ),
                                        ),
                                        Expanded(
                                          flex: 3,
                                          child: Container(
                                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                            decoration: BoxDecoration(
                                              color: _getTenantStatusColor(_tenant!.status).withAlpha(26),
                                              borderRadius: BorderRadius.circular(12),
                                              border: Border.all(
                                                color: _getTenantStatusColor(_tenant!.status).withAlpha(128),
                                              ),
                                            ),
                                            child: Text(
                                              _getTenantStatusName(_tenant!.status),
                                              style: TextStyle(
                                                color: _getTenantStatusColor(_tenant!.status),
                                                fontWeight: FontWeight.bold,
                                                fontSize: 12,
                                              ),
                                              textAlign: TextAlign.center,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                      
                            // Bills paid
                            _buildModernCard(
                              title: 'Bills Paid',
                              icon: Icons.receipt_long,
                              children: [
                                if (_bills.isEmpty)
                                  Container(
                                    padding: const EdgeInsets.all(20),
                                    decoration: BoxDecoration(
                                      color: theme.colorScheme.surfaceContainerHighest,
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                    child: Row(
                                      mainAxisAlignment: MainAxisAlignment.center,
                                      children: [
                                        Icon(
                                          Icons.receipt_outlined,
                                          color: theme.colorScheme.onSurfaceVariant,
                                          size: 24,
                                        ),
                                        const SizedBox(width: 8),
                                        Text(
                                          'No bills found',
                                          style: theme.textTheme.bodyLarge?.copyWith(
                                            color: theme.colorScheme.onSurfaceVariant,
                                          ),
                                        ),
                                      ],
                                    ),
                                  )
                                else
                                  ..._bills.asMap().entries.map((entry) {
                                    final index = entry.key;
                                    final bill = entry.value;
                                    return Container(
                                      margin: EdgeInsets.only(
                                        bottom: index == _bills.length - 1 ? 0 : 12,
                                      ),
                                      padding: const EdgeInsets.all(16),
                                      decoration: BoxDecoration(
                                        color: theme.colorScheme.surfaceContainerHighest,
                                        borderRadius: BorderRadius.circular(12),
                                        border: Border.all(
                                          color: theme.colorScheme.outline.withAlpha(77),
                                        ),
                                      ),
                                      child: Column(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          Row(
                                            children: [
                                              Expanded(
                                                child: Text(
                                                  bill.title,
                                                  style: theme.textTheme.titleSmall?.copyWith(
                                                    fontWeight: FontWeight.bold,
                                                  ),
                                                ),
                                              ),
                                              Container(
                                                padding: const EdgeInsets.symmetric(
                                                  horizontal: 8,
                                                  vertical: 4,
                                                ),
                                                decoration: BoxDecoration(
                                                  color: theme.colorScheme.primary.withAlpha(26),
                                                  borderRadius: BorderRadius.circular(6),
                                                ),
                                                child: Text(
                                                  CurrencyFormatter.formatCurrency(bill.amount),
                                                  style: theme.textTheme.labelMedium?.copyWith(
                                                    color: theme.colorScheme.primary,
                                                    fontWeight: FontWeight.bold,
                                                  ),
                                                ),
                                              ),
                                            ],
                                          ),
                                          const SizedBox(height: 8),
                                          Text(
                                            bill.description,
                                            style: theme.textTheme.bodyMedium?.copyWith(
                                              color: theme.colorScheme.onSurfaceVariant,
                                            ),
                                          ),
                                          const SizedBox(height: 8),
                                          Row(
                                            children: [
                                              Icon(
                                                Icons.schedule,
                                                size: 16,
                                                color: theme.colorScheme.onSurfaceVariant,
                                              ),
                                              const SizedBox(width: 4),
                                              Text(
                                                'Due: ${_formatDate(bill.dueDate)}',
                                                style: theme.textTheme.bodySmall?.copyWith(
                                                  color: theme.colorScheme.onSurfaceVariant,
                                                ),
                                              ),
                                            ],
                                          ),
                                        ],
                                      ),
                                    );
                                  }),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
    );
  }
  
  Widget _buildModernCard({
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    final theme = Theme.of(context);
    
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: theme.colorScheme.shadow.withAlpha(13),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: theme.colorScheme.primaryContainer,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    icon,
                    color: theme.colorScheme.onPrimaryContainer,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  title,
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ...children,
          ],
        ),
      ),
    );
  }
  
  Widget _buildDetailTile({
    required IconData icon,
    required String label,
    required String value,
  }) {
    final theme = Theme.of(context);
    
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Icon(
            icon,
            size: 18,
            color: theme.colorScheme.onSurfaceVariant,
          ),
          const SizedBox(width: 12),
          Expanded(
            flex: 2,
            child: Text(
              label,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ),
          ),
          Expanded(
            flex: 3,
            child: Text(
              value,
              style: theme.textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }


  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  String _getStatusName(PaymentStatus status) {
    switch (status) {
      case PaymentStatus.pending:
        return 'Pending Verification';
      case PaymentStatus.verified:
        return 'Verified';
      case PaymentStatus.rejected:
        return 'Rejected';
    }
  }

  IconData _getStatusIcon(PaymentStatus status) {
    switch (status) {
      case PaymentStatus.pending:
        return Icons.pending;
      case PaymentStatus.verified:
        return Icons.check_circle;
      case PaymentStatus.rejected:
        return Icons.cancel;
    }
  }

  Color _getStatusColor(PaymentStatus status) {
    switch (status) {
      case PaymentStatus.pending:
        return Colors.orange;
      case PaymentStatus.verified:
        return Colors.green;
      case PaymentStatus.rejected:
        return Colors.red;
    }
  }

  String _getPaymentMethodName(PaymentMethod method) {
    switch (method) {
      case PaymentMethod.cash:
        return 'Cash';
      case PaymentMethod.bankTransfer:
        return 'Bank Transfer';
      case PaymentMethod.mobileMoney:
        return 'Mobile Money';
      case PaymentMethod.check:
        return 'Check';
      case PaymentMethod.other:
        return 'Other';
    }
  }
  
  String _getTenantStatusName(TenantStatus status) {
    switch (status) {
      case TenantStatus.active:
        return 'Active';
      case TenantStatus.pending:
        return 'Pending';
      case TenantStatus.movedOut:
        return 'Moved Out';
    }
  }
  
  Color _getTenantStatusColor(TenantStatus status) {
    switch (status) {
      case TenantStatus.active:
        return Colors.green;
      case TenantStatus.pending:
        return Colors.orange;
      case TenantStatus.movedOut:
        return Colors.grey;
    }
  }
}
