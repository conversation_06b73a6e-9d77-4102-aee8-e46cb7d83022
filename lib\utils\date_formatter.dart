class DateFormatter {
  /// Formats a DateTime into a user-friendly relative time string
  /// Examples: "Today", "1 week ago", "2 months ago", "1 year ago"
  static String formatRelativeTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    // If it's today
    if (difference.inDays == 0) {
      return 'Today';
    }

    // If it's yesterday
    if (difference.inDays == 1) {
      return 'Yesterday';
    }

    // Less than a week (2-6 days)
    if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    }

    // Calculate actual months difference for more accuracy
    final monthsDiff = _calculateMonthsDifference(dateTime, now);

    // Less than 2 months (show in weeks)
    if (monthsDiff < 2) {
      final weeks = (difference.inDays / 7).floor();
      return weeks == 1 ? '1 week ago' : '$weeks weeks ago';
    }

    // Less than a year (show in months)
    if (monthsDiff < 12) {
      return monthsDiff == 1 ? '1 month ago' : '$monthsDiff months ago';
    }

    // More than a year
    final years = (monthsDiff / 12).floor();
    return years == 1 ? '1 year ago' : '$years years ago';
  }

  /// Calculate the difference in months between two dates
  static int _calculateMonthsDifference(DateTime startDate, DateTime endDate) {
    int months = (endDate.year - startDate.year) * 12;
    months += endDate.month - startDate.month;

    // If the day hasn't been reached yet in the current month, subtract 1
    if (endDate.day < startDate.day) {
      months--;
    }

    return months;
  }

  /// Formats a DateTime into a simple date string (dd/mm/yyyy)
  static String formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  /// Formats a DateTime into a more detailed relative time for activity logs
  /// Examples: "Just now", "5m ago", "2h ago", "3d ago"
  static String formatActivityTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }
}
