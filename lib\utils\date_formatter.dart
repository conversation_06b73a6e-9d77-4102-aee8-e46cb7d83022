class DateFormatter {
  /// Formats a DateTime into a user-friendly relative time string
  /// Examples: "Today", "1 week ago", "2 months ago", "1 year ago"
  static String formatRelativeTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);
    
    // If it's today
    if (difference.inDays == 0) {
      return 'Today';
    }
    
    // If it's yesterday
    if (difference.inDays == 1) {
      return 'Yesterday';
    }
    
    // Less than a week (2-6 days)
    if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    }
    
    // Less than a month (1-4 weeks)
    if (difference.inDays < 30) {
      final weeks = (difference.inDays / 7).floor();
      return weeks == 1 ? '1 week ago' : '$weeks weeks ago';
    }
    
    // Less than a year (1-11 months)
    if (difference.inDays < 365) {
      final months = (difference.inDays / 30).floor();
      return months == 1 ? '1 month ago' : '$months months ago';
    }
    
    // More than a year
    final years = (difference.inDays / 365).floor();
    return years == 1 ? '1 year ago' : '$years years ago';
  }
  
  /// Formats a DateTime into a simple date string (dd/mm/yyyy)
  static String formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
  
  /// Formats a DateTime into a more detailed relative time for activity logs
  /// Examples: "Just now", "5m ago", "2h ago", "3d ago"
  static String formatActivityTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }
}
