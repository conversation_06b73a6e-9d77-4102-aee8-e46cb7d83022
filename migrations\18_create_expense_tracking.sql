-- Create expense categories table
CREATE TABLE IF NOT EXISTS expense_categories (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  description TEXT,
  color TEXT NOT NULL,
  parent_category_id UUID REFERENCES expense_categories(id),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  is_default BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE
);

-- Create indexes for foreign keys in expense_categories
CREATE INDEX IF NOT EXISTS idx_expense_categories_parent_category_id ON expense_categories(parent_category_id);
CREATE INDEX IF NOT EXISTS idx_expense_categories_user_id ON expense_categories(user_id);

-- Add RLS policies for expense_categories
ALTER TABLE expense_categories ENABLE ROW LEVEL SECURITY;

DROP POLICY IF EXISTS expense_categories_select_policy ON expense_categories;
CREATE POLICY expense_categories_select_policy ON expense_categories
  FOR SELECT
  USING (user_id = (SELECT auth.uid()));

DROP POLICY IF EXISTS expense_categories_insert_policy ON expense_categories;
CREATE POLICY expense_categories_insert_policy ON expense_categories
  FOR INSERT
  WITH CHECK (user_id = (SELECT auth.uid()));

DROP POLICY IF EXISTS expense_categories_update_policy ON expense_categories;
CREATE POLICY expense_categories_update_policy ON expense_categories
  FOR UPDATE
  USING (user_id = (SELECT auth.uid()));

DROP POLICY IF EXISTS expense_categories_delete_policy ON expense_categories;
CREATE POLICY expense_categories_delete_policy ON expense_categories
  FOR DELETE
  USING (user_id = (SELECT auth.uid()));

-- Create function to insert default expense categories for new users
CREATE OR REPLACE FUNCTION create_default_expense_categories() 
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  categories TEXT[] := ARRAY[
    'Maintenance|Routine repairs and maintenance expenses|#FF5733',
    'Utilities|Water, electricity, gas, and other utility bills|#33A1FF',
    'Taxes|Property taxes and other tax-related expenses|#FF33A8',
    'Insurance|Property and liability insurance costs|#33FF57',
    'Renovations|Major property improvements and renovations|#A833FF',
    'Cleaning|Professional cleaning services and supplies|#FFD433',
    'Landscaping|Lawn care, gardening, and outdoor maintenance|#33FFD4',
    'Administrative|Office supplies, software, and management fees|#FF8B33',
    'Legal|Legal fees and consultation expenses|#3357FF',
    'Miscellaneous|Other expenses that don''t fit in other categories|#B0B0B0'
  ];
  cat_data TEXT[];
  cat_name TEXT;
  cat_desc TEXT;
  cat_color TEXT;
BEGIN
  -- Set search_path to empty to prevent search path injection
  PERFORM set_config('search_path', '', false);
  
  FOREACH cat_data SLICE 1 IN ARRAY categories
  LOOP
    cat_name := split_part(cat_data[1], '|', 1);
    cat_desc := split_part(cat_data[1], '|', 2);
    cat_color := split_part(cat_data[1], '|', 3);
    
    INSERT INTO public.expense_categories (name, description, color, user_id, is_default)
    VALUES (cat_name, cat_desc, cat_color, NEW.id, TRUE);
  END LOOP;
  
  RETURN NEW;
END;
$$;

-- Create trigger to add default expense categories when a new user signs up
DROP TRIGGER IF EXISTS create_default_expense_categories_trigger ON auth.users;
CREATE TRIGGER create_default_expense_categories_trigger
AFTER INSERT ON auth.users
FOR EACH ROW
EXECUTE FUNCTION create_default_expense_categories();

-- Create vendors table
CREATE TABLE IF NOT EXISTS vendors (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  description TEXT,
  contact_person TEXT,
  phone TEXT,
  email TEXT,
  address TEXT,
  website TEXT,
  category_id UUID REFERENCES expense_categories(id),
  is_preferred BOOLEAN DEFAULT FALSE,
  rating DECIMAL(3,1),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE
);

-- Create indexes for foreign keys in vendors
CREATE INDEX IF NOT EXISTS idx_vendors_category_id ON vendors(category_id);
CREATE INDEX IF NOT EXISTS idx_vendors_user_id ON vendors(user_id);

-- Add RLS policies for vendors
ALTER TABLE vendors ENABLE ROW LEVEL SECURITY;

DROP POLICY IF EXISTS vendors_select_policy ON vendors;
CREATE POLICY vendors_select_policy ON vendors
  FOR SELECT
  USING (user_id = (SELECT auth.uid()));

DROP POLICY IF EXISTS vendors_insert_policy ON vendors;
CREATE POLICY vendors_insert_policy ON vendors
  FOR INSERT
  WITH CHECK (user_id = (SELECT auth.uid()));

DROP POLICY IF EXISTS vendors_update_policy ON vendors;
CREATE POLICY vendors_update_policy ON vendors
  FOR UPDATE
  USING (user_id = (SELECT auth.uid()));

DROP POLICY IF EXISTS vendors_delete_policy ON vendors;
CREATE POLICY vendors_delete_policy ON vendors
  FOR DELETE
  USING (user_id = (SELECT auth.uid()));

-- Create sequence for expense numbers starting from 1000001
-- This sequence will always increment and never reuse deleted numbers
CREATE SEQUENCE IF NOT EXISTS expense_number_seq START WITH 1000001 NO CYCLE;

-- Check if expenses table exists
DO $$
BEGIN
  IF EXISTS (
    SELECT FROM information_schema.tables 
    WHERE table_name = 'expenses'
  ) THEN
    -- Table exists, check if expense_number column exists
    IF NOT EXISTS (
      SELECT FROM information_schema.columns 
      WHERE table_name = 'expenses' AND column_name = 'expense_number'
    ) THEN
      -- Add expense_number column to existing table
      ALTER TABLE expenses ADD COLUMN expense_number INTEGER;
      
      -- Update existing rows with sequential numbers
      WITH numbered_expenses AS (
        SELECT id, ROW_NUMBER() OVER (ORDER BY created_at) + 1000000 AS row_num
        FROM expenses
      )
      UPDATE expenses e
      SET expense_number = ne.row_num
      FROM numbered_expenses ne
      WHERE e.id = ne.id;
      
      -- Make the column NOT NULL after populating it
      ALTER TABLE expenses ALTER COLUMN expense_number SET NOT NULL;
      
      -- Set default for new rows
      ALTER TABLE expenses ALTER COLUMN expense_number SET DEFAULT nextval('expense_number_seq');
    END IF;

    -- Check if receipt_number column exists
    IF NOT EXISTS (
      SELECT FROM information_schema.columns 
      WHERE table_name = 'expenses' AND column_name = 'receipt_number'
    ) THEN
      -- Add receipt_number column to existing table
      ALTER TABLE expenses ADD COLUMN receipt_number TEXT;
      
      -- Create index for receipt_number
      CREATE INDEX IF NOT EXISTS idx_expenses_receipt_number ON expenses(receipt_number);
      
      -- Add comment to describe the receipt_number column
      COMMENT ON COLUMN expenses.receipt_number IS 'Stores the receipt or invoice number for the expense';
    END IF;

    -- Check if vendor_name column exists
    IF NOT EXISTS (
      SELECT FROM information_schema.columns 
      WHERE table_name = 'expenses' AND column_name = 'vendor_name'
    ) THEN
      -- Add vendor_name column to existing table
      ALTER TABLE expenses ADD COLUMN vendor_name TEXT;
      
      -- Create index for vendor_name
      CREATE INDEX IF NOT EXISTS idx_expenses_vendor_name ON expenses(vendor_name);
      
      -- Add comment to describe the vendor_name column
      COMMENT ON COLUMN expenses.vendor_name IS 'Stores the name of the vendor for quick access';

      -- Update existing vendor names from vendors table
      UPDATE expenses e
      SET vendor_name = v.name
      FROM vendors v
      WHERE e.vendor_id = v.id;
    END IF;
  ELSE
    -- Create expenses table if it doesn't exist
    CREATE TABLE expenses (
      id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
      expense_number INTEGER NOT NULL DEFAULT nextval('expense_number_seq'),
      title TEXT NOT NULL,
      description TEXT,
      amount DECIMAL(15,2) NOT NULL,
      date TIMESTAMP WITH TIME ZONE NOT NULL,
      category_id UUID NOT NULL REFERENCES expense_categories(id),
      property_id UUID REFERENCES properties(id),
      room_id UUID REFERENCES rooms(id),
      vendor_id UUID REFERENCES vendors(id),
      vendor_name TEXT,
      frequency TEXT,
      next_due_date TIMESTAMP WITH TIME ZONE,
      end_date TIMESTAMP WITH TIME ZONE,
      occurrences INTEGER,
      occurrences_completed INTEGER DEFAULT 0,
      is_recurring BOOLEAN DEFAULT FALSE,
      receipt_url TEXT,
      receipt_number TEXT,
      user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      updated_at TIMESTAMP WITH TIME ZONE
    );
  END IF;
END $$;

-- Create indexes for foreign keys in expenses
CREATE INDEX IF NOT EXISTS idx_expenses_category_id ON expenses(category_id);
CREATE INDEX IF NOT EXISTS idx_expenses_property_id ON expenses(property_id);
CREATE INDEX IF NOT EXISTS idx_expenses_room_id ON expenses(room_id);
CREATE INDEX IF NOT EXISTS idx_expenses_vendor_id ON expenses(vendor_id);
CREATE INDEX IF NOT EXISTS idx_expenses_user_id ON expenses(user_id);
CREATE INDEX IF NOT EXISTS idx_expenses_expense_number ON expenses(expense_number);
CREATE INDEX IF NOT EXISTS idx_expenses_receipt_number ON expenses(receipt_number);
CREATE INDEX IF NOT EXISTS idx_expenses_vendor_name ON expenses(vendor_name);

-- Create trigger to update vendor_name when vendor_id changes
CREATE OR REPLACE FUNCTION update_expense_vendor_name()
RETURNS TRIGGER
SECURITY DEFINER
LANGUAGE plpgsql
AS $$
BEGIN
  -- Set search_path to empty to prevent search path injection
  PERFORM set_config('search_path', '', false);
  
  IF NEW.vendor_id IS NOT NULL THEN
    SELECT name INTO NEW.vendor_name
    FROM public.vendors
    WHERE id = NEW.vendor_id;
  END IF;
  RETURN NEW;
END;
$$;

DROP TRIGGER IF EXISTS update_expense_vendor_name_trigger ON expenses;
CREATE TRIGGER update_expense_vendor_name_trigger
BEFORE INSERT OR UPDATE OF vendor_id ON expenses
FOR EACH ROW
EXECUTE FUNCTION update_expense_vendor_name();

-- Add foreign key constraints for proper relationships
DO $$
BEGIN
  -- Add foreign key constraint for expenses -> properties if it doesn't exist
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.table_constraints 
    WHERE constraint_name = 'expenses_property_id_fkey' 
    AND table_name = 'expenses'
  ) THEN
    -- Clean up any invalid property_id references first
    UPDATE expenses 
    SET property_id = NULL 
    WHERE property_id IS NOT NULL 
      AND property_id NOT IN (SELECT id FROM properties);
    
    -- Add the foreign key constraint
    ALTER TABLE expenses 
    ADD CONSTRAINT expenses_property_id_fkey 
    FOREIGN KEY (property_id) REFERENCES properties(id) ON DELETE SET NULL;
  END IF;

  -- Add foreign key constraint for expenses -> rooms if it doesn't exist
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.table_constraints 
    WHERE constraint_name = 'expenses_room_id_fkey' 
    AND table_name = 'expenses'
  ) THEN
    -- Clean up any invalid room_id references first
    UPDATE expenses 
    SET room_id = NULL 
    WHERE room_id IS NOT NULL 
      AND room_id NOT IN (SELECT id FROM rooms);
    
    -- Add the foreign key constraint
    ALTER TABLE expenses 
    ADD CONSTRAINT expenses_room_id_fkey 
    FOREIGN KEY (room_id) REFERENCES rooms(id) ON DELETE SET NULL;
  END IF;
END $$;

-- Add RLS policies for expenses table
ALTER TABLE expenses ENABLE ROW LEVEL SECURITY;

DROP POLICY IF EXISTS expenses_select_policy ON expenses;
CREATE POLICY expenses_select_policy ON expenses
  FOR SELECT
  USING (user_id = (SELECT auth.uid()));

DROP POLICY IF EXISTS expenses_insert_policy ON expenses;
CREATE POLICY expenses_insert_policy ON expenses
  FOR INSERT
  WITH CHECK (user_id = (SELECT auth.uid()));

DROP POLICY IF EXISTS expenses_update_policy ON expenses;
CREATE POLICY expenses_update_policy ON expenses
  FOR UPDATE
  USING (user_id = (SELECT auth.uid()));

DROP POLICY IF EXISTS expenses_delete_policy ON expenses;
CREATE POLICY expenses_delete_policy ON expenses
  FOR DELETE
  USING (user_id = (SELECT auth.uid()));  

-- Create comprehensive view for expense reporting
-- Security for this view is handled by RLS on the underlying tables
DROP VIEW IF EXISTS expense_summary;
CREATE VIEW expense_summary 
WITH (security_invoker = true) AS
SELECT 
    e.*,
    ec.name as category_name,
    ec.color as category_color,
    COALESCE(e.vendor_name, v.name) as vendor_name_resolved,
    p.name as property_name,
    r.name as room_name
FROM expenses e
    LEFT JOIN expense_categories ec ON e.category_id = ec.id
    LEFT JOIN vendors v ON e.vendor_id = v.id
    LEFT JOIN properties p ON e.property_id = p.id
    LEFT JOIN rooms r ON e.room_id = r.id;

-- Add comments for the security-enhanced functions
COMMENT ON FUNCTION create_default_expense_categories() IS 
'Creates default expense categories for new users. Uses explicit search_path handling to prevent search path injection attacks.';

COMMENT ON FUNCTION update_expense_vendor_name() IS 
'Updates the vendor name in expenses when vendor ID changes. Uses explicit search_path handling to prevent search path injection attacks.'; 