-- Add user_id column to tenants table to link with auth.users
ALTER TABLE public.tenants 
ADD COLUMN IF NOT EXISTS user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL;

-- Create index for better performance when searching by user_id
CREATE INDEX IF NOT EXISTS idx_tenants_user_id ON public.tenants(user_id);

-- Update the link_tenant_to_auth_user function to use the new user_id column
CREATE OR REPLACE FUNCTION public.link_tenant_to_auth_user()
RETURNS TRIGGER AS $$
DECLARE
    matching_user_id UUID;
BEGIN
    -- Set search_path to empty to prevent search path injection
    PERFORM set_config('search_path', '', false);
    
    -- Get matching user ID once to avoid multiple evaluations
    SELECT id INTO matching_user_id FROM auth.users WHERE email = NEW.email LIMIT 1;
    
    -- If the new tenant has a Supabase auth user with matching email
    -- Update the tenant record with the auth user's ID
    IF matching_user_id IS NOT NULL THEN
        UPDATE public.tenants
        SET user_id = matching_user_id
        WHERE id = NEW.id;
    END IF;
      
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Drop and recreate the trigger to ensure it uses the latest function version
DROP TRIGGER IF EXISTS on_tenant_created ON public.tenants;
CREATE TRIGGER on_tenant_created
    AFTER INSERT ON public.tenants
    FOR EACH ROW
    EXECUTE FUNCTION public.link_tenant_to_auth_user();

-- Create a function to get a tenant by user ID
CREATE OR REPLACE FUNCTION public.get_tenant_by_user_id(user_id_param UUID)
RETURNS SETOF public.tenants
LANGUAGE sql
SECURITY DEFINER
AS $$
    -- Set search_path to empty to prevent search path injection
    SELECT set_config('search_path', '', false);
    
    SELECT * FROM public.tenants
    WHERE user_id = user_id_param
    LIMIT 1;
$$;

-- Add comment for the new column
COMMENT ON COLUMN public.tenants.user_id IS 'Foreign key reference to auth.users';

-- Add comments for the security-enhanced functions
COMMENT ON FUNCTION public.link_tenant_to_auth_user() IS 
'Links a tenant record to an auth user with matching email. Uses explicit search_path handling to prevent search path injection attacks.';

COMMENT ON FUNCTION public.get_tenant_by_user_id(UUID) IS 
'Gets a tenant record by user ID. Uses explicit search_path handling to prevent search path injection attacks.'; 