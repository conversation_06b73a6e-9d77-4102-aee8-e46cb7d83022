import 'dart:async';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../models/room/room_model.dart';
import '../../models/room/amenity_model.dart';
import '../../models/room/room_type_model.dart';
import '../../models/activity_log.dart';
import '../../utils/logger.dart';

/// Service for managing rooms using Supabase
class RoomService {
  // Stream controller for room changes
  final _roomStreamController = StreamController<List<Room>>.broadcast();

  // Supabase client
  late final SupabaseClient _supabase;

  // Singleton instance
  static final RoomService _instance = RoomService._internal();

  // Factory constructor
  factory RoomService() {
    return _instance;
  }

  // Private constructor
  RoomService._internal() {
    _supabase = Supabase.instance.client;
    _refreshRooms();
  }

  /// Get stream of room updates
  Stream<List<Room>> get roomsStream => _roomStreamController.stream;

  /// Fetch all rooms
  Future<List<Room>> _fetchAllRooms() async {
    try {
      final response = await _supabase
          .from('rooms')
          .select('''
        *,
        room_amenities:room_amenities(
          *,
          amenity:amenities(*)
        )
      ''')
          .order('created_at');

      final List<dynamic> data = response;
      return data.map((roomData) => _mapToRoom(roomData)).toList();
    } catch (error) {
      AppLogger.error('Error fetching rooms', error);
      return [];
    }
  }

  /// Map Supabase response to Room model
  Room _mapToRoom(Map<String, dynamic> roomData) {
    // Extract room amenities from the joined data
    final List<dynamic> roomAmenitiesData = roomData['room_amenities'] ?? [];
    final roomAmenities =
        roomAmenitiesData.map((amenityData) {
          final amenityJson = amenityData['amenity'];

          return RoomAmenity(
            id: amenityData['id'],
            roomId:
                amenityData['room_id'] as String, // Cast to non-nullable String
            amenityId: amenityData['amenity_id'],
            customAmenityName: amenityData['custom_amenity_name'],
            amenity: amenityJson != null ? Amenity.fromJson(amenityJson) : null,
            createdAt:
                amenityData['created_at'] != null
                    ? DateTime.parse(amenityData['created_at'])
                    : null,
            updatedAt:
                amenityData['updated_at'] != null
                    ? DateTime.parse(amenityData['updated_at'])
                    : null,
          );
        }).toList();

    // Create room with amenities
    return Room(
      id: roomData['id'],
      name: roomData['name'],
      propertyId: roomData['property_id'],
      occupancyStatus: RoomOccupancyStatusExtension.fromString(
        roomData['occupancy_status'],
      ),
      roomTypeId: roomData['room_type_id'],
      customRoomType: roomData['custom_room_type'],
      rentalPrice: (roomData['rental_price'] as num).toDouble(),
      size:
          roomData['size'] != null
              ? (roomData['size'] as num).toDouble()
              : null,
      floor:
          roomData['floor'] != null ? (roomData['floor'] as num).toInt() : null,
      isFurnished: roomData['is_furnished'],
      amenities: roomAmenities,
      description: roomData['description'],
      imageUrl: roomData['image_url'],
      notes: roomData['notes'],
      additionalInfo: roomData['additional_info'],
      createdAt: DateTime.parse(roomData['created_at']),
      updatedAt: DateTime.parse(roomData['updated_at']),
    );
  }

  /// Manually refresh rooms data
  Future<void> _refreshRooms() async {
    final rooms = await _fetchAllRooms();
    if (!_roomStreamController.isClosed) {
      _roomStreamController.add(rooms);
    }
  }

  /// Get all rooms
  Future<List<Room>> getAllRooms() async {
    final rooms = await _fetchAllRooms();

    // Update the stream with fresh data
    if (!_roomStreamController.isClosed) {
      _roomStreamController.add(rooms);
    }

    return rooms;
  }

  /// Get a room by ID
  Future<Room?> getRoomById(String id) async {
    try {
      final response =
          await _supabase
              .from('rooms')
              .select('''
        *,
        room_amenities:room_amenities(
          *,
          amenity:amenities(*)
        )
      ''')
              .eq('id', id)
              .single();

      return _mapToRoom(response);
    } catch (error) {
      AppLogger.error('Error fetching room by ID', error);
      return null;
    }
  }

  /// Get rooms for a specific property
  Future<List<Room>> getRoomsByPropertyId(String propertyId) async {
    try {
      final response = await _supabase
          .from('rooms')
          .select('''
        *,
        room_amenities:room_amenities(
          *,
          amenity:amenities(*)
        )
      ''')
          .eq('property_id', propertyId)
          .order('created_at');

      final List<dynamic> data = response;
      return data.map((roomData) => _mapToRoom(roomData)).toList();
    } catch (error) {
      AppLogger.error('Error fetching rooms by property ID', error);
      return [];
    }
  }

  /// Get all room types
  Future<List<RoomType>> getAllRoomTypes() async {
    try {
      final response = await _supabase
          .from('room_types')
          .select()
          .order('name');

      final List<dynamic> data = response;
      return data.map((json) => RoomType.fromJson(json)).toList();
    } catch (error) {
      AppLogger.error('Error fetching room types', error);
      return [];
    }
  }

  /// Get all amenities
  Future<List<Amenity>> getAllAmenities() async {
    try {
      final response = await _supabase.from('amenities').select().order('name');

      final List<dynamic> data = response;
      return data.map((json) => Amenity.fromJson(json)).toList();
    } catch (error) {
      AppLogger.error('Error fetching amenities', error);
      return [];
    }
  }

  /// Add a new room
  Future<Room?> addRoom({
    required String name,
    required String propertyId,
    required RoomOccupancyStatus occupancyStatus,
    String? roomTypeId,
    String? customRoomType,
    required double rentalPrice,
    double? size,
    int? floor,
    required bool isFurnished,
    List<RoomAmenity>? amenities,
    String? description,
    String? imageUrl,
    String? notes,
    Map<String, dynamic>? additionalInfo,
  }) async {
    try {
      // Validate room type - either roomTypeId or customRoomType must be provided
      if (roomTypeId == null && customRoomType == null) {
        throw Exception(
          'Either room type ID or custom room type must be provided',
        );
      }

      if (roomTypeId != null && customRoomType != null) {
        throw Exception(
          'Only one of room type ID or custom room type can be provided',
        );
      }

      // Insert the room
      final response =
          await _supabase
              .from('rooms')
              .insert({
                'name': name,
                'property_id': propertyId,
                'occupancy_status': occupancyStatus.dbValue,
                'room_type_id': roomTypeId,
                'custom_room_type': customRoomType,
                'rental_price': rentalPrice,
                'size': size,
                'floor': floor,
                'is_furnished': isFurnished,
                'description': description,
                'image_url': imageUrl,
                'notes': notes,
                'additional_info': additionalInfo,
              })
              .select()
              .single();

      final roomId = response['id'];

      // Add amenities if provided
      if (amenities != null && amenities.isNotEmpty) {
        await _updateRoomAmenities(roomId, amenities);
      }

      // Refresh the rooms stream
      _refreshRooms();

      // Return the newly created room with amenities
      return await getRoomById(roomId);
    } catch (error) {
      AppLogger.error('Error adding room', error);
      throw Exception('Failed to add room: $error');
    }
  }

  /// Update room amenities
  Future<void> _updateRoomAmenities(
    String roomId,
    List<RoomAmenity> amenities,
  ) async {
    try {
      // Delete existing room amenities
      await _supabase.from('room_amenities').delete().eq('room_id', roomId);

      // Insert new room amenities
      if (amenities.isNotEmpty) {
        final amenityData =
            amenities.map((amenity) {
              final data = <String, dynamic>{'room_id': roomId};

              if (amenity.amenityId != null) {
                data['amenity_id'] = amenity.amenityId!;
              } else if (amenity.customAmenityName != null) {
                data['custom_amenity_name'] = amenity.customAmenityName!;
              }

              return data;
            }).toList();

        await _supabase.from('room_amenities').insert(amenityData);
      }
    } catch (error) {
      AppLogger.error('Error updating room amenities', error);
      throw Exception('Failed to update room amenities: $error');
    }
  }

  /// Update an existing room
  Future<Room?> updateRoom({
    required String id,
    String? name,
    String? propertyId,
    RoomOccupancyStatus? occupancyStatus,
    String? roomTypeId,
    String? customRoomType,
    double? rentalPrice,
    double? size,
    int? floor,
    bool? isFurnished,
    List<RoomAmenity>? amenities,
    String? description,
    String? imageUrl,
    String? notes,
    Map<String, dynamic>? additionalInfo,
  }) async {
    try {
      // Get current room to determine changes
      final currentRoom = await getRoomById(id);
      if (currentRoom == null) {
        return null;
      }

      // Validate room type - ensure we don't have both roomTypeId and customRoomType
      if (roomTypeId != null && customRoomType != null) {
        throw Exception(
          'Only one of room type ID or custom room type can be provided',
        );
      }

      // Update room data
      final updateData = <String, dynamic>{};
      if (name != null) {
        updateData['name'] = name;
      }
      if (propertyId != null) {
        updateData['property_id'] = propertyId;
      }
      if (occupancyStatus != null) {
        updateData['occupancy_status'] = occupancyStatus.dbValue;
      }

      // Handle room type updates
      if (roomTypeId != null) {
        updateData['room_type_id'] = roomTypeId;
        updateData['custom_room_type'] = null;
      } else if (customRoomType != null) {
        updateData['room_type_id'] = null;
        updateData['custom_room_type'] = customRoomType;
      }

      if (rentalPrice != null) {
        updateData['rental_price'] = rentalPrice;
      }
      if (size != null) {
        updateData['size'] = size;
      }
      if (floor != null) {
        updateData['floor'] = floor;
      }
      if (isFurnished != null) {
        updateData['is_furnished'] = isFurnished;
      }
      if (description != null) {
        updateData['description'] = description;
      }
      if (imageUrl != null) {
        updateData['image_url'] = imageUrl;
      }
      if (notes != null) {
        updateData['notes'] = notes;
      }
      if (additionalInfo != null) {
        updateData['additional_info'] = additionalInfo;
      }

      if (updateData.isNotEmpty) {
        await _supabase.from('rooms').update(updateData).eq('id', id);
      }

      // Update amenities if provided
      if (amenities != null) {
        await _updateRoomAmenities(id, amenities);
      }

      // Refresh the rooms stream
      _refreshRooms();

      // Return the updated room
      return await getRoomById(id);
    } catch (error) {
      AppLogger.error('Error updating room', error);
      return null;
    }
  }

  /// Delete a room
  Future<bool> deleteRoom(String id) async {
    try {
      // The room amenities will be automatically deleted due to the CASCADE constraint
      await _supabase.from('rooms').delete().eq('id', id);
      return true;
    } catch (error) {
      AppLogger.error('Error deleting room', error);
      return false;
    }
  }

  /// Update room occupancy status
  Future<Room> updateRoomOccupancyStatus(String roomId, RoomOccupancyStatus status) async {
    try {
      final room = await getRoomById(roomId);
      if (room == null) {
        throw Exception('Room not found');
      }

      // Store the old status for logging
      final oldStatus = room.occupancyStatus;

      // Create a new DateTime for the update
      final now = DateTime.now();

      final response = await _supabase
          .from('rooms')
          .update({
            'occupancy_status': status.dbValue,
            'updated_at': now.toIso8601String(),
          })
          .eq('id', roomId)
          .select()
          .single();

      final result = _mapToRoom(response);

      // Log the status change activity if status actually changed
      if (oldStatus != status) {
        await _logRoomStatusChange(result, oldStatus, status);
      }

      // Update the stream with fresh data
      _refreshRooms();

      return result;
    } catch (error) {
      AppLogger.error('Error updating room occupancy status', error);
      rethrow;
    }
  }

  /// Add a custom amenity to a room
  Future<bool> addCustomAmenityToRoom(
    String roomId,
    String customAmenityName,
  ) async {
    try {
      await _supabase.from('room_amenities').insert({
        'room_id': roomId,
        'custom_amenity_name': customAmenityName,
      });
      return true;
    } catch (error) {
      AppLogger.error('Error adding custom amenity to room', error);
      return false;
    }
  }

  /// Add a predefined amenity to a room
  Future<bool> addAmenityToRoom(String roomId, String amenityId) async {
    try {
      await _supabase.from('room_amenities').insert({
        'room_id': roomId,
        'amenity_id': amenityId,
      });
      return true;
    } catch (error) {
      AppLogger.error('Error adding amenity to room', error);
      return false;
    }
  }

  /// Remove an amenity from a room
  Future<bool> removeAmenityFromRoom(String roomAmenityId) async {
    try {
      await _supabase.from('room_amenities').delete().eq('id', roomAmenityId);
      return true;
    } catch (error) {
      AppLogger.error('Error removing amenity from room', error);
      return false;
    }
  }
  
  /// Get rooms by property ID (alias for getRoomsByPropertyId)
  Future<List<Room>> getRoomsByProperty(String propertyId) async {
    return getRoomsByPropertyId(propertyId);
  }

  /// Log room status change activity
  Future<void> _logRoomStatusChange(
    Room room,
    RoomOccupancyStatus oldStatus,
    RoomOccupancyStatus newStatus,
  ) async {
    try {
      final activityLog = ActivityLog(
        type: ActivityType.roomStatusChanged,
        roomId: room.id,
        propertyId: room.propertyId,
        action: 'Room status changed from ${oldStatus.displayName} to ${newStatus.displayName}',
        details: {
          'room_id': room.id,
          'room_name': room.name,
          'property_id': room.propertyId,
          'old_status': oldStatus.displayName,
          'new_status': newStatus.displayName,
          'old_status_value': oldStatus.dbValue,
          'new_status_value': newStatus.dbValue,
          'changed_at': DateTime.now().toIso8601String(),
        },
      );

      // Insert activity log directly into Supabase
      await _supabase.from('activity_logs').insert(activityLog.toJson());
      
      AppLogger.info('Room status change logged successfully: ${room.name} (${room.id}) changed from ${oldStatus.displayName} to ${newStatus.displayName}');
    } catch (error) {
      AppLogger.error('Error logging room status change', error);
      // Don't rethrow - logging failure shouldn't break the main operation
    }
  }

  /// Dispose of resources
  void dispose() {
    _roomStreamController.close();
  }
}
