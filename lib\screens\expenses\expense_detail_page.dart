import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../models/expense/expense_model.dart';
import '../../services/expense/expense_service.dart';
import '../../services/service_locator.dart';
import '../../utils/currency_formatter.dart';
import '../../widgets/app_loading_indicator.dart';
import '../../widgets/common/app_error_widget.dart';
import '../../widgets/common/section_header.dart';

class ExpenseDetailPage extends StatefulWidget {
  final String expenseId;

  const ExpenseDetailPage({super.key, required this.expenseId});

  @override
  State<ExpenseDetailPage> createState() => _ExpenseDetailPageState();
}

class _ExpenseDetailPageState extends State<ExpenseDetailPage> {
  final ExpenseService _expenseService = serviceLocator.expenseService;
  late Future<ExpenseModel?> _expenseFuture;
  bool _isProcessingAction = false;

  @override
  void initState() {
    super.initState();
    _loadExpense();
  }

  void _loadExpense() {
    _expenseFuture = _expenseService.getExpenseById(widget.expenseId);
  }

  Future<void> _deleteExpense(BuildContext context, String id) async {
    // Capture navigator before any async operations to avoid context issues
    final navigator = Navigator.of(context);
    final scaffoldMessenger = ScaffoldMessenger.of(context);
    
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (dialogContext) => AlertDialog(
        title: const Text('Delete Expense'),
        content: const Text('Are you sure you want to delete this expense?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(dialogContext).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            onPressed: () => Navigator.of(dialogContext).pop(true),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    // If not confirmed or widget unmounted, exit early
    if (confirmed != true || !mounted) return;

    setState(() {
      _isProcessingAction = true;
    });

    try {
      await _expenseService.deleteExpense(id);
      
      // Check if widget is still mounted after async operation
      if (!mounted) return;
      
      scaffoldMessenger.showSnackBar(
        const SnackBar(
          content: Text('Expense deleted successfully'),
          backgroundColor: Colors.green,
        ),
      );
      navigator.pop();
    } catch (e) {
      // Check if widget is still mounted after async operation
      if (!mounted) return;
      
      scaffoldMessenger.showSnackBar(
        SnackBar(
          content: Text('Failed to delete expense: $e'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      if (mounted) {
        setState(() {
          _isProcessingAction = false;
        });
      }
    }
  }

  Future<void> _processRecurringExpense(String id) async {
    if (_isProcessingAction) return;

    final scaffoldMessenger = ScaffoldMessenger.of(context);
    
    setState(() {
      _isProcessingAction = true;
    });

    try {
      await _expenseService.processRecurringExpense(id);
      
      // Check if widget is still mounted after async operation
      if (!mounted) return;
      
      scaffoldMessenger.showSnackBar(
        const SnackBar(
          content: Text('Recurring expense processed successfully'),
          backgroundColor: Colors.green,
        ),
      );
      
      setState(() {
        _loadExpense();
      });
    } catch (e) {
      // Check if widget is still mounted after async operation
      if (!mounted) return;
      
      scaffoldMessenger.showSnackBar(
        SnackBar(
          content: Text('Failed to process recurring expense: $e'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      if (mounted) {
        setState(() {
          _isProcessingAction = false;
        });
      }
    }
  }

  Future<void> _shareExpenseDetails(ExpenseModel expense) async {
    final dateFormat = DateFormat('MMMM d, yyyy');
    final expenseDetails = '''
${expense.title} (${expense.formattedExpenseNumber})
Amount: ${CurrencyFormatter.formatCurrency(expense.amount)}
Date: ${dateFormat.format(expense.date)}
Category: ${expense.categoryName ?? 'Uncategorized'}
${expense.propertyName != null ? 'Property: ${expense.propertyName}\n' : ''}${expense.roomName != null ? 'Room: ${expense.roomName}\n' : ''}${expense.vendorName != null ? 'Vendor: ${expense.vendorName}\n' : ''}
${expense.description.isNotEmpty ? 'Description: ${expense.description}\n' : ''}
''';

    await Clipboard.setData(ClipboardData(text: expenseDetails));
    if (!mounted) return;
    
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Expense details copied to clipboard'),
      ),
    );
  }

  Future<void> _viewReceipt(String? receiptUrl) async {
    if (receiptUrl == null) return;
    
    final Uri url = Uri.parse(receiptUrl);
    if (await canLaunchUrl(url)) {
      await launchUrl(url);
    } else {
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Could not open receipt'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Expense Details'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Theme.of(context).colorScheme.onPrimary,
        actions: [
          FutureBuilder<ExpenseModel?>(
            future: _expenseFuture,
            builder: (context, snapshot) {
              if (snapshot.connectionState == ConnectionState.waiting ||
                  !snapshot.hasData) {
                return const SizedBox.shrink();
              }
              return Row(
                children: [
                  IconButton(
                    icon: const Icon(Icons.share),
                    tooltip: 'Share expense details',
                    onPressed: () => _shareExpenseDetails(snapshot.data!),
                  ),
                  PopupMenuButton<String>(
                    onSelected: (value) {
                      if (value == 'edit') {
                        Navigator.pushNamed(
                          context,
                          '/expenses/edit',
                          arguments: snapshot.data!.id,
                        ).then((_) => setState(() {
                              _loadExpense();
                            }));
                      } else if (value == 'delete') {
                        _deleteExpense(context, snapshot.data!.id!);
                      } else if (value == 'process') {
                        _processRecurringExpense(snapshot.data!.id!);
                      }
                    },
                    itemBuilder: (context) => [
                      const PopupMenuItem(
                        value: 'edit',
                        child: Row(
                          children: [
                            Icon(Icons.edit),
                            SizedBox(width: 8),
                            Text('Edit'),
                          ],
                        ),
                      ),
                      if (snapshot.data!.isRecurring)
                        const PopupMenuItem(
                          value: 'process',
                          child: Row(
                            children: [
                              Icon(Icons.event_repeat),
                              SizedBox(width: 8),
                              Text('Process Now'),
                            ],
                          ),
                        ),
                      const PopupMenuItem(
                        value: 'delete',
                        child: Row(
                          children: [
                            Icon(Icons.delete, color: Colors.red),
                            SizedBox(width: 8),
                            Text('Delete', style: TextStyle(color: Colors.red)),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              );
            },
          ),
        ],
      ),
      body: FutureBuilder<ExpenseModel?>(
        future: _expenseFuture,
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const AppLoadingIndicator();
          } else if (snapshot.hasError) {
            return AppErrorWidget(
              message: snapshot.error.toString(),
              onRetry: () {
                setState(() {
                  _loadExpense();
                });
              },
            );
          } else if (!snapshot.hasData) {
            return const Center(
              child: Text('Expense not found'),
            );
          }

          final expense = snapshot.data!;
          return Stack(
            children: [
              // Details content (previously in tab)
              _buildDetailsTab(expense),
              
              if (_isProcessingAction)
                Container(
                  color: Colors.black.withAlpha(77), // 0.3 * 255 ≈ 77
                  child: const Center(
                    child: CircularProgressIndicator(),
                  ),
                ),
            ],
          );
        },
      ),
      floatingActionButton: FutureBuilder<ExpenseModel?>(
        future: _expenseFuture,
        builder: (context, snapshot) {
          if (!snapshot.hasData || !snapshot.data!.isRecurring) {
            return const SizedBox.shrink();
          }
          
          return FloatingActionButton.extended(
            onPressed: () => _processRecurringExpense(snapshot.data!.id!),
            icon: const Icon(Icons.event_repeat),
            label: const Text('Process Now'),
            backgroundColor: Theme.of(context).colorScheme.secondary,
          );
        },
      ),
    );
  }

  Widget _buildDetailsTab(ExpenseModel expense) {
    return RefreshIndicator(
      onRefresh: () async {
        setState(() {
          _loadExpense();
        });
      },
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        physics: const AlwaysScrollableScrollPhysics(),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Main Info Card
            Card(
              elevation: 4,
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
              child: Column(
                children: [
                  // Header with gradient
                  Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          Theme.of(context).colorScheme.primary,
                          Theme.of(context).colorScheme.primary.withAlpha(204), // 0.8 * 255 ≈ 204
                        ],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(16),
                        topRight: Radius.circular(16),
                      ),
                    ),
                    padding: const EdgeInsets.all(20),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    expense.title,
                                    style: const TextStyle(
                                      fontSize: 24,
                                      fontWeight: FontWeight.bold,
                                      color: Colors.white,
                                    ),
                                  ),
                                  const SizedBox(height: 8),
                                  Text(
                                    expense.formattedExpenseNumber,
                                    style: TextStyle(
                                      color: Colors.white.withAlpha(204), // 0.8 * 255 ≈ 204
                                      fontSize: 14,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.end,
                              children: [
                                Text(
                                  CurrencyFormatter.formatCurrency(expense.amount),
                                  style: const TextStyle(
                                    fontSize: 24,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.white,
                                  ),
                                ),
                                if (expense.isRecurring)
                                  Container(
                                    margin: const EdgeInsets.only(top: 8),
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 12,
                                      vertical: 4,
                                    ),
                                    decoration: BoxDecoration(
                                      color: Colors.white.withAlpha(51), // 0.2 * 255 ≈ 51
                                      borderRadius: BorderRadius.circular(20),
                                    ),
                                    child: Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        const Icon(
                                          Icons.repeat,
                                          size: 14,
                                          color: Colors.white,
                                        ),
                                        const SizedBox(width: 4),
                                        const Text(
                                          'Recurring',
                                          style: TextStyle(
                                            color: Colors.white,
                                            fontSize: 12,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                              ],
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),
                        Row(
                          children: [
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 12,
                                vertical: 6,
                              ),
                              decoration: BoxDecoration(
                                color: expense.categoryColor?.withAlpha(51) ?? Colors.grey.withAlpha(51), // 0.2 * 255 ≈ 51
                                borderRadius: BorderRadius.circular(20),
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Container(
                                    width: 8,
                                    height: 8,
                                    decoration: BoxDecoration(
                                      color: expense.categoryColor ?? Colors.grey,
                                      shape: BoxShape.circle,
                                    ),
                                  ),
                                  const SizedBox(width: 8),
                                  Text(
                                    expense.categoryName ?? 'Uncategorized',
                                    style: const TextStyle(
                                      color: Colors.white,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            const SizedBox(width: 12),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 12,
                                vertical: 6,
                              ),
                              decoration: BoxDecoration(
                                color: Colors.white.withAlpha(51), // 0.2 * 255 ≈ 51
                                borderRadius: BorderRadius.circular(20),
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  const Icon(
                                    Icons.calendar_today,
                                    size: 12,
                                    color: Colors.white,
                                  ),
                                  const SizedBox(width: 8),
                                  Text(
                                    DateFormat('MMM d, y').format(expense.date),
                                    style: const TextStyle(
                                      color: Colors.white,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  // Body content
                  Padding(
                    padding: const EdgeInsets.all(20),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Description
                        if (expense.description.isNotEmpty) ...[
                          const SectionHeader(title: 'Description'),
                          Padding(
                            padding: const EdgeInsets.only(bottom: 20),
                            child: Text(
                              expense.description,
                              style: const TextStyle(
                                fontSize: 16,
                                height: 1.5,
                              ),
                            ),
                          ),
                        ],

                        // Location
                        if (expense.propertyName != null || expense.roomName != null) ...[
                          const SectionHeader(title: 'Location'),
                          Padding(
                            padding: const EdgeInsets.only(bottom: 20),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                if (expense.propertyName != null)
                                  _buildInfoRow(
                                    Icons.business,
                                    'Property',
                                    expense.propertyName!,
                                  ),
                                if (expense.roomName != null)
                                  _buildInfoRow(
                                    Icons.meeting_room,
                                    'Room',
                                    expense.roomName!,
                                  ),
                              ],
                            ),
                          ),
                        ],

                        // Vendor
                        if (expense.vendorName != null) ...[
                          const SectionHeader(title: 'Vendor'),
                          Padding(
                            padding: const EdgeInsets.only(bottom: 20),
                            child: _buildInfoRow(
                              Icons.storefront,
                              'Vendor',
                              expense.vendorName!,
                            ),
                          ),
                        ],

                        // Receipt
                        if (expense.receiptUrl != null) ...[
                          const SectionHeader(title: 'Receipt'),
                          Padding(
                            padding: const EdgeInsets.only(bottom: 20),
                            child: InkWell(
                              onTap: () => _viewReceipt(expense.receiptUrl),
                              child: Container(
                                decoration: BoxDecoration(
                                  border: Border.all(color: Colors.grey.shade300),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                padding: const EdgeInsets.all(12),
                                child: Row(
                                  children: [
                                    const Icon(
                                      Icons.receipt_long,
                                      color: Colors.blue,
                                    ),
                                    const SizedBox(width: 12),
                                    const Expanded(
                                      child: Text(
                                        'View Receipt',
                                        style: TextStyle(
                                          color: Colors.blue,
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                    ),
                                    const Icon(
                                      Icons.open_in_new,
                                      color: Colors.blue,
                                      size: 16,
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ],

                        // If recurring, show recurring details
                        if (expense.isRecurring) ...[
                          const SectionHeader(title: 'Recurring Schedule'),
                          Padding(
                            padding: const EdgeInsets.only(bottom: 20),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                _buildInfoRow(
                                  Icons.repeat,
                                  'Frequency',
                                  _formatFrequency(expense.frequency),
                                ),
                                if (expense.nextDueDate != null)
                                  _buildInfoRow(
                                    Icons.event_note,
                                    'Next Due Date',
                                    DateFormat('MMMM d, y').format(expense.nextDueDate!),
                                  ),
                                if (expense.occurrences != null)
                                  _buildInfoRow(
                                    Icons.repeat_one,
                                    'Occurrences',
                                    '${expense.occurrencesCompleted ?? 0}/${expense.occurrences}',
                                  ),
                                if (expense.endDate != null)
                                  _buildInfoRow(
                                    Icons.event_busy,
                                    'End Date',
                                    DateFormat('MMMM d, y').format(expense.endDate!),
                                  ),
                              ],
                            ),
                          ),
                        ],

                        // Metadata
                        const SectionHeader(title: 'Metadata'),
                        Padding(
                          padding: const EdgeInsets.only(bottom: 20),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              _buildInfoRow(
                                Icons.tag,
                                'Expense ID',
                                expense.formattedExpenseNumber,
                              ),
                              _buildInfoRow(
                                Icons.access_time,
                                'Created',
                                DateFormat('MMM d, y').format(expense.createdAt),
                              ),
                              if (expense.updatedAt != null)
                                _buildInfoRow(
                                  Icons.update,
                                  'Last Updated',
                                  DateFormat('MMM d, y').format(expense.updatedAt!),
                                ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(IconData icon, String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            icon,
            size: 16,
            color: Colors.grey.shade600,
          ),
          const SizedBox(width: 12),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey.shade600,
                ),
              ),
              Text(
                value,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  String _formatFrequency(ExpenseFrequency? frequency) {
    if (frequency == null) return 'One-time';
    
    switch (frequency) {
      case ExpenseFrequency.weekly:
        return 'Weekly';
      case ExpenseFrequency.monthly:
        return 'Monthly';
      case ExpenseFrequency.quarterly:
        return 'Every 3 months';
      case ExpenseFrequency.annually:
        return 'Yearly';
      case ExpenseFrequency.oneTime:
        return 'One-time';
      case ExpenseFrequency.custom:
        return 'Custom';
    }
  }
} 