#pragma once

#include <string>
#include <windows.h>

namespace ATL {
    // Basic implementation of CW2A to convert wide strings to ASCII
    class CW2A {
    public:
        CW2A(LPCWSTR src) {
            if (src) {
                int size_needed = WideCharToMultiByte(CP_ACP, 0, src, -1, NULL, 0, NULL, NULL);
                m_psz = new char[size_needed];
                WideCharToMultiByte(CP_ACP, 0, src, -1, m_psz, size_needed, NULL, NULL);
            } else {
                m_psz = new char[1];
                m_psz[0] = '\0';
            }
        }

        ~CW2A() {
            delete[] m_psz;
        }

        operator LPSTR() const {
            return m_psz;
        }

    private:
        char* m_psz;
        CW2A(const CW2A&);
        CW2A& operator=(const CW2A&);
    };

    // Basic implementation of CA2W to convert ASCII strings to wide
    class CA2W {
    public:
        CA2W(LPCSTR src) {
            if (src) {
                int size_needed = MultiByteToWideChar(CP_ACP, 0, src, -1, NULL, 0);
                m_psz = new wchar_t[size_needed];
                MultiByteToWideChar(CP_ACP, 0, src, -1, m_psz, size_needed);
            } else {
                m_psz = new wchar_t[1];
                m_psz[0] = L'\0';
            }
        }

        ~CA2W() {
            delete[] m_psz;
        }

        operator LPWSTR() const {
            return m_psz;
        }

    private:
        wchar_t* m_psz;
        CA2W(const CA2W&);
        CA2W& operator=(const CA2W&);
    };
} 