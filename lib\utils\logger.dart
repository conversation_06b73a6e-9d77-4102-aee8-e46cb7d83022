import 'package:logging/logging.dart';

/// A utility class for logging in the application
class AppLogger {
  static final Logger _logger = Logger('TenantaApp');
  static bool _initialized = false;

  /// Initialize the logger
  static void init() {
    if (_initialized) return;

    // Completely disable all logging for production
    Logger.root.level = Level.OFF;
    Logger.root.onRecord.listen((record) {
      // All logging disabled for production
    });

    _initialized = true;
  }

  /// Log an info message
  static void info(String message) {
    _ensureInitialized();
    _logger.info(message);
  }

  /// Log a warning message
  static void warning(String message, [Object? error, StackTrace? stackTrace]) {
    _ensureInitialized();
    _logger.warning(message, error, stackTrace);
  }

  /// Log an error message
  static void error(String message, [Object? error, StackTrace? stackTrace]) {
    _ensureInitialized();
    _logger.severe(message, error, stackTrace);
  }

  /// Log a debug message (only in debug mode)
  static void debug(String message, [Object? error, StackTrace? stackTrace]) {
    _ensureInitialized();
    _logger.fine(message, error, stackTrace);
  }

  /// Make sure the logger is initialized
  static void _ensureInitialized() {
    if (!_initialized) {
      init();
    }
  }
} 