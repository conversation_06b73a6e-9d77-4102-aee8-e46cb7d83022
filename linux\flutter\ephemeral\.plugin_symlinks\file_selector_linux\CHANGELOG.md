## 0.9.3+2

* Updates <PERSON><PERSON> to resolve a compilation failure with some versions of glib.

## 0.9.3+1

* Fixes a regression in 0.9.3 with handling of canceled dialogs.

## 0.9.3

* Updates method channel implementation to use <PERSON><PERSON>.
* Updates minimum supported SDK version to Flutter 3.19/Dart 3.3.

## 0.9.2+1

* Adds pub topics to package metadata.
* Updates minimum supported SDK version to Flutter 3.7/Dart 2.19.
* Migrates `styleFrom` usage in examples off of deprecated `primary` and `onPrimary` parameters.

## 0.9.2

* Adds `getSaveLocation` and deprecates `getSavePath`.
* Updates minimum supported SDK version to Flutter 3.3/Dart 2.18.

## 0.9.1+3

* Sets a cmake_policy compatibility version to fix build warnings.

## 0.9.1+2

* Clarifies explanation of endorsement in README.
* Aligns Dart and Flutter SDK constraints.

## 0.9.1+1

* Updates links for the merge of flutter/plugins into flutter/packages.
* Updates example code for `use_build_context_synchronously` lint.
* Updates minimum Flutter version to 3.0.

## 0.9.1

* Adds `getDirectoryPaths` implementation.

## 0.9.0+1

* Changes XTypeGroup initialization from final to const.
* Updates minimum Flutter version to 2.10.

## 0.9.0

* Moves source to flutter/plugins.

## 0.0.3

* Adds Dart implementation for in-package method channel.

## 0.0.2+1

* Updates README

## 0.0.2

* Updates SDK constraint to signal compatibility with null safety.

## 0.0.1

* Initial Linux implementation of `file_selector`.
