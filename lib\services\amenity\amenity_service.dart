import 'dart:async';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../models/room/amenity_model.dart';
import '../../utils/logger.dart';

/// Service for managing amenities using Supabase
class AmenityService {
  // Stream controller for amenity changes
  final _amenityStreamController = StreamController<List<Amenity>>.broadcast();
  
  // Supabase client
  late final SupabaseClient _supabase;
  
  // Singleton instance
  static final AmenityService _instance = AmenityService._internal();
  
  // Factory constructor
  factory AmenityService() {
    return _instance;
  }
  
  // Private constructor
  AmenityService._internal() {
    _supabase = Supabase.instance.client;
    _refreshAmenities();
  }
  
  /// Get stream of amenity updates
  Stream<List<Amenity>> get amenitiesStream => _amenityStreamController.stream;
  
  /// Fetch all amenities
  Future<List<Amenity>> _fetchAllAmenities() async {
    try {
      final response = await _supabase.from('amenities').select().order('name');
      
      final List<dynamic> data = response;
      return data.map((json) => Amenity.fromJson(json)).toList();
    } catch (error) {
      AppLogger.error('Error fetching amenities', error);
      return [];
    }
  }
  
  /// Manually refresh amenities data
  Future<void> _refreshAmenities() async {
    final amenities = await _fetchAllAmenities();
    if (!_amenityStreamController.isClosed) {
      _amenityStreamController.add(amenities);
    }
  }
  
  /// Get all amenities
  Future<List<Amenity>> getAllAmenities() async {
    final amenities = await _fetchAllAmenities();
    
    // Update the stream with fresh data
    if (!_amenityStreamController.isClosed) {
      _amenityStreamController.add(amenities);
    }
    
    return amenities;
  }
  
  /// Get predefined amenities
  Future<List<Amenity>> getPredefinedAmenities() async {
    try {
      final response = await _supabase
          .from('amenities')
          .select()
          .eq('is_predefined', true)
          .order('name');
      
      final List<dynamic> data = response;
      return data.map((json) => Amenity.fromJson(json)).toList();
    } catch (error) {
      AppLogger.error('Error fetching predefined amenities', error);
      return [];
    }
  }
  
  /// Get an amenity by ID
  Future<Amenity?> getAmenityById(String id) async {
    try {
      final response = await _supabase
          .from('amenities')
          .select()
          .eq('id', id)
          .maybeSingle();

      if (response == null) {
        AppLogger.info('No amenity found with ID: $id');
        return null;
      }

      return Amenity.fromJson(response);
    } catch (error) {
      AppLogger.error('Error fetching amenity by ID', error);
      return null;
    }
  }
  
  /// Add a new custom amenity
  Future<Amenity?> addCustomAmenity({
    required String name,
    String? description,
    String? icon,
  }) async {
    try {
      final response = await _supabase.from('amenities').insert({
        'name': name,
        'description': description,
        'icon': icon,
        'is_predefined': false,
      }).select().single();
      
      // Refresh the amenities stream
      _refreshAmenities();
      
      return Amenity.fromJson(response);
    } catch (error) {
      AppLogger.error('Error adding custom amenity', error);
      throw Exception('Failed to add custom amenity: $error');
    }
  }
  
  /// Update a custom amenity
  Future<Amenity?> updateCustomAmenity({
    required String id,
    String? name,
    String? description,
    String? icon,
  }) async {
    try {
      // Only allow updating custom amenities, not predefined ones
      final amenity = await getAmenityById(id);
      if (amenity == null || amenity.isPredefined) {
        throw Exception('Cannot update a predefined amenity');
      }
      
      final updateData = <String, dynamic>{};
      if (name != null) updateData['name'] = name;
      if (description != null) updateData['description'] = description;
      if (icon != null) updateData['icon'] = icon;
      
      if (updateData.isNotEmpty) {
        final response = await _supabase
            .from('amenities')
            .update(updateData)
            .eq('id', id)
            .select()
            .single();
        
        // Refresh the amenities stream
        _refreshAmenities();
        
        return Amenity.fromJson(response);
      }
      
      return amenity;
    } catch (error) {
      AppLogger.error('Error updating custom amenity', error);
      throw Exception('Failed to update custom amenity: $error');
    }
  }
  
  /// Delete a custom amenity
  Future<bool> deleteCustomAmenity(String id) async {
    try {
      // Only allow deleting custom amenities, not predefined ones
      final amenity = await getAmenityById(id);
      if (amenity == null || amenity.isPredefined) {
        throw Exception('Cannot delete a predefined amenity');
      }
      
      await _supabase.from('amenities').delete().eq('id', id);
      
      // Refresh the amenities stream
      _refreshAmenities();
      
      return true;
    } catch (error) {
      AppLogger.error('Error deleting custom amenity', error);
      return false;
    }
  }
  
  /// Dispose of resources
  void dispose() {
    _amenityStreamController.close();
  }
} 