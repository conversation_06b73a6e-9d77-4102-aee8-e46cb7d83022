import 'package:flutter/material.dart';
import '../../models/payment/payment_model.dart';
import '../../services/service_locator.dart';
import '../../utils/currency_formatter.dart';
import '../../utils/date_formatter.dart';
import 'payment_detail_page.dart';

class PaymentHistoryPage extends StatefulWidget {
  final String?
  tenantId; // Optional: if provided, show only payments for this tenant

  const PaymentHistoryPage({super.key, this.tenantId});

  @override
  State<PaymentHistoryPage> createState() => _PaymentHistoryPageState();
}

class _PaymentHistoryPageState extends State<PaymentHistoryPage> {
  bool _isLoading = true;
  List<Payment> _payments = [];
  PaymentStatus? _statusFilter;
  String _searchQuery = '';
  List<Payment> _filteredPayments = [];

  // Date range filter
  DateTime? _startDate;
  DateTime? _endDate;

  // Selection mode
  bool _selectionMode = false;
  final Set<String> _selectedPaymentIds = {};

  // Auto-verification timer
  bool _autoVerificationEnabled = true;

  @override
  void initState() {
    super.initState();
    _loadPayments();
  }

  Future<void> _loadPayments() async {
    setState(() {
      _isLoading = true;
    });

    try {
      List<Payment> payments;

      if (widget.tenantId != null) {
        // Load payments for specific tenant
        payments = await serviceLocator.paymentService.getPaymentsByTenantId(
          widget.tenantId!,
          startDate: _startDate,
          endDate: _endDate,
          status: _statusFilter,
        );
      } else {
        // Load all payments
        payments = await serviceLocator.paymentService.getAllPayments(
          startDate: _startDate,
          endDate: _endDate,
          status: _statusFilter,
        );
      }

      if (mounted) {
        setState(() {
          _payments = payments;
          _applyFilters();
          _isLoading = false;

          // Check for payments that need auto-verification
          if (_autoVerificationEnabled) {
            _checkForAutoVerification(payments);
          }
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Error loading payments: $e')));
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _checkForAutoVerification(List<Payment> payments) async {
    final DateTime sevenDaysAgo = DateTime.now().subtract(
      const Duration(days: 7),
    );
    final List<Payment> paymentsToAutoVerify =
        payments
            .where(
              (payment) =>
                  payment.status == PaymentStatus.pending &&
                  payment.paymentDate.isBefore(sevenDaysAgo),
            )
            .toList();

    if (paymentsToAutoVerify.isNotEmpty) {
      try {
        // Verify all eligible payments
        for (final payment in paymentsToAutoVerify) {
          await serviceLocator.paymentService.verifyPayment(payment.id);
        }

        // Show success message
        if (mounted && paymentsToAutoVerify.isNotEmpty) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                '${paymentsToAutoVerify.length} payments auto-verified (older than 7 days)',
              ),
              backgroundColor: Colors.green,
            ),
          );
          // Reload payments to reflect changes
          _loadPayments();
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Error auto-verifying payments: $e')),
          );
        }
      }
    }
  }

  void _applyFilters() {
    setState(() {
      if (_searchQuery.isEmpty) {
        _filteredPayments = _payments;
      } else {
        _filteredPayments =
            _payments.where((payment) {
              // Search by reference number or notes
              final reference = payment.receiptReference?.toLowerCase() ?? '';
              final notes = payment.notes?.toLowerCase() ?? '';
              final searchLower = _searchQuery.toLowerCase();

              return reference.contains(searchLower) ||
                  notes.contains(searchLower);
            }).toList();
      }

      // Clear selection when filters change
      if (_selectionMode) {
        _selectedPaymentIds.clear();
      }
    });
  }

  void _setStatusFilter(PaymentStatus? status) {
    setState(() {
      _statusFilter = status;
    });
    _loadPayments();
  }

  void _setDateRange() async {
    final DateTimeRange? picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2000),
      lastDate: DateTime.now().add(const Duration(days: 1)),
      initialDateRange:
          _startDate != null && _endDate != null
              ? DateTimeRange(start: _startDate!, end: _endDate!)
              : null,
    );

    if (picked != null) {
      setState(() {
        _startDate = picked.start;
        _endDate = picked.end;
      });
      _loadPayments();
    }
  }

  void _clearFilters() {
    setState(() {
      _statusFilter = null;
      _startDate = null;
      _endDate = null;
      _searchQuery = '';
    });
    _loadPayments();
  }

  void _viewPaymentDetails(Payment payment) {
    Navigator.of(context)
        .push(
          MaterialPageRoute(
            builder: (context) => PaymentDetailPage(paymentId: payment.id),
          ),
        )
        .then((_) {
          // Refresh the list when returning from details
          _loadPayments();
        });
  }

  void _toggleSelectionMode() {
    setState(() {
      _selectionMode = !_selectionMode;
      if (!_selectionMode) {
        _selectedPaymentIds.clear();
      }
    });
  }

  void _togglePaymentSelection(String paymentId) {
    setState(() {
      if (_selectedPaymentIds.contains(paymentId)) {
        _selectedPaymentIds.remove(paymentId);
      } else {
        _selectedPaymentIds.add(paymentId);
      }

      // Exit selection mode if nothing is selected
      if (_selectedPaymentIds.isEmpty) {
        _selectionMode = false;
      }
    });
  }

  void _selectAll() {
    setState(() {
      if (_selectedPaymentIds.length ==
          _filteredPayments
              .where((p) => p.status == PaymentStatus.pending)
              .length) {
        // If all are selected, deselect all
        _selectedPaymentIds.clear();
      } else {
        // Otherwise select all pending payments
        _selectedPaymentIds.clear();
        for (final payment in _filteredPayments) {
          if (payment.status == PaymentStatus.pending) {
            _selectedPaymentIds.add(payment.id);
          }
        }
      }
    });
  }

  Future<void> _verifySelectedPayments() async {
    if (_selectedPaymentIds.isEmpty) return;

    // Show confirmation dialog
    final bool confirm =
        await showDialog(
          context: context,
          builder:
              (context) => AlertDialog(
                title: const Text('Verify Payments'),
                content: Text(
                  'Are you sure you want to verify ${_selectedPaymentIds.length} payments?',
                ),
                actions: [
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(false),
                    child: const Text('CANCEL'),
                  ),
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(true),
                    child: const Text('VERIFY'),
                  ),
                ],
              ),
        ) ??
        false;

    if (!confirm) return;

    setState(() {
      _isLoading = true;
    });

    try {
      // Verify all selected payments
      for (final paymentId in _selectedPaymentIds) {
        await serviceLocator.paymentService.verifyPayment(paymentId);
      }

      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              '${_selectedPaymentIds.length} payments verified successfully',
            ),
            backgroundColor: Colors.green,
          ),
        );
      }

      // Exit selection mode and reload payments
      setState(() {
        _selectionMode = false;
        _selectedPaymentIds.clear();
      });
      _loadPayments();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Error verifying payments: $e')));
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _toggleAutoVerification() {
    setState(() {
      _autoVerificationEnabled = !_autoVerificationEnabled;
    });

    // Show confirmation of setting change
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          _autoVerificationEnabled
              ? 'Auto-verification enabled (after 7 days)'
              : 'Auto-verification disabled',
        ),
        duration: const Duration(seconds: 2),
      ),
    );

    // If enabled, check for payments to verify
    if (_autoVerificationEnabled) {
      _checkForAutoVerification(_payments);
    }
  }

  @override
  Widget build(BuildContext context) {
    // Count pending payments for verification
    final int pendingCount =
        _filteredPayments
            .where((p) => p.status == PaymentStatus.pending)
            .length;

    return Scaffold(
      appBar: AppBar(
        title: const Text('Payment History'),
        leading:
            _selectionMode
                ? IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: _toggleSelectionMode,
                )
                : null,
        actions:
            _selectionMode
                ? [
                  // Selection mode actions
                  IconButton(
                    icon: const Icon(Icons.select_all),
                    tooltip: 'Select All',
                    onPressed: _selectAll,
                  ),
                  if (_selectedPaymentIds.isNotEmpty)
                    IconButton(
                      icon: const Icon(Icons.check_circle_outline),
                      tooltip: 'Verify Selected',
                      onPressed: _verifySelectedPayments,
                    ),
                ]
                : [
                  // Normal mode actions
                  IconButton(
                    icon: Icon(
                      _autoVerificationEnabled ? Icons.timer : Icons.timer_off,
                      color: _autoVerificationEnabled ? Colors.green : null,
                    ),
                    tooltip:
                        _autoVerificationEnabled
                            ? 'Auto-verification enabled (7 days)'
                            : 'Auto-verification disabled',
                    onPressed: _toggleAutoVerification,
                  ),
                  if (pendingCount > 0)
                    IconButton(
                      icon: const Icon(Icons.checklist),
                      tooltip: 'Bulk Verify',
                      onPressed: _toggleSelectionMode,
                    ),
                  IconButton(
                    icon: const Icon(Icons.filter_list),
                    onPressed: () {
                      _showFilterBottomSheet();
                    },
                  ),
                ],
      ),
      body: Column(
        children: [
          // Search bar
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: TextField(
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                  _applyFilters();
                });
              },
              decoration: InputDecoration(
                labelText: 'Search payments',
                hintText: 'Enter reference number or notes',
                prefixIcon: const Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
                suffixIcon:
                    _searchQuery.isNotEmpty
                        ? IconButton(
                          icon: const Icon(Icons.clear),
                          onPressed: () {
                            setState(() {
                              _searchQuery = '';
                              _applyFilters();
                            });
                          },
                        )
                        : null,
              ),
            ),
          ),

          // Active filters chips
          if (_statusFilter != null || _startDate != null)
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: Wrap(
                spacing: 8.0,
                children: [
                  if (_statusFilter != null)
                    Chip(
                      label: Text(_getStatusName(_statusFilter!)),
                      onDeleted: () {
                        _setStatusFilter(null);
                      },
                    ),
                  if (_startDate != null && _endDate != null)
                    Chip(
                      label: Text(
                        '${_formatDate(_startDate!)} - ${_formatDate(_endDate!)}',
                      ),
                      onDeleted: () {
                        setState(() {
                          _startDate = null;
                          _endDate = null;
                        });
                        _loadPayments();
                      },
                    ),
                  if (_statusFilter != null || _startDate != null)
                    ActionChip(
                      label: const Text('Clear All'),
                      onPressed: _clearFilters,
                    ),
                ],
              ),
            ),

          // Selection info bar
          if (_selectionMode && _selectedPaymentIds.isNotEmpty)
            Container(
              color: Theme.of(context).colorScheme.primaryContainer,
              padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
              child: Row(
                children: [
                  Text(
                    '${_selectedPaymentIds.length} selected',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).colorScheme.onPrimaryContainer,
                    ),
                  ),
                  const Spacer(),
                  TextButton.icon(
                    icon: const Icon(Icons.check_circle),
                    label: const Text('VERIFY'),
                    onPressed: _verifySelectedPayments,
                    style: TextButton.styleFrom(
                      foregroundColor:
                          Theme.of(context).colorScheme.onPrimaryContainer,
                    ),
                  ),
                ],
              ),
            ),

          // Payments list
          Expanded(
            child:
                _isLoading
                    ? const Center(child: CircularProgressIndicator())
                    : _filteredPayments.isEmpty
                    ? Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const Icon(
                            Icons.payment,
                            size: 64,
                            color: Colors.grey,
                          ),
                          const SizedBox(height: 16),
                          Text(
                            _searchQuery.isEmpty &&
                                    _statusFilter == null &&
                                    _startDate == null
                                ? 'No payments found'
                                : 'No payments match your filters',
                            style: Theme.of(context).textTheme.titleMedium,
                          ),
                        ],
                      ),
                    )
                    : ListView.builder(
                      itemCount: _filteredPayments.length,
                      itemBuilder: (context, index) {
                        final payment = _filteredPayments[index];
                        final bool isSelectable =
                            payment.status == PaymentStatus.pending;
                        final bool isSelected = _selectedPaymentIds.contains(
                          payment.id,
                        );

                        // Calculate days since payment
                        final daysSincePayment =
                            DateTime.now()
                                .difference(payment.paymentDate)
                                .inDays;
                        final bool isNearingAutoVerification =
                            payment.status == PaymentStatus.pending &&
                            daysSincePayment >= 5 &&
                            daysSincePayment < 7 &&
                            _autoVerificationEnabled;

                        return ListTile(
                          leading:
                              _selectionMode
                                  ? (isSelectable
                                      ? Checkbox(
                                        value: isSelected,
                                        onChanged: (bool? value) {
                                          _togglePaymentSelection(payment.id);
                                        },
                                      )
                                      : const SizedBox(width: 48))
                                  : CircleAvatar(
                                    backgroundColor: _getStatusColor(
                                      payment.status,
                                    ),
                                    child: Icon(
                                      _getPaymentMethodIcon(payment.method),
                                      color: Colors.white,
                                    ),
                                  ),
                          title: Row(
                            children: [
                              Expanded(
                                child: Text(
                                  payment.receiptReference ??
                                      'Payment on ${_formatDate(payment.paymentDate)}',
                                  style:
                                      isNearingAutoVerification
                                          ? TextStyle(
                                            color: Colors.orange.shade800,
                                          )
                                          : null,
                                ),
                              ),
                              if (isNearingAutoVerification)
                                Tooltip(
                                  message:
                                      'Auto-verification in ${7 - daysSincePayment} days',
                                  child: Icon(
                                    Icons.timer,
                                    size: 16,
                                    color: Colors.orange.shade800,
                                  ),
                                ),
                            ],
                          ),
                          subtitle: Text(
                            '${_getStatusName(payment.status)} • ${_getPaymentMethodName(payment.method)} • ${DateFormatter.formatDate(payment.paymentDate)}',
                          ),
                          trailing: Text(
                            CurrencyFormatter.formatCurrency(payment.amount),
                            style: Theme.of(context).textTheme.titleMedium,
                          ),
                          selected: isSelected,
                          selectedTileColor: Theme.of(
                            context,
                          ).colorScheme.primaryContainer.withAlpha(76),
                          onTap:
                              _selectionMode
                                  ? (isSelectable
                                      ? () =>
                                          _togglePaymentSelection(payment.id)
                                      : null)
                                  : () => _viewPaymentDetails(payment),
                          onLongPress:
                              isSelectable && !_selectionMode
                                  ? () {
                                    _toggleSelectionMode();
                                    _togglePaymentSelection(payment.id);
                                  }
                                  : null,
                        );
                      },
                    ),
          ),
        ],
      ),
    );
  }

  void _showFilterBottomSheet() {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setModalState) {
            return Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Filter Payments',
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                      TextButton(
                        onPressed: () {
                          _clearFilters();
                          Navigator.pop(context);
                        },
                        child: const Text('Clear All'),
                      ),
                    ],
                  ),
                  const Divider(),

                  // Status filter
                  Text(
                    'Status',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const SizedBox(height: 8),
                  Wrap(
                    spacing: 8.0,
                    children: [
                      ChoiceChip(
                        label: const Text('All'),
                        selected: _statusFilter == null,
                        onSelected: (selected) {
                          if (selected) {
                            setModalState(() {
                              _statusFilter = null;
                            });
                          }
                        },
                      ),
                      ...PaymentStatus.values.map((status) {
                        return ChoiceChip(
                          label: Text(_getStatusName(status)),
                          selected: _statusFilter == status,
                          onSelected: (selected) {
                            setModalState(() {
                              _statusFilter = selected ? status : null;
                            });
                          },
                        );
                      }),
                    ],
                  ),

                  // Date range filter
                  const SizedBox(height: 16),
                  Text(
                    'Date Range',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const SizedBox(height: 8),
                  OutlinedButton.icon(
                    onPressed: () async {
                      Navigator.pop(context);
                      await Future.delayed(const Duration(milliseconds: 300));
                      _setDateRange();
                    },
                    icon: const Icon(Icons.date_range),
                    label: Text(
                      _startDate != null && _endDate != null
                          ? '${_formatDate(_startDate!)} - ${_formatDate(_endDate!)}'
                          : 'Select Date Range',
                    ),
                  ),

                  // Auto-verification setting
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Text(
                        'Auto-Verification',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      const Spacer(),
                      Switch(
                        value: _autoVerificationEnabled,
                        onChanged: (value) {
                          setModalState(() {
                            _autoVerificationEnabled = value;
                          });
                        },
                      ),
                    ],
                  ),
                  Text(
                    'Automatically verify payments after 7 days',
                    style: Theme.of(context).textTheme.bodySmall,
                  ),

                  // Apply button
                  const SizedBox(height: 24),
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: () {
                        Navigator.pop(context);
                        _loadPayments();
                      },
                      child: const Text('Apply Filters'),
                    ),
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  String _getStatusName(PaymentStatus status) {
    switch (status) {
      case PaymentStatus.pending:
        return 'Pending';
      case PaymentStatus.verified:
        return 'Verified';
      case PaymentStatus.rejected:
        return 'Rejected';
    }
  }

  String _getPaymentMethodName(PaymentMethod method) {
    switch (method) {
      case PaymentMethod.cash:
        return 'Cash';
      case PaymentMethod.bankTransfer:
        return 'Bank Transfer';
      case PaymentMethod.mobileMoney:
        return 'Mobile Money';
      case PaymentMethod.check:
        return 'Check';
      case PaymentMethod.other:
        return 'Other';
    }
  }

  IconData _getPaymentMethodIcon(PaymentMethod method) {
    switch (method) {
      case PaymentMethod.cash:
        return Icons.money;
      case PaymentMethod.bankTransfer:
        return Icons.account_balance;
      case PaymentMethod.mobileMoney:
        return Icons.phone_android;
      case PaymentMethod.check:
        return Icons.payment;
      case PaymentMethod.other:
        return Icons.more_horiz;
    }
  }

  Color _getStatusColor(PaymentStatus status) {
    switch (status) {
      case PaymentStatus.pending:
        return Colors.orange;
      case PaymentStatus.verified:
        return Colors.green;
      case PaymentStatus.rejected:
        return Colors.red;
    }
  }
}
