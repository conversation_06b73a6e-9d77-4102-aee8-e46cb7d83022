import '../../models/expense/expense_category_model.dart';
import '../../utils/logger.dart';
import '../supabase_service.dart';

class Logger {
  final String _name;
  
  Logger(this._name);
  
  void error(String message) {
    AppLogger.error('[$_name] $message');
  }
  
  void info(String message) {
    AppLogger.info('[$_name] $message');
  }
  
  void warning(String message) {
    AppLogger.warning('[$_name] $message');
  }
  
  void debug(String message) {
    AppLogger.debug('[$_name] $message');
  }
}

class ExpenseCategoryService {
  final SupabaseService _supabaseService;
  final Logger _logger = Logger('ExpenseCategoryService');

  ExpenseCategoryService(this._supabaseService);

  Future<List<ExpenseCategoryModel>> getCategories({
    bool includeDefault = true,
    String? parentCategoryId,
  }) async {
    try {
      final supabase = _supabaseService.client;
      
      var query = supabase
          .from('expense_categories')
          .select();

      if (!includeDefault) {
        query = query.eq('is_default', false);
      }

      if (parentCategoryId != null) {
        query = query.eq('parent_category_id', parentCategoryId);
      } else {
        query = query.filter('parent_category_id', 'is', null);
      }

      final response = await query.order('name');
      
      return response.map((category) => ExpenseCategoryModel.fromJson(category)).toList();
    } catch (e) {
      _logger.error('Error fetching expense categories: $e');
      rethrow;
    }
  }

  Future<ExpenseCategoryModel> getCategoryById(String id) async {
    try {
      final supabase = _supabaseService.client;
      
      final response = await supabase
          .from('expense_categories')
          .select()
          .eq('id', id)
          .single();
      
      return ExpenseCategoryModel.fromJson(response);
    } catch (e) {
      _logger.error('Error fetching expense category by ID: $e');
      rethrow;
    }
  }

  Future<ExpenseCategoryModel> createCategory(ExpenseCategoryModel category) async {
    try {
      final supabase = _supabaseService.client;
      
      final response = await supabase
          .from('expense_categories')
          .insert(category.toJson())
          .select()
          .single();
      
      return ExpenseCategoryModel.fromJson(response);
    } catch (e) {
      _logger.error('Error creating expense category: $e');
      rethrow;
    }
  }

  Future<ExpenseCategoryModel> updateCategory(ExpenseCategoryModel category) async {
    try {
      final supabase = _supabaseService.client;
      
      // Don't allow updating default categories
      final existingCategory = await getCategoryById(category.id!);
      if (existingCategory.isDefault) {
        throw Exception('Cannot update default expense categories');
      }
      
      final response = await supabase
          .from('expense_categories')
          .update(category.toJson())
          .eq('id', category.id!)
          .select()
          .single();
      
      return ExpenseCategoryModel.fromJson(response);
    } catch (e) {
      _logger.error('Error updating expense category: $e');
      rethrow;
    }
  }

  Future<void> deleteCategory(String id) async {
    try {
      final supabase = _supabaseService.client;
      
      // Don't allow deleting default categories
      final category = await getCategoryById(id);
      if (category.isDefault) {
        throw Exception('Cannot delete default expense categories');
      }
      
      // Check if there are any expenses using this category
      final expensesResponse = await supabase
          .from('expenses')
          .select('id')
          .eq('category_id', id);
      
      if (expensesResponse.isNotEmpty) {
        throw Exception('Cannot delete category that is used by expenses');
      }
      
      await supabase
          .from('expense_categories')
          .delete()
          .eq('id', id);
    } catch (e) {
      _logger.error('Error deleting expense category: $e');
      rethrow;
    }
  }

  Future<void> createDefaultCategories(String userId) async {
    try {
      final supabase = _supabaseService.client;
      final now = DateTime.now();
      
      final defaultCategories = [
        {
          'name': 'Maintenance',
          'description': 'Regular maintenance and repairs',
          'color': 'FF4CAF50', // Green
          'user_id': userId,
          'is_default': true,
          'created_at': now.toIso8601String(),
        },
        {
          'name': 'Utilities',
          'description': 'Electricity, water, gas, etc.',
          'color': 'FF2196F3', // Blue
          'user_id': userId,
          'is_default': true,
          'created_at': now.toIso8601String(),
        },
        {
          'name': 'Taxes',
          'description': 'Property taxes and other tax expenses',
          'color': 'FFF44336', // Red
          'user_id': userId,
          'is_default': true,
          'created_at': now.toIso8601String(),
        },
        {
          'name': 'Insurance',
          'description': 'Property insurance and liability coverage',
          'color': 'FF9C27B0', // Purple
          'user_id': userId,
          'is_default': true,
          'created_at': now.toIso8601String(),
        },
        {
          'name': 'Supplies',
          'description': 'Cleaning supplies and other consumables',
          'color': 'FFFF9800', // Orange
          'user_id': userId,
          'is_default': true,
          'created_at': now.toIso8601String(),
        },
        {
          'name': 'Marketing',
          'description': 'Advertising and promotion expenses',
          'color': 'FF00BCD4', // Cyan
          'user_id': userId,
          'is_default': true,
          'created_at': now.toIso8601String(),
        },
        {
          'name': 'Legal',
          'description': 'Legal fees and professional services',
          'color': 'FF795548', // Brown
          'user_id': userId,
          'is_default': true,
          'created_at': now.toIso8601String(),
        },
        {
          'name': 'Other',
          'description': 'Miscellaneous expenses',
          'color': 'FF607D8B', // Blue Grey
          'user_id': userId,
          'is_default': true,
          'created_at': now.toIso8601String(),
        },
      ];
      
      await supabase
          .from('expense_categories')
          .insert(defaultCategories);
    } catch (e) {
      _logger.error('Error creating default expense categories: $e');
      rethrow;
    }
  }
} 