import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../models/bill/bill.dart';
import '../../services/service_locator.dart';
import '../../utils/form_validators.dart';
import '../../utils/logger.dart';
import '../../widgets/loading_indicator.dart';
import '../../widgets/common/section_header.dart';
import 'group_bill_select_tenants_page.dart';

class GroupBillCreatePage extends StatefulWidget {
  final Bill? existingBill;
  
  const GroupBillCreatePage({
    super.key,
    this.existingBill,
  });

  @override
  State<GroupBillCreatePage> createState() => _GroupBillCreatePageState();
}

class _GroupBillCreatePageState extends State<GroupBillCreatePage> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _amountController = TextEditingController();
  final _notesController = TextEditingController();
  final _billNumberController = TextEditingController();
  
  DateTime _dueDate = DateTime.now().add(const Duration(days: 30));
  BillType _type = BillType.utility;
  RecurrenceType _recurrence = RecurrenceType.monthly;
  bool _isLoading = false;
  bool _isSplitEvenly = true;
  final _amountFocusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    
    // Initialize amount to 0.00 for clean display
    if (_amountController.text.isEmpty) {
      _amountController.text = '0.00';
    }
    
    _amountFocusNode.addListener(() {
      if (_amountFocusNode.hasFocus && _amountController.text == '0.00') {
        _amountController.clear();
      } else if (!_amountFocusNode.hasFocus && _amountController.text.isEmpty) {
        _amountController.text = '0.00';
      }
    });
    
    _generateBillNumber();
    
    // If editing an existing bill, populate the form
    if (widget.existingBill != null) {
      _populateFormWithExistingBill();
    }
  }

  // Generate a bill number using the service
  Future<void> _generateBillNumber() async {
    setState(() {
      _isLoading = true;
    });
    
    try {
      final billService = serviceLocator.billService;
      final billNumbers = await billService.generateGroupBillNumbers(1);
      final billNumber = billNumbers.first;
      
      setState(() {
        _billNumberController.text = billNumber;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      AppLogger.error('Error generating bill number: $e');
    }
  }

  // Populate form with existing bill data if editing
  void _populateFormWithExistingBill() {
    final bill = widget.existingBill!;
    _titleController.text = bill.title;
    _descriptionController.text = bill.description;
    _amountController.text = bill.amount.toString();
    _notesController.text = bill.notes ?? '';
    _billNumberController.text = bill.billNumber ?? '';
    _dueDate = bill.dueDate;
    _type = bill.type;
    _recurrence = bill.recurrence;
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _amountController.dispose();
    _notesController.dispose();
    _billNumberController.dispose();
    _amountFocusNode.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    final currencySymbol = serviceLocator.settingsService.selectedCurrency.symbol;

    return Scaffold(
      appBar: AppBar(
        title: Text(widget.existingBill != null ? 'Edit Group Bill' : 'Create Group Bill'),
      ),
      body: _isLoading
          ? const Center(child: LoadingIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildBillDetailsSection(currencySymbol),
                    const SizedBox(height: 24),
                    _buildSplitOptionsSection(),
                    const SizedBox(height: 32),
                    _buildNextButton(),
                  ],
                ),
              ),
            ),
    );
  }
  
  Widget _buildBillDetailsSection(String currencySymbol) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SectionHeader(title: 'Bill Details'),
        const SizedBox(height: 16),
        
        // Bill Number (Read-only)
        TextFormField(
          controller: _billNumberController,
          decoration: const InputDecoration(
            labelText: 'Bill Number',
            border: OutlineInputBorder(),
          ),
          readOnly: true,
          enabled: false,
        ),
        const SizedBox(height: 16),
        
        // Title
        TextFormField(
          controller: _titleController,
          decoration: const InputDecoration(
            labelText: 'Title*',
            border: OutlineInputBorder(),
          ),
          validator: FormValidators.required,
        ),
        const SizedBox(height: 16),
        
        // Description
        TextFormField(
          controller: _descriptionController,
          decoration: const InputDecoration(
            labelText: 'Description*',
            border: OutlineInputBorder(),
          ),
          validator: (value) => FormValidators.minLength(value, 3),
          maxLines: 2,
        ),
        const SizedBox(height: 16),
        
        // Amount
        TextFormField(
          controller: _amountController,
          focusNode: _amountFocusNode,
          decoration: InputDecoration(
            labelText: 'Amount*',
            border: const OutlineInputBorder(),
            prefixText: '$currencySymbol ',
          ),
          keyboardType: TextInputType.number,
          inputFormatters: [
            FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}')),
          ],
          validator: FormValidators.nonZeroAmount,
          onChanged: (value) {
            // Update view when amount changes
            setState(() {});
          },
        ),
        const SizedBox(height: 16),
        
        // Due Date
        InkWell(
          onTap: () => _selectDueDate(context),
          child: InputDecorator(
            decoration: const InputDecoration(
              labelText: 'Due Date*',
              border: OutlineInputBorder(),
            ),
            child: Text(
              '${_dueDate.day}/${_dueDate.month}/${_dueDate.year}',
            ),
          ),
        ),
        const SizedBox(height: 16),
        
        // Bill Type
        DropdownButtonFormField<BillType>(
          value: _type,
          decoration: const InputDecoration(
            labelText: 'Bill Type*',
            border: OutlineInputBorder(),
          ),
          items: [
            DropdownMenuItem(value: BillType.utility, child: Text('Utility')),
            DropdownMenuItem(value: BillType.maintenance, child: Text('Maintenance')),
            DropdownMenuItem(value: BillType.service, child: Text('Service')),
            DropdownMenuItem(value: BillType.other, child: Text('Other')),
          ],
          onChanged: (value) {
            if (value != null) {
              setState(() {
                _type = value;
              });
            }
          },
        ),
        const SizedBox(height: 16),
        
        // Recurrence Type
        DropdownButtonFormField<RecurrenceType>(
          value: _recurrence,
          decoration: const InputDecoration(
            labelText: 'Recurrence*',
            border: OutlineInputBorder(),
          ),
          items: [
            DropdownMenuItem(value: RecurrenceType.oneTime, child: Text('One-time')),
            DropdownMenuItem(value: RecurrenceType.monthly, child: Text('Monthly')),
            DropdownMenuItem(value: RecurrenceType.quarterly, child: Text('Quarterly')),
            DropdownMenuItem(value: RecurrenceType.yearly, child: Text('Yearly')),
          ],
          onChanged: (value) {
            if (value != null) {
              setState(() {
                _recurrence = value;
              });
            }
          },
        ),
        const SizedBox(height: 16),
        
        // Notes
        TextFormField(
          controller: _notesController,
          decoration: const InputDecoration(
            labelText: 'Notes (Optional)',
            border: OutlineInputBorder(),
          ),
          maxLines: 3,
        ),
      ],
    );
  }
  
  Widget _buildSplitOptionsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SectionHeader(title: 'Bill Split Options'),
        const SizedBox(height: 16),
        
        // Split evenly switch
        SwitchListTile(
          title: const Text('Split amount evenly among tenants'),
          subtitle: Text(
            _isSplitEvenly
                ? 'The total amount will be divided equally among all selected tenants.'
                : 'You can specify a custom amount for each tenant on the this page.',
          ),
          value: _isSplitEvenly,
          onChanged: (value) {
            setState(() {
              _isSplitEvenly = value;
            });
          },
        ),
      ],
    );
  }
  
  Widget _buildNextButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: _proceedToTenantSelection,
        style: ElevatedButton.styleFrom(
          padding: const EdgeInsets.symmetric(vertical: 16),
        ),
        child: const Text('Next: Select Tenants'),
      ),
    );
  }
  
  Future<void> _selectDueDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _dueDate,
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365 * 10)),
    );
    
    if (picked != null && picked != _dueDate) {
      setState(() {
        _dueDate = picked;
      });
    }
  }
  
  void _proceedToTenantSelection() {
    if (!_formKey.currentState!.validate()) {
      return;
    }
    
    // Create a draft bill object with the current details
    final bill = Bill(
      title: _titleController.text.trim(),
      description: _descriptionController.text.trim(),
      amount: double.parse(_amountController.text),
      dueDate: _dueDate,
      type: _type,
      recurrence: _recurrence,
      notes: _notesController.text.trim().isNotEmpty ? _notesController.text.trim() : null,
      billNumber: _billNumberController.text,
    );
    
    // Navigate to tenant selection page
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => GroupBillSelectTenantsPage(
          bill: bill,
          existingBill: widget.existingBill,
          isSplitEvenly: _isSplitEvenly,
        ),
      ),
    );
  }
} 