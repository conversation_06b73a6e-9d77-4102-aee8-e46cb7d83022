import 'package:flutter/material.dart';
import '../../utils/currency_formatter.dart';

class CurrencyAmountDisplay extends StatelessWidget {
  final double amount;
  final TextStyle? style;
  final bool showCurrencySymbol;

  const CurrencyAmountDisplay({
    super.key,
    required this.amount,
    this.style,
    this.showCurrencySymbol = true,
  });

  @override
  Widget build(BuildContext context) {
    return Text(
      showCurrencySymbol
          ? CurrencyFormatter.formatCurrency(amount)
          : CurrencyFormatter.formatNumber(amount),
      style: style,
    );
  }
}

// Example usage in other widgets:
// CurrencyAmountDisplay(amount: 1234.56)
// CurrencyAmountDisplay(amount: 9876.54, showCurrencySymbol: false)
