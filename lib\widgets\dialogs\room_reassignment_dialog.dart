import 'package:flutter/material.dart';
import '../../models/room/room_model.dart';
import '../../models/property/property_model.dart';
import '../../services/service_locator.dart';

/// Dialog for reassigning rooms to different properties when a property is deleted
class RoomReassignmentDialog extends StatefulWidget {
  final List<Room> rooms;
  final String deletingPropertyName;
  final String deletingPropertyId;

  const RoomReassignmentDialog({
    super.key,
    required this.rooms,
    required this.deletingPropertyName,
    required this.deletingPropertyId,
  });

  @override
  State<RoomReassignmentDialog> createState() => _RoomReassignmentDialogState();
}

class _RoomReassignmentDialogState extends State<RoomReassignmentDialog> {
  List<Property> _availableProperties = [];
  final Map<String, String?> _roomAssignments = {}; // roomId -> propertyId
  bool _isLoading = true;
  bool _isProcessing = false;

  @override
  void initState() {
    super.initState();
    _loadAvailableProperties();
    // Initialize room assignments map
    for (final room in widget.rooms) {
      _roomAssignments[room.id] = null;
    }
  }

  Future<void> _loadAvailableProperties() async {
    try {
      final allProperties = await serviceLocator.propertyService.getAllProperties();
      // Filter out the property being deleted
      _availableProperties = allProperties
          .where((property) => property.id != widget.deletingPropertyId)
          .toList();
      
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        _showErrorSnackBar('Failed to load properties: ${e.toString()}');
      }
    }
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
      ),
    );
  }

  bool get _canProceed {
    // Check if all rooms have been assigned to properties
    return _roomAssignments.values.every((propertyId) => propertyId != null);
  }

  Future<void> _reassignRooms() async {
    if (!_canProceed) return;

    setState(() {
      _isProcessing = true;
    });

    try {
      // Reassign each room to its selected property
      for (final room in widget.rooms) {
        final newPropertyId = _roomAssignments[room.id];
        if (newPropertyId != null && newPropertyId != room.propertyId) {
          await serviceLocator.roomService.updateRoom(
            id: room.id,
            propertyId: newPropertyId,
          );
        }
      }

      if (mounted) {
        _showSuccessSnackBar('Rooms successfully reassigned');
        Navigator.of(context).pop(true); // Return true to indicate success
      }
    } catch (e) {
      if (mounted) {
        _showErrorSnackBar('Failed to reassign rooms: ${e.toString()}');
      }
    } finally {
      if (mounted) {
        setState(() {
          _isProcessing = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Row(
        children: [
          Icon(
            Icons.warning_amber_rounded,
            color: Colors.orange,
            size: 28,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              'Reassign Rooms',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
      content: _isLoading
          ? const SizedBox(
              height: 100,
              child: Center(child: CircularProgressIndicator()),
            )
          : _buildContent(),
      actions: [
        TextButton(
          onPressed: _isProcessing ? null : () => Navigator.of(context).pop(false),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: _isProcessing || !_canProceed ? null : _reassignRooms,
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.orange,
            foregroundColor: Colors.white,
          ),
          child: _isProcessing
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                )
              : const Text('Reassign Rooms'),
        ),
      ],
    );
  }

  Widget _buildContent() {
    if (_availableProperties.isEmpty) {
      return Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.error_outline,
            size: 48,
            color: Colors.red,
          ),
          const SizedBox(height: 16),
          Text(
            'No Available Properties',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'You need to create at least one other property before you can delete "${widget.deletingPropertyName}".',
            textAlign: TextAlign.center,
            style: TextStyle(
              color: Colors.grey[600],
            ),
          ),
        ],
      );
    }

    return SizedBox(
      width: double.maxFinite,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'The property "${widget.deletingPropertyName}" contains ${widget.rooms.length} room(s). Please assign each room to a new property:',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey[700],
            ),
          ),
          const SizedBox(height: 16),
          Container(
            constraints: const BoxConstraints(maxHeight: 400),
            child: SingleChildScrollView(
              child: Column(
                children: widget.rooms.map((room) => _buildRoomAssignmentCard(room)).toList(),
              ),
            ),
          ),
          if (_roomAssignments.values.any((propertyId) => propertyId == null))
            Padding(
              padding: const EdgeInsets.only(top: 16),
              child: Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.orange.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.orange.withValues(alpha: 0.3)),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.info_outline,
                      color: Colors.orange,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'Please assign all rooms to properties before proceeding.',
                        style: TextStyle(
                          color: Colors.orange[800],
                          fontSize: 14,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildRoomAssignmentCard(Room room) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.meeting_room,
                  color: Colors.blue,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    room.name,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: _getStatusColor(room.occupancyStatus).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: _getStatusColor(room.occupancyStatus).withValues(alpha: 0.3),
                    ),
                  ),
                  child: Text(
                    room.occupancyStatus.displayName,
                    style: TextStyle(
                      fontSize: 12,
                      color: _getStatusColor(room.occupancyStatus),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              'Rental Price: \$${room.rentalPrice.toStringAsFixed(2)}',
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 14,
              ),
            ),
            const SizedBox(height: 12),
            DropdownButtonFormField<String>(
              decoration: const InputDecoration(
                labelText: 'Assign to Property',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.home),
              ),
              value: _roomAssignments[room.id],
              items: _availableProperties.map((property) {
                return DropdownMenuItem<String>(
                  value: property.id,
                  child: Text(property.name),
                );
              }).toList(),
              onChanged: (value) {
                setState(() {
                  _roomAssignments[room.id] = value;
                });
              },
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please select a property';
                }
                return null;
              },
            ),
          ],
        ),
      ),
    );
  }

  Color _getStatusColor(RoomOccupancyStatus status) {
    switch (status) {
      case RoomOccupancyStatus.vacant:
        return Colors.green;
      case RoomOccupancyStatus.occupied:
        return Colors.blue;
      case RoomOccupancyStatus.reserved:
        return Colors.orange;
      case RoomOccupancyStatus.maintenance:
        return Colors.red;
    }
  }
}
