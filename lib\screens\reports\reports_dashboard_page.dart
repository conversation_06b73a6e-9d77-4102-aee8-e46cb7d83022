import 'package:flutter/material.dart';
import '../../widgets/navigation/app_drawer.dart';

class ReportsDashboardPage extends StatelessWidget {
  const ReportsDashboardPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Reports'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      drawer: const AppDrawer(),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Financial Reports',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildReportGrid(context, [
              _ReportItem(
                title: 'Income Summary',
                icon: Icons.attach_money,
                color: Colors.green,
                route: '/reports/income-summary',
                description: 'Overview of all income from your properties',
                isImplemented: true,
              ),
              _ReportItem(
                title: 'Rent Collection',
                icon: Icons.payments,
                color: Colors.blue,
                route: '/reports/rent-collection',
                description: 'Track tenant rent payments and outstanding balances',
                isImplemented: true,
              ),
              _ReportItem(
                title: 'Expense Tracker',
                icon: Icons.receipt_long,
                color: Colors.orange,
                route: '/reports/expense-tracker',
                description: 'Monitor where your money is being spent',
                isImplemented: true,
              ),
              _ReportItem(
                title: 'Profit Report',
                icon: Icons.trending_up,
                color: Colors.purple,
                route: '/reports/profit',
                description: 'See the bottom line for your properties',
                isImplemented: true,
              ),
            ]),
            
            const SizedBox(height: 32),
            const Text(
              'Property Reports',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildReportGrid(context, [
              _ReportItem(
                title: 'Occupancy Summary',
                icon: Icons.home_work,
                color: Colors.indigo,
                route: '/reports/occupancy',
                description: 'See which rooms are occupied or vacant',
                isImplemented: true,
              ),
              _ReportItem(
                title: 'Lease Expiration',
                icon: Icons.calendar_today,
                color: Colors.red,
                route: '/reports/lease-expiration',
                description: 'View upcoming lease expirations',
                isImplemented: true,
              ),
            ]),
          ],
        ),
      ),
    );
  }

  Widget _buildReportGrid(BuildContext context, List<_ReportItem> reports) {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 1.1,
        crossAxisSpacing: 10,
        mainAxisSpacing: 10,
      ),
      itemCount: reports.length,
      itemBuilder: (context, index) {
        final report = reports[index];
        return _buildReportCard(context, report);
      },
    );
  }

  Widget _buildReportCard(BuildContext context, _ReportItem report) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: () {
          if (report.isImplemented) {
            Navigator.pushNamed(context, report.route);
          } else {
            // Show a "coming soon" message for unimplemented reports
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('${report.title} report coming soon!'),
                duration: const Duration(seconds: 2),
              ),
            );
          }
        },
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(12.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                report.icon,
                size: 40,
                color: report.color,
              ),
              const SizedBox(height: 8),
              Text(
                report.title,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 4),
              Text(
                report.description,
                style: const TextStyle(
                  fontSize: 12,
                  color: Colors.grey,
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class _ReportItem {
  final String title;
  final IconData icon;
  final Color color;
  final String route;
  final String description;
  final bool isImplemented;

  _ReportItem({
    required this.title,
    required this.icon,
    required this.color,
    required this.route,
    required this.description,
    this.isImplemented = false,
  });
} 
