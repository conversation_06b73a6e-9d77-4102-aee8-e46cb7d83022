# Tenanta - Property Management Application

A comprehensive property management application designed to help landlords and property managers efficiently manage their properties, rooms, tenants, billing, and analytics.

## Overview

Tenanta streamlines property management tasks by providing tools for managing multiple properties, rooms, utilities, and tenant interactions. The application helps property owners track occupancy, manage rental income, handle bills and payments, maintain detailed records, and generate insightful reports for better decision-making.

## Implemented Features

### Property Management
- Create, update, and delete properties
- Track property details (address, name, type)
- View property statistics (occupancy rates, income)
- Property image management
- Location mapping integration
- Multiple property dashboard
- Advanced search and filtering
- Batch property updates
- Property performance metrics

### Room Management
- Add and manage rooms within properties
- Track room occupancy status (vacant, occupied, reserved, maintenance)
- Set and update rental prices
- Associate amenities with rooms
- Room availability calendar
- Room type categorization
- Room image gallery
- Maintenance scheduling
- Room history tracking
- Batch room updates

### Tenant Management
- Comprehensive tenant profiles
- Tenant room assignment and tracking
- Tenant contact information management
- Tenant search and filtering capabilities
- Tenant history and activity logs
- Tenant dashboard with billing overview
- Emergency contact information
- Lease start and end date tracking
- Tenant status management (active, pending, moved out)
- Tenant bills association

### Billing System
- Create and manage individual and group bills
- Bill categorization (rent, utility, maintenance, service)
- Bill recurrence settings (one-time, monthly, quarterly, yearly)
- Split bills among multiple tenants
- Track payment status (pending, paid, overdue)
- Record full and partial payments
- Utility bill management with meter readings
- Bill components and itemization
- Unique bill numbering system
- Bill search and filtering
- Bill templates for recurring charges
- Automated bill generation based on templates

### Payment System
- Automatic receipt number generation (sequential REC-1000001 format)
- Record payments for individual and multiple bills
- Payment verification workflow
- Bulk payment verification
- Auto-verification of payments after 7 days
- Payment history tracking and filtering
- Payment method tracking (cash, bank transfer, mobile money)
- Payment status management (pending, verified, rejected)
- Partial payment support
- Payment analytics and summaries

### Reports and Analytics
- Income summary reports with monthly breakdowns
- Pending bills tracking and visualization
- Income categorization by type (rent, utility, other)
- Payment method analysis
- Property-based income reports
- Custom date range selection for reports
- Visual data representation with charts and progress indicators
- Bill type breakdown and analysis
- Financial performance metrics
- Exportable report data
- Enhanced rent collection reporting
- Property-specific filtering in reports
- Modern date range selector (This Month, Last Month, Custom Range)
- Improved UI/UX for report filters and displays
- Interactive data visualization
- Expense tracking reports with category and property breakdowns
- Expense analytics with visual representations
- Occupancy reports with property and room details
- Occupancy trend analysis with historical data
- Property performance comparison
- Enhanced 3-month occupancy forecast with improved accuracy
- Optimized occupancy chart layout and responsiveness
- Real-time occupancy rate calculations based on active tenants
- Predictive vacancy tracking based on lease end dates
- Support Ticket Report with real-time status updates
- Maintenance request analytics and tracking
- Ticket status monitoring and reporting

### Expense Tracking
- Comprehensive expense recording and management
- Expense categorization with color-coded categories
- Expense association with properties and rooms
- Vendor management for expense tracking
- Recurring expense support (weekly, monthly, quarterly, annual)
- Expense reports with filtering by date, category, property, and room
- Visual expense breakdowns by category and property
- Expense summary cards with total calculations
- Expense detail tracking with receipt numbers
- Interactive expense filtering with intuitive UI
- Pagination support for large expense lists (12 items per page)
- Smart pagination with page navigation controls
- Expense list performance optimization

### Amenities System
- Predefined amenities catalog
- Custom amenities for specific rooms
- Track amenities across properties

### Database Integration
- Secure data management with Row Level Security
- User-specific data access
- Optimized database views and queries
- Activity logging and history tracking

### User Authentication
- Secure login and registration
- User-specific data isolation
- Role-based access control
- Password reset functionality
- Session management
- Remember me functionality (default enabled)
- Comprehensive Terms & Conditions and Privacy Policy
- Legal document integration in auth flow
- Mandatory acceptance of legal terms during signup
- Professional legal document display screens
- GDPR-compliant privacy policy with user rights
- Legal document access from settings page

### UI/UX Features
- Multi-language support (English/Kiswahili)
- Currency format customization
- Dark/Light theme switching
- Responsive layout design
- Interactive property maps
- Improved navigation system
- Quick actions menu
- Performance optimizations
- Copy to clipboard functionality
- Modern card-based UI design
- Consistent visual styling
- Interactive filter chips for data filtering
- Visual data representation with progress bars and charts
- Adaptive layouts for different screen sizes
- Intuitive navigation patterns
- Error handling with user-friendly messages
- Enhanced tenant details page with modern UI
- Gradient backgrounds and professional styling
- Improved pagination controls for large datasets
- Mobile-optimized legal document viewers
- Professional settings page with legal document access

## Roadmap

### Implemented ✅

- Core property management functionality
- Room tracking and management
- Amenities system
- Database structure and security
- Basic authentication and user management
- Language settings (English and Kiswahili support)
- User preferences and configuration
- Tenant profiles and management
- Property image uploads
- Multiple property tracking
- Room status management
- Property location mapping
- Room calendar management
- Property image galleries
- Interactive map integration
- Dark/Light theme support
- Multi-language system
- Room type categorization
- Dashboard analytics
- Advanced property search
- Maintenance scheduling
- Quick actions menu
- Performance improvements
- Batch update capabilities
- Tenant search functionality
- Property analytics dashboard
- Advanced room filtering
- Property sharing features
- Enhanced security measures
- Email notification system
- User profile management
- Property preferences settings
- Bill management system
- Group and individual billing
- Bill payment tracking
- Currency customization
- Bill number generation
- Utility bill management
- Tenant bill association
- Automatic receipt number generation
- Payment verification system
- Bulk payment verification
- Auto-verification after 7 days
- Payment history and filtering
- Bill templates
- Automated bill generation
- Income summary reports and analytics
- Monthly financial tracking with pending bills
- Income source breakdown visualization
- Custom date range reporting
- Enhanced rent collection reporting system
- Property-based report filtering
- Modern report interface with intuitive controls
- Interactive reporting dashboard
- Comprehensive expense tracking system
- Expense categorization with visual indicators
- Expense reporting with interactive filters
- Expense analytics with category and property breakdowns
- Recurring expense management
- Expense association with properties and rooms
- Occupancy reporting and analytics
- Room occupancy status tracking
- Occupancy trend visualization
- Property occupancy comparison
- Enhanced 3-month occupancy forecasting
- Optimized occupancy visualization layout
- Accurate occupancy rate calculations
- Predictive vacancy tracking system
- Lease expiration reporting with contact information display
- Improved BuildContext handling in async operations
- Optimized contact information display
- Enhanced error handling in reports
- Streamlined tenant communication interface
- Comprehensive Terms & Conditions and Privacy Policy implementation
- Legal document display screens with professional formatting
- Mandatory legal acceptance during user registration
- Legal document access integration in settings page
- Enhanced tenant details page with modern gradient UI
- Improved top bar styling with professional appearance
- Expense list pagination with 12 items per page optimization
- Support Ticket Report with real-time status fetching
- Database query optimization to prevent PGRST116 errors
- Improved error handling across all service methods
- Remember me functionality default enabled for better UX

### Ongoing 🔄

#### Settings Module
- ✅ Language switching (English/Kiswahili)
- ✅ Theme customization
- ✅ Profile management
- ✅ Property preferences
- ✅ Quick actions configuration
- ✅ Basic notifications
- ✅ Email notifications
- ✅ Currency format settings
- Custom notification rules
- Notification preferences dashboard

#### Tenant Management
- ✅ Basic tenant profiles
- ✅ Tenant room assignment
- ✅ Tenant contact info
- ✅ Tenant search and filtering
- ✅ Basic tenant history
- ✅ Tenant dashboard
- ✅ Tenant bill management
- ✅ Contact information display
- Digital lease agreements
- Advanced tenant analytics
- Tenant communication system
- Tenant document storage

#### Billing System
- ✅ Bill creation and management
- ✅ Group billing functionality
- ✅ Bill payment tracking
- ✅ Bill categorization
- ✅ Bill recurrence settings
- ✅ Bill components and itemization
- ✅ Automated bill generation
- ✅ Bill templates

#### Payment System
- ✅ Automatic receipt generation
- ✅ Payment tracking and history
- ✅ Payment verification workflow
- ✅ Bulk payment processing
- ✅ Auto-verification system
- Mobile money integration
- ✅ Payment analytics
- ✅ Automated invoicing

#### Reports and Analytics
- ✅ Income summary reports
- ✅ Monthly income tracking
- ✅ Pending bills analysis
- ✅ Income type categorization
- ✅ Property-based income reports
- ✅ Payment method analysis
- ✅ Custom date range filtering
- ✅ Bill type breakdown visualization
- ✅ Financial performance metrics
- ✅ Enhanced rent collection reporting
- ✅ Property-specific report filtering
- ✅ Expense tracking reports
- ✅ Expense category analysis
- ✅ Expense vs. property breakdown
- ✅ Occupancy reports and analytics
- ✅ Occupancy trend visualization
- ✅ Property occupancy comparison
- ✅ Enhanced 3-month occupancy forecasting
- ✅ Optimized occupancy visualization layout
- ✅ Accurate occupancy rate calculations
- ✅ Predictive vacancy tracking system
- ✅ Lease expiration reporting
- ✅ Tenant communication system
- ✅ Digital lease agreements
- ✅ Advanced tenant analytics
- ✅ Tenant communication history

#### User Interface
- ✅ Responsive design
- ✅ Dark/Light themes
- ✅ Multi-language support
- ✅ Interactive filters
- ✅ Modern card layouts
- ✅ Error handling
- ✅ Loading indicators
- ✅ Contact information display
- ✅ Advanced search features

#### Expense Tracking
- ✅ Comprehensive expense recording
- ✅ Expense categorization
- ✅ Expense reporting
- ✅ Expense filtering by property and room
- ✅ Expense analytics with visual breakdowns
- ✅ Recurring expense management
- ✅ Expense association with properties  
- Expense vs. income analysis
- Expense forecasting

#### Occupancy Management
- ✅ Room occupancy status tracking
- ✅ Property occupancy rates
- ✅ Occupancy trend analysis
- ✅ Occupancy reporting
- ✅ Room details view
- ✅ Seasonal occupancy analysis
- ✅ Occupancy forecasting
- ✅ Vacancy cost analysis

### Coming Soon 🚀

#### Payment System Enhancements
- ✅ Payment receipt PDF generation on Tenant App is working
- Payment reminder notifications
- ✅ Advanced payment analytics

#### Mobile Enhancements
- Offline mode
- Push notifications
- Location services
- Biometric authentication
- Camera integration for documentation
- QR code scanning for quick actions

#### Advanced Analytics
- ✅ Revenue forecasting
- ✅ Occupancy trends
- ✅ Bill payment trends
- ✅ Tenant payment history analytics
- ✅ Income source breakdown
- ✅ Monthly financial performance
- ✅ Pending bills tracking
- ✅ Enhanced rent collection analytics
- ✅ Property-based reporting
- ✅ Expense tracking and analysis
- ✅ Occupancy reporting and trend analysis
- Expense vs. income ratio analysis
- Property ROI calculations
- ✅ Tenant retention analysis

#### New Features
- ✅ Property maintenance logs
- ✅ Expense tracking
- ✅ Occupancy reporting
- ✅ Invoice generation on tenant app
- ✅ Tenant portal access On Tenant App Residence app
- Lease agreement templates
- Tenant screening tools
- Property inspection checklists

#### Tickets System
- Ticket creation and assignment
- Ticket status tracking (open, in progress, closed)
- Ticket list and filtering
- Ticket details and management
- Ticket priority levels
- Ticket comments and attachments
- Ticket analytics and reporting
- Integration with maintenance scheduling
- Automated ticket routing

#### Resident Hub
- ✅ Tenant self-service portal
- Online rent payments
- Maintenance request submission
- ✅ Document access and downloads
- Community announcements
- Tenant-landlord messaging

## Technology Stack

- **Frontend**: Flutter (cross-platform app development)
- **Backend**: Supabase (PostgreSQL database with API)
- **Authentication**: Supabase Auth
- **Storage**: Supabase Storage
- **Database**: PostgreSQL with Row Level Security
- **State Management**: Provider pattern
- **Localization**: Flutter intl package
- **UI Framework**: Material Design

## Getting Started

### Prerequisites

- Flutter SDK (latest version)
- Supabase account and project
- Dart SDK

### Installation

1. Clone the repository:

   ```bash
   git clone https://github.com/Thiararapeter/Tenanta-App.git
   ```

2. Navigate to the project directory:

   ```bash
   cd Tenanta-App
   ```

3. Install dependencies:

   ```bash
   flutter pub get
   ```

4. Configure Environment Variables:
   - Copy `.env.example` to `.env`:
     ```bash
     cp .env.example .env
     ```
   - Edit `.env` and add your Supabase credentials:
     ```
     SUPABASE_URL=your_supabase_project_url
     SUPABASE_ANON_KEY=your_supabase_anon_key
     ```

5. Configure Supabase:
   - Run the database migrations in the `/migrations` folder

6. Run the app:

   ```bash
   flutter run
   ```

## Environment Configuration

Tenanta uses environment variables to securely manage sensitive configuration like API keys. The configuration is handled through the `EnvironmentConfig` class.

### Environment Variables

| Variable | Description | Required |
|----------|-------------|----------|
| `SUPABASE_URL` | Your Supabase project URL | Yes |
| `SUPABASE_ANON_KEY` | Your Supabase anonymous key | Yes |

### Security Notes

- The `.env` file is automatically excluded from version control via `.gitignore`
- Never commit sensitive credentials to the repository
- Use `.env.example` as a template for required environment variables
- The app will throw descriptive errors if required environment variables are missing

### Environment Configuration Class

The `EnvironmentConfig` class provides:
- Automatic loading of environment variables from `.env` file
- Validation of required configuration
- Secure access to sensitive values
- Error handling for missing configuration

## Contributing

Contributions to Tenanta are welcome! Please follow these steps:

1. Fork the repository [https://github.com/Thiararapeter/Tenanta-App](https://github.com/Thiararapeter/Tenanta-App)
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Reporting Issues

Found a bug or have a suggestion? Please report it by creating an issue at:
[https://github.com/Thiararapeter/Tenanta-App/issues](https://github.com/Thiararapeter/Tenanta-App/issues)

### Project Repository

View the full project, contribute code, and more at our GitHub repository:
[https://github.com/Thiararapeter/Tenanta-App](https://github.com/Thiararapeter/Tenanta-App)

## License

This project is licensed under the MIT License - see the LICENSE file for details.