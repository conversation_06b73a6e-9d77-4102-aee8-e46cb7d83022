import 'package:flutter/material.dart';
import '../../models/community_notice/community_notice.dart';
import '../../services/community_notice/community_notice_service.dart';
import '../../screens/community_notices/community_notices_screen.dart';
import '../../screens/community_notices/notice_detail_screen.dart';
import '../../screens/community_notices/create_notice_screen.dart';

class PropertyNoticesWidget extends StatefulWidget {
  final String propertyId;
  final String? propertyName;
  final bool showCreateButton;
  final int maxNotices;

  const PropertyNoticesWidget({
    super.key,
    required this.propertyId,
    this.propertyName,
    this.showCreateButton = true,
    this.maxNotices = 3,
  });

  @override
  State<PropertyNoticesWidget> createState() => _PropertyNoticesWidgetState();
}

class _PropertyNoticesWidgetState extends State<PropertyNoticesWidget> {
  final CommunityNoticeService _noticeService = CommunityNoticeService();
  List<CommunityNotice> _notices = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadNotices();
  }

  Future<void> _loadNotices() async {
    try {
      final notices = await _noticeService.getNoticesForProperty(widget.propertyId);
      setState(() {
        _notices = notices.take(widget.maxNotices).toList();
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.campaign, color: Colors.blue),
                const SizedBox(width: 8),
                const Text(
                  'Community Notices',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                if (widget.showCreateButton)
                  IconButton(
                    icon: const Icon(Icons.add),
                    onPressed: _createNotice,
                    tooltip: 'Create Notice',
                  ),
                TextButton(
                  onPressed: _viewAllNotices,
                  child: const Text('View All'),
                ),
              ],
            ),
            const SizedBox(height: 12),
            _buildContent(),
          ],
        ),
      ),
    );
  }

  Widget _buildContent() {
    if (_isLoading) {
      return const Center(
        child: Padding(
          padding: EdgeInsets.all(20),
          child: CircularProgressIndicator(),
        ),
      );
    }

    if (_notices.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            Icon(
              Icons.campaign_outlined,
              size: 48,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 8),
            Text(
              'No notices yet',
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 16,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              'Create your first community notice',
              style: TextStyle(
                color: Colors.grey[500],
                fontSize: 14,
              ),
            ),
            if (widget.showCreateButton) ...[
              const SizedBox(height: 12),
              ElevatedButton.icon(
                onPressed: _createNotice,
                icon: const Icon(Icons.add),
                label: const Text('Create Notice'),
              ),
            ],
          ],
        ),
      );
    }

    return Column(
      children: [
        ..._notices.map((notice) => _buildNoticeItem(notice)),
        if (_notices.length >= widget.maxNotices) ...[
          const SizedBox(height: 8),
          Center(
            child: TextButton(
              onPressed: _viewAllNotices,
              child: const Text('View All Notices'),
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildNoticeItem(CommunityNotice notice) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: InkWell(
        onTap: () => _viewNoticeDetail(notice),
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey[300]!),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          notice.title,
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 14,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        if (notice.isCombinedPropertyNotice) ...[
                          const SizedBox(height: 2),
                          Row(
                            children: [
                              Icon(
                                Icons.business_outlined,
                                size: 12,
                                color: Colors.blue[600],
                              ),
                              const SizedBox(width: 4),
                              Text(
                                'Multi-property notice (${notice.propertyCount} properties)',
                                style: TextStyle(
                                  color: Colors.blue[600],
                                  fontSize: 11,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ],
                    ),
                  ),
                  const SizedBox(width: 8),
                  _buildPriorityChip(notice.priority),
                ],
              ),
              const SizedBox(height: 6),
              
              Row(
                children: [
                  _buildTypeChip(notice.type),
                  const Spacer(),
                  Text(
                    _formatDate(notice.createdAt),
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 6),
              
              Text(
                notice.content,
                style: TextStyle(
                  color: Colors.grey[700],
                  fontSize: 13,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              
              if (notice.expiresAt != null) ...[
                const SizedBox(height: 6),
                Row(
                  children: [
                    Icon(
                      notice.isExpired ? Icons.error : Icons.schedule,
                      size: 14,
                      color: notice.isExpired ? Colors.red : Colors.orange,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      notice.isExpired 
                          ? 'Expired'
                          : 'Expires ${_formatDate(notice.expiresAt!)}',
                      style: TextStyle(
                        color: notice.isExpired ? Colors.red : Colors.orange,
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTypeChip(NoticeType type) {
    Color color;
    switch (type) {
      case NoticeType.emergency:
        color = Colors.red;
        break;
      case NoticeType.maintenance:
        color = Colors.orange;
        break;
      case NoticeType.event:
        color = Colors.blue;
        break;
      case NoticeType.policy:
        color = Colors.purple;
        break;
      default:
        color = Colors.grey;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Text(
        type.displayName,
        style: TextStyle(
          color: color,
          fontSize: 10,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildPriorityChip(NoticePriority priority) {
    Color color;
    switch (priority) {
      case NoticePriority.urgent:
        color = Colors.red;
        break;
      case NoticePriority.high:
        color = Colors.orange;
        break;
      case NoticePriority.medium:
        color = Colors.blue;
        break;
      default:
        color = Colors.grey;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Text(
        priority.displayName,
        style: TextStyle(
          color: color,
          fontSize: 10,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  void _createNotice() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CreateNoticeScreen(
          propertyId: widget.propertyId,
        ),
      ),
    );

    if (result == true) {
      _loadNotices();
    }
  }

  void _viewAllNotices() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CommunityNoticesScreen(
          propertyId: widget.propertyId,
        ),
      ),
    );
  }

  void _viewNoticeDetail(CommunityNotice notice) async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => NoticeDetailScreen(notice: notice),
      ),
    );

    if (result == true) {
      _loadNotices();
    }
  }
}
