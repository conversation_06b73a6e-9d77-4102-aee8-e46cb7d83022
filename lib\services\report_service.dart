import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/payment/payment_model.dart';
import '../models/expense/expense_model.dart';
import '../models/room/room_model.dart';
import '../models/tenant/tenant.dart';
import '../utils/logger.dart';

class ReportService {
  final SupabaseClient _client;

  ReportService(this._client);

  // Get income summary data
  Future<Map<String, dynamic>> getIncomeSummary({
    DateTime? startDate,
    DateTime? endDate,
    String? propertyId,
  }) async {
    try {
      // Set default date range if not provided
      startDate ??= DateTime(DateTime.now().year, DateTime.now().month, 1);
      endDate ??= DateTime.now();

      // Query payments within date range
      var query = _client
          .from('payments')
          .select('*, tenant:tenants(id, first_name, last_name)')
          .gte('payment_date', startDate.toIso8601String())
          .lte('payment_date', endDate.toIso8601String());

      // Filter by property if provided
      if (propertyId != null) {
        // We need to join with bills to filter by property
        query = _client
            .from('payments')
            .select(
              '*, tenant:tenants(id, first_name, last_name), bills!bills(property_id)',
            )
            .gte('payment_date', startDate.toIso8601String())
            .lte('payment_date', endDate.toIso8601String())
            .eq('bills.property_id', propertyId);
      }

      final response = await query;
      final List<Payment> payments = (response as List<dynamic>)
          .map((data) => Payment.fromJson(data))
          .toList();

      // Calculate total income
      final totalIncome = payments.fold<double>(
        0,
        (sum, payment) => sum + payment.amount,
      );

      // Group by month
      final incomeByMonth = <int, double>{};
      for (final payment in payments) {
        final month = payment.paymentDate.month;
        incomeByMonth[month] = (incomeByMonth[month] ?? 0) + payment.amount;
      }

      // Group by payment method
      final incomeByMethod = <String, double>{};
      for (final payment in payments) {
        final methodName = payment.method.name;
        incomeByMethod[methodName] = (incomeByMethod[methodName] ?? 0) + payment.amount;
      }

      // Return the summary data
      return {
        'totalIncome': totalIncome,
        'incomeByMonth': incomeByMonth,
        'incomeByMethod': incomeByMethod,
        'payments': payments,
      };
    } catch (e) {
      AppLogger.error('Error getting income summary: $e');
      rethrow;
    }
  }

  // Get expense summary data
  Future<Map<String, dynamic>> getExpenseSummary({
    DateTime? startDate,
    DateTime? endDate,
    String? propertyId,
    String? categoryId,
  }) async {
    try {
      // Set default date range if not provided
      startDate ??= DateTime(DateTime.now().year, DateTime.now().month, 1);
      endDate ??= DateTime.now();

      // Query expenses within date range
      var query = _client
          .from('expenses')
          .select('*, category:expense_categories(id, name, color)')
          .gte('date', startDate.toIso8601String())
          .lte('date', endDate.toIso8601String());

      // Filter by property if provided
      if (propertyId != null) {
        query = query.eq('property_id', propertyId);
      }

      // Filter by category if provided
      if (categoryId != null) {
        query = query.eq('category_id', categoryId);
      }

      final response = await query;
      final List<ExpenseModel> expenses = (response as List<dynamic>)
          .map((data) => ExpenseModel.fromJson(data))
          .toList();

      // Calculate total expenses
      final totalExpenses = expenses.fold<double>(
        0,
        (sum, expense) => sum + expense.amount,
      );

      // Group by month
      final expensesByMonth = <int, double>{};
      for (final expense in expenses) {
        final month = expense.date.month;
        expensesByMonth[month] = (expensesByMonth[month] ?? 0) + expense.amount;
      }

      // Group by category
      final expensesByCategory = <String, double>{};
      for (final expense in expenses) {
        final categoryName = expense.categoryName ?? 'Uncategorized';
        expensesByCategory[categoryName] = 
            (expensesByCategory[categoryName] ?? 0) + expense.amount;
      }

      // Return the summary data
      return {
        'totalExpenses': totalExpenses,
        'expensesByMonth': expensesByMonth,
        'expensesByCategory': expensesByCategory,
        'expenses': expenses,
      };
    } catch (e) {
      AppLogger.error('Error getting expense summary: $e');
      rethrow;
    }
  }

  // Get occupancy summary data
  Future<Map<String, dynamic>> getOccupancySummary({
    String? propertyId,
  }) async {
    try {
      // Query rooms
      var query = _client.from('rooms').select('*, property:properties(id, name)');

      // Filter by property if provided
      if (propertyId != null) {
        query = query.eq('property_id', propertyId);
      }

      final response = await query;
      final List<Room> rooms = (response as List<dynamic>)
          .map((data) => Room.fromJson(data))
          .toList();

      // Calculate occupancy statistics
      final totalRooms = rooms.length;
      final occupiedRooms = rooms.where((room) => room.occupancyStatus == RoomOccupancyStatus.occupied).length;
      final vacantRooms = rooms.where((room) => room.occupancyStatus == RoomOccupancyStatus.vacant).length;
      final reservedRooms = rooms.where((room) => room.occupancyStatus == RoomOccupancyStatus.reserved).length;
      
      final occupancyRate = totalRooms > 0 ? (occupiedRooms / totalRooms) * 100 : 0;

      // Group by property
      final roomsByProperty = <String, List<Room>>{};
      for (final room in rooms) {
        // Since property is not directly accessible in Room model, extract from JSON data
        final propertyData = response.firstWhere((r) => r['id'] == room.id, orElse: () => {})['property'];
        final propertyName = propertyData?['name'] ?? 'Unknown';
        roomsByProperty[propertyName] = (roomsByProperty[propertyName] ?? [])..add(room);
      }

      // Calculate occupancy rate by property
      final occupancyByProperty = <String, double>{};
      for (final entry in roomsByProperty.entries) {
        final propertyRooms = entry.value.length;
        final propertyOccupied = entry.value.where((room) => room.occupancyStatus == RoomOccupancyStatus.occupied).length;
        occupancyByProperty[entry.key] = propertyRooms > 0 ? (propertyOccupied / propertyRooms) * 100 : 0;
      }

      // Return the summary data
      return {
        'totalRooms': totalRooms,
        'occupiedRooms': occupiedRooms,
        'vacantRooms': vacantRooms,
        'reservedRooms': reservedRooms,
        'occupancyRate': occupancyRate,
        'occupancyByProperty': occupancyByProperty,
        'rooms': rooms,
      };
    } catch (e) {
      AppLogger.error('Error getting occupancy summary: $e');
      rethrow;
    }
  }

  // Get lease expiration data
  Future<Map<String, dynamic>> getLeaseExpirations({
    int? daysAhead = 90,
  }) async {
    try {
      final now = DateTime.now();
      final futureDate = now.add(Duration(days: daysAhead ?? 90));

      // Query tenants with lease end dates within the specified range
      final response = await _client
          .from('tenants')
          .select('*, room:rooms(id, name, property_id, property:properties(id, name))')
          .gte('lease_end_date', now.toIso8601String())
          .lte('lease_end_date', futureDate.toIso8601String());

      final List<Tenant> tenants = (response as List<dynamic>)
          .map((data) => Tenant.fromJson(data))
          .toList();

      // Group by month
      final expirationsByMonth = <int, List<Tenant>>{};
      for (final tenant in tenants) {
        if (tenant.leaseEndDate != null) {
          final month = tenant.leaseEndDate!.month;
          expirationsByMonth[month] = (expirationsByMonth[month] ?? [])..add(tenant);
        }
      }

      // Group by property
      final expirationsByProperty = <String, List<Tenant>>{};
      for (final tenant in tenants) {
        // Since tenant.room is not directly accessible, extract from JSON data
        final roomData = response.firstWhere((t) => t['id'] == tenant.id, orElse: () => {})['room'];
        final propertyData = roomData?['property'];
        final propertyName = propertyData?['name'] ?? 'Unknown';
        expirationsByProperty[propertyName] = (expirationsByProperty[propertyName] ?? [])..add(tenant);
      }

      // Group by days remaining
      final next30Days = <Tenant>[];
      final next60Days = <Tenant>[];
      final next90Days = <Tenant>[];

      for (final tenant in tenants) {
        if (tenant.leaseEndDate != null) {
          final daysRemaining = tenant.leaseEndDate!.difference(now).inDays;
          if (daysRemaining <= 30) {
            next30Days.add(tenant);
          } else if (daysRemaining <= 60) {
            next60Days.add(tenant);
          } else if (daysRemaining <= 90) {
            next90Days.add(tenant);
          }
        }
      }

      // Return the summary data
      return {
        'totalExpirations': tenants.length,
        'next30Days': next30Days,
        'next60Days': next60Days,
        'next90Days': next90Days,
        'expirationsByMonth': expirationsByMonth,
        'expirationsByProperty': expirationsByProperty,
        'tenants': tenants,
      };
    } catch (e) {
      AppLogger.error('Error getting lease expirations: $e');
      rethrow;
    }
  }
} 