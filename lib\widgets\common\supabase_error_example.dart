import 'package:flutter/material.dart';
import 'supabase_error_notice.dart';

/// Example usage of the SupabaseErrorNotice widget
class SupabaseErrorExample extends StatelessWidget {
  const SupabaseErrorExample({super.key});

  @override
  Widget build(BuildContext context) {
    // Example error message from logs
    const String errorMessage = 'Error fetching property by ID: ClientException: Connection reset by peer, uri=https://qyovaxxljzfpnciumarh.supabase.co/rest/v1/properties?select=%2A%2Cutility_bills%28%2A%29&id=eq.9ff8d0ff-e7c8-479f-a036-1f1f37114f86';

    return Scaffold(
      appBar: AppBar(
        title: const Text('Database Connection Error'),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Error occurred while loading data',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              
              // Basic usage
              SupabaseErrorNotice(
                errorMessage: errorMessage,
                onRetry: () {
                  // Implement retry logic
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('Retrying connection...')),
                  );
                },
              ),
              
              const SizedBox(height: 24),
              const Text(
                'With additional actions:',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              
              // With additional actions
              SupabaseErrorNotice(
                errorMessage: errorMessage,
                onRetry: () {
                  // Implement retry logic
                },
                additionalActions: [
                  TextButton.icon(
                    onPressed: () {
                      // Navigate to offline mode or cached data
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(content: Text('Switching to offline mode...')),
                      );
                    },
                    icon: const Icon(Icons.offline_bolt),
                    label: const Text('Use Offline'),
                    style: TextButton.styleFrom(
                      foregroundColor: Colors.grey[700],
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
} 