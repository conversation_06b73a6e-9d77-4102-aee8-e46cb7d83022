import 'package:uuid/uuid.dart';

enum PaymentStatus {
  pending,
  verified,
  rejected,
}

enum PaymentMethod {
  cash,
  bankTransfer,
  mobileMoney,
  check,
  other,
}

class Payment {
  final String id;
  final String? tenantId;
  final List<String> billIds; // List of bill IDs this payment applies to
  final double amount;
  final DateTime paymentDate;
  final PaymentStatus status;
  final PaymentMethod method;
  final String? receiptReference; // Reference number or receipt number
  final String? notes;
  final String? proofImageUrl; // URL to uploaded proof image
  final DateTime createdAt;
  final DateTime? updatedAt;
  final String? verifiedBy; // User ID who verified the payment

  Payment({
    String? id,
    required this.tenantId,
    required this.billIds,
    required this.amount,
    required this.paymentDate,
    this.status = PaymentStatus.pending,
    required this.method,
    this.receiptReference,
    this.notes,
    this.proofImageUrl,
    DateTime? createdAt,
    this.updatedAt,
    this.verifiedBy,
  })  : id = id ?? const Uuid().v4(),
        createdAt = createdAt ?? DateTime.now();

  // Create a Payment from JSON data
  factory Payment.fromJson(Map<String, dynamic> json) {
    return Payment(
      id: json['id'],
      tenantId: json['tenant_id'],
      billIds: (json['bill_ids'] as List<dynamic>).map((e) => e as String).toList(),
      amount: json['amount']?.toDouble(),
      paymentDate: DateTime.parse(json['payment_date']),
      status: _parseStatus(json['status']),
      method: _parseMethod(json['method']),
      receiptReference: json['receipt_reference'],
      notes: json['notes'],
      proofImageUrl: json['proof_image_url'],
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: json['updated_at'] != null ? DateTime.parse(json['updated_at']) : null,
      verifiedBy: json['verified_by'],
    );
  }

  // Convert Payment to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'tenant_id': tenantId,
      'bill_ids': billIds,
      'amount': amount,
      'payment_date': paymentDate.toIso8601String(),
      'status': status.name,
      'method': method.name,
      'receipt_reference': receiptReference,
      'notes': notes,
      'proof_image_url': proofImageUrl,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'verified_by': verifiedBy,
    };
  }

  // Parse status from string
  static PaymentStatus _parseStatus(String? status) {
    if (status == null) return PaymentStatus.pending;

    switch (status) {
      case 'verified':
        return PaymentStatus.verified;
      case 'rejected':
        return PaymentStatus.rejected;
      case 'pending':
      default:
        return PaymentStatus.pending;
    }
  }

  // Parse method from string
  static PaymentMethod _parseMethod(String? method) {
    if (method == null) return PaymentMethod.other;

    switch (method) {
      case 'cash':
        return PaymentMethod.cash;
      case 'bankTransfer':
        return PaymentMethod.bankTransfer;
      case 'mobileMoney':
        return PaymentMethod.mobileMoney;
      case 'check':
        return PaymentMethod.check;
      case 'other':
      default:
        return PaymentMethod.other;
    }
  }

  // Create a copy of Payment with updated fields
  Payment copyWith({
    String? id,
    String? tenantId,
    List<String>? billIds,
    double? amount,
    DateTime? paymentDate,
    PaymentStatus? status,
    PaymentMethod? method,
    String? receiptReference,
    String? notes,
    String? proofImageUrl,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? verifiedBy,
  }) {
    return Payment(
      id: id ?? this.id,
      tenantId: tenantId ?? this.tenantId,
      billIds: billIds ?? this.billIds,
      amount: amount ?? this.amount,
      paymentDate: paymentDate ?? this.paymentDate,
      status: status ?? this.status,
      method: method ?? this.method,
      receiptReference: receiptReference ?? this.receiptReference,
      notes: notes ?? this.notes,
      proofImageUrl: proofImageUrl ?? this.proofImageUrl,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      verifiedBy: verifiedBy ?? this.verifiedBy,
    );
  }
} 