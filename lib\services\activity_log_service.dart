import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/activity_log.dart';
import 'package:logging/logging.dart';

class ActivityLogService {
  final SupabaseClient _client;
  final Logger _logger = Logger('ActivityLogService');
  
  ActivityLogService(this._client);

  // Log a new activity
  Future<ActivityLog> logActivity(ActivityLog log) async {
    try {
      final response = await _client
          .from('activity_logs')
          .insert(log.toJson())
          .select()
          .single();
      
      return ActivityLog.fromJson(response);
    } catch (e) {
      _logger.severe('Error logging activity: $e');
      // Return the original log even if it failed to save
      // so the app can continue without crashing
      return log;
    }
  }

  // Get activity logs by tenant ID
  Future<List<ActivityLog>> getLogsByTenant(String tenantId) async {
    try {
      final response = await _client
          .from('activity_logs')
          .select()
          .eq('tenant_id', tenantId)
          .order('created_at', ascending: false);
      
      return response.map<ActivityLog>((json) => ActivityLog.fromJson(json)).toList();
    } catch (e) {
      _logger.severe('Error getting logs by tenant: $e');
      return [];
    }
  }

  // Get activity logs by room ID
  Future<List<ActivityLog>> getLogsByRoom(String roomId) async {
    try {
      final response = await _client
          .from('activity_logs')
          .select()
          .eq('room_id', roomId)
          .order('created_at', ascending: false);
      
      return response.map<ActivityLog>((json) => ActivityLog.fromJson(json)).toList();
    } catch (e) {
      _logger.severe('Error getting logs by room: $e');
      return [];
    }
  }

  // Get activity logs by property ID
  Future<List<ActivityLog>> getLogsByProperty(String propertyId) async {
    try {
      final response = await _client
          .from('activity_logs')
          .select()
          .eq('property_id', propertyId)
          .order('created_at', ascending: false);
      
      return response.map<ActivityLog>((json) => ActivityLog.fromJson(json)).toList();
    } catch (e) {
      _logger.severe('Error getting logs by property: $e');
      return [];
    }
  }

  // Get activity logs by type
  Future<List<ActivityLog>> getLogsByType(ActivityType type) async {
    try {
      final response = await _client
          .from('activity_logs')
          .select()
          .eq('type', type.name)
          .order('created_at', ascending: false);
      
      return response.map<ActivityLog>((json) => ActivityLog.fromJson(json)).toList();
    } catch (e) {
      _logger.severe('Error getting logs by type: $e');
      return [];
    }
  }

  // Get recent activity logs
  Future<List<ActivityLog>> getRecentLogs({int limit = 20}) async {
    try {
      final response = await _client
          .from('activity_logs')
          .select()
          .order('created_at', ascending: false)
          .limit(limit);
      
      return response.map<ActivityLog>((json) => ActivityLog.fromJson(json)).toList();
    } catch (e) {
      _logger.severe('Error getting recent logs: $e');
      return [];
    }
  }
}
