import 'package:supabase_flutter/supabase_flutter.dart';
import '../../models/payment/payment_model.dart';
import '../../utils/logger.dart';
import '../bill/bill_service.dart';

class PaymentService {
  final SupabaseClient _client = Supabase.instance.client;
  final String _tableName = 'payments';
  final BillService _billService;

  PaymentService(this._billService);

  // Generate a sequential receipt number
  Future<String> _generateReceiptNumber() async {
    try {
      // Get the count of existing payments to determine the next number
      final result = await _client
          .from(_tableName)
          .select('id')
          .order('created_at', ascending: false)
          .limit(1);

      // If there are no payments yet, start with 1000001
      int nextNumber = 1000001;
      
      if (result.isNotEmpty) {
        // Try to extract the last receipt number if it exists
        final lastPayment = await _client
            .from(_tableName)
            .select('receipt_reference')
            .order('created_at', ascending: false)
            .limit(1)
            .single();
            
        final lastReference = lastPayment['receipt_reference'] as String?;
        
        if (lastReference != null && lastReference.startsWith('REC-')) {
          // Extract the number part and increment
          final numberStr = lastReference.substring(4); // Remove 'REC-'
          try {
            final number = int.parse(numberStr);
            nextNumber = number + 1;
          } catch (e) {
            // If parsing fails, use the default next number
            AppLogger.error('Failed to parse last receipt number: $e');
          }
        }
      }
      
      // Format as REC-1000001
      return 'REC-$nextNumber';
    } catch (e) {
      AppLogger.error('Failed to generate receipt number: $e');
      // Return a fallback with timestamp to ensure uniqueness
      return 'REC-${DateTime.now().millisecondsSinceEpoch}';
    }
  }

  // Validate M-Pesa transaction code
  bool isValidMpesaCode(String code) {
    // M-Pesa transaction codes typically follow patterns like:
    // - Standard: 10 characters, starts with letters (e.g., OFH7X5TPKZ)
    // - Newer format: Starts with letter, followed by numbers (e.g., P235ABCDEF)
    // - Can be any combination of letters and numbers
    
    // Updated validation - allow any combination of letters and numbers
    // Length is typically between 8-12 characters
    final mpesaRegex = RegExp(r'^[A-Za-z0-9]{8,12}$');
    return mpesaRegex.hasMatch(code);
  }

  // Create a new payment with auto-generated receipt number
  Future<Payment> createPayment(Payment payment) async {
    try {
      // Check if any of the bills are already fully paid
      for (final billId in payment.billIds) {
        final bill = await _billService.getBill(billId);
        if (bill.isFullyPaid()) {
          throw Exception('Bill ${bill.billNumber ?? bill.id} is already fully paid. Cannot record duplicate payment.');
        }
      }
      
      // Generate a receipt number if one is not provided
      String receiptReference = payment.receiptReference ?? await _generateReceiptNumber();
      
      // For M-Pesa payments, validate the transaction code if provided
      if (payment.method == PaymentMethod.mobileMoney && 
          payment.receiptReference != null &&
          !isValidMpesaCode(payment.receiptReference!)) {
        throw Exception('Invalid M-Pesa transaction code format');
      }
      
      // Create a new payment object with the receipt number
      final paymentWithReceipt = payment.copyWith(
        receiptReference: receiptReference
      );
      
      final paymentData = paymentWithReceipt.toJson();
      
      final response = await _client
          .from(_tableName)
          .insert(paymentData)
          .select()
          .single();

      // Update the status of all bills included in this payment
      await _updateBillsForPayment(paymentWithReceipt);

      return Payment.fromJson(response);
    } catch (e) {
      AppLogger.error('Failed to create payment: $e');
      throw Exception('Failed to create payment: $e');
    }
  }

  // Update bills associated with a payment
  Future<void> _updateBillsForPayment(Payment payment) async {
    try {
      final validBillIds = <String>[];

      // First, validate all bill IDs and collect valid ones
      for (final billId in payment.billIds) {
        final bill = await _billService.getBillSafe(billId);
        if (bill != null) {
          validBillIds.add(billId);
        } else {
          AppLogger.warning('Payment ${payment.id} references non-existent bill: $billId');
        }
      }

      if (validBillIds.isEmpty) {
        AppLogger.warning('Payment ${payment.id} has no valid bill references');
        return;
      }

      // Update each valid bill with the payment amount
      for (final billId in validBillIds) {
        final bill = await _billService.getBill(billId);

        // Skip bills that are already fully paid
        if (bill.isFullyPaid()) {
          AppLogger.warning('Bill ${bill.billNumber ?? bill.id} is already fully paid. Skipping payment update.');
          continue;
        }

        // Distribute the payment amount equally among all valid bills
        final billAmount = payment.amount / validBillIds.length;

        await _billService.recordPayment(
          billId: billId,
          amount: billAmount,
          notes: 'Payment ID: ${payment.id}${payment.notes != null ? ' - ${payment.notes}' : ''}',
        );
      }
    } catch (e) {
      AppLogger.error('Failed to update bills for payment: $e');
      throw Exception('Failed to update bills for payment: $e');
    }
  }

  // Get a payment by ID
  Future<Payment> getPayment(String id) async {
    try {
      final response = await _client
          .from(_tableName)
          .select()
          .eq('id', id)
          .single();

      return Payment.fromJson(response);
    } catch (e) {
      AppLogger.error('Failed to get payment: $e');
      throw Exception('Failed to get payment: $e');
    }
  }

  // Get payment by receipt number
  Future<Payment?> getPaymentByReceiptNumber(String receiptNumber) async {
    try {
      final response = await _client
          .from(_tableName)
          .select()
          .eq('receipt_reference', receiptNumber)
          .limit(1);
      
      if (response.isEmpty) {
        return null;
      }
      
      return Payment.fromJson(response.first);
    } catch (e) {
      AppLogger.error('Failed to get payment by receipt number: $e');
      throw Exception('Failed to get payment by receipt number: $e');
    }
  }

  // Get all payments
  Future<List<Payment>> getAllPayments({
    DateTime? startDate,
    DateTime? endDate,
    PaymentStatus? status,
  }) async {
    try {
      var query = _client.from(_tableName).select();
      
      // Apply date filters if provided
      if (startDate != null) {
        query = query.gte('payment_date', startDate.toIso8601String());
      }
      
      if (endDate != null) {
        query = query.lte('payment_date', endDate.toIso8601String());
      }
      
      // Apply status filter if provided
      if (status != null) {
        query = query.eq('status', status.name);
      }
      
      final response = await query.order('payment_date', ascending: false);

      return response.map<Payment>((data) => Payment.fromJson(data)).toList();
    } catch (e) {
      AppLogger.error('Failed to get payments: $e');
      throw Exception('Failed to get payments: $e');
    }
  }

  // Get payments by tenant ID
  Future<List<Payment>> getPaymentsByTenantId(
    String tenantId, {
    DateTime? startDate,
    DateTime? endDate,
    PaymentStatus? status,
  }) async {
    try {
      var query = _client
          .from(_tableName)
          .select()
          .eq('tenant_id', tenantId);
      
      // Apply date filters if provided
      if (startDate != null) {
        query = query.gte('payment_date', startDate.toIso8601String());
      }
      
      if (endDate != null) {
        query = query.lte('payment_date', endDate.toIso8601String());
      }
      
      // Apply status filter if provided
      if (status != null) {
        query = query.eq('status', status.name);
      }
      
      final response = await query.order('payment_date', ascending: false);

      return response.map<Payment>((data) => Payment.fromJson(data)).toList();
    } catch (e) {
      AppLogger.error('Failed to get payments for tenant: $e');
      throw Exception('Failed to get payments for tenant: $e');
    }
  }

  // Get payments by bill ID
  Future<List<Payment>> getPaymentsByBillId(String billId) async {
    try {
      // This is more complex as we need to search in the array of bill_ids
      // Using Postgres contains operator for array search
      final response = await _client
          .from(_tableName)
          .select()
          .contains('bill_ids', [billId])
          .order('payment_date', ascending: false);

      return response.map<Payment>((data) => Payment.fromJson(data)).toList();
    } catch (e) {
      AppLogger.error('Failed to get payments for bill: $e');
      throw Exception('Failed to get payments for bill: $e');
    }
  }

  // Update payment status
  Future<Payment> updatePaymentStatus(
    String id,
    PaymentStatus status, {
    String? verifiedBy,
    String? notes,
  }) async {
    try {
      final payment = await getPayment(id);
      
      final updatedPayment = payment.copyWith(
        status: status,
        verifiedBy: verifiedBy ?? payment.verifiedBy,
        notes: notes != null 
            ? (payment.notes != null 
                ? '${payment.notes}\n${DateTime.now().toIso8601String()}: $notes' 
                : '${DateTime.now().toIso8601String()}: $notes')
            : payment.notes,
        updatedAt: DateTime.now(),
      );

      final response = await _client
          .from(_tableName)
          .update(updatedPayment.toJson())
          .eq('id', id)
          .select()
          .single();

      return Payment.fromJson(response);
    } catch (e) {
      AppLogger.error('Failed to update payment status: $e');
      throw Exception('Failed to update payment status: $e');
    }
  }

  // Verify a payment (convenience method)
  Future<Payment> verifyPayment(String id, {String? verifiedBy, String? notes}) async {
    try {
      // Add auto-verification note if no notes provided
      final verificationNotes = notes ?? 'Payment verified';
      
      // Use the existing updatePaymentStatus method with verified status
      return await updatePaymentStatus(
        id, 
        PaymentStatus.verified,
        verifiedBy: verifiedBy,
        notes: verificationNotes,
      );
    } catch (e) {
      AppLogger.error('Failed to verify payment: $e');
      throw Exception('Failed to verify payment: $e');
    }
  }

  // Update payment details
  Future<Payment> updatePayment(Payment payment) async {
    try {
      final response = await _client
          .from(_tableName)
          .update(payment.copyWith(updatedAt: DateTime.now()).toJson())
          .eq('id', payment.id)
          .select()
          .single();

      return Payment.fromJson(response);
    } catch (e) {
      AppLogger.error('Failed to update payment: $e');
      throw Exception('Failed to update payment: $e');
    }
  }

  // Delete a payment
  Future<void> deletePayment(String id) async {
    try {
      await _client.from(_tableName).delete().eq('id', id);
    } catch (e) {
      AppLogger.error('Failed to delete payment: $e');
      throw Exception('Failed to delete payment: $e');
    }
  }

  // Get payment statistics
  Future<Map<String, dynamic>> getPaymentStatistics({
    DateTime? startDate,
    DateTime? endDate,
    String? tenantId,
  }) async {
    try {
      var query = _client.from(_tableName).select();
      
      // Apply date filters if provided
      if (startDate != null) {
        query = query.gte('payment_date', startDate.toIso8601String());
      }
      
      if (endDate != null) {
        query = query.lte('payment_date', endDate.toIso8601String());
      }
      
      // Apply tenant filter if provided
      if (tenantId != null) {
        query = query.eq('tenant_id', tenantId);
      }
      
      final response = await query;
      
      // Calculate statistics
      double totalAmount = 0;
      int verifiedCount = 0;
      int pendingCount = 0;
      int rejectedCount = 0;
      
      final payments = response.map<Payment>((data) => Payment.fromJson(data)).toList();
      
      for (final payment in payments) {
        totalAmount += payment.amount;
        
        switch (payment.status) {
          case PaymentStatus.verified:
            verifiedCount++;
            break;
          case PaymentStatus.pending:
            pendingCount++;
            break;
          case PaymentStatus.rejected:
            rejectedCount++;
            break;
        }
      }
      
      return {
        'totalAmount': totalAmount,
        'totalCount': payments.length,
        'verifiedCount': verifiedCount,
        'pendingCount': pendingCount,
        'rejectedCount': rejectedCount,
      };
    } catch (e) {
      AppLogger.error('Failed to get payment statistics: $e');
      throw Exception('Failed to get payment statistics: $e');
    }
  }

  // Validate payment bill references and return orphaned bill IDs
  Future<List<String>> validatePaymentBillReferences(String paymentId) async {
    try {
      final payment = await getPayment(paymentId);
      final orphanedBillIds = <String>[];

      for (final billId in payment.billIds) {
        final bill = await _billService.getBillSafe(billId);
        if (bill == null) {
          orphanedBillIds.add(billId);
        }
      }

      return orphanedBillIds;
    } catch (e) {
      AppLogger.error('Failed to validate payment bill references: $e');
      return [];
    }
  }

  // Clean up orphaned bill references from a payment
  Future<Payment?> cleanupOrphanedBillReferences(String paymentId) async {
    try {
      final payment = await getPayment(paymentId);
      final validBillIds = <String>[];

      for (final billId in payment.billIds) {
        final bill = await _billService.getBillSafe(billId);
        if (bill != null) {
          validBillIds.add(billId);
        } else {
          AppLogger.warning('Removing orphaned bill reference: $billId from payment: $paymentId');
        }
      }

      if (validBillIds.length != payment.billIds.length) {
        // Update payment with only valid bill IDs
        final updatedPayment = payment.copyWith(
          billIds: validBillIds,
          updatedAt: DateTime.now(),
        );

        final response = await _client
            .from(_tableName)
            .update(updatedPayment.toJson())
            .eq('id', paymentId)
            .select()
            .single();

        AppLogger.info('Cleaned up payment $paymentId: removed ${payment.billIds.length - validBillIds.length} orphaned bill references');
        return Payment.fromJson(response);
      }

      return payment;
    } catch (e) {
      AppLogger.error('Failed to cleanup orphaned bill references: $e');
      return null;
    }
  }
}