import 'package:flutter/material.dart';
import '../../models/bill/bill.dart';
import '../../models/tenant/tenant.dart';
import '../../models/property/property_model.dart';
import '../../models/room/room_model.dart';
import '../../services/service_locator.dart';
import '../../utils/currency_formatter.dart';
import '../../utils/logger.dart';
import 'record_multiple_bills_payment_page.dart';

class SelectBillsForPaymentPage extends StatefulWidget {
  final Tenant tenant;

  const SelectBillsForPaymentPage({super.key, required this.tenant});

  @override
  State<SelectBillsForPaymentPage> createState() => _SelectBillsForPaymentPageState();
}

class _SelectBillsForPaymentPageState extends State<SelectBillsForPaymentPage> {
  bool _isLoading = true;
  List<Bill> _bills = [];
  List<Bill> _filteredBills = [];
  final Map<String, bool> _selectedBills = {};
  double _totalSelected = 0;
  
  // Tenant details
  Property? _tenantProperty;
  Room? _tenantRoom;
  
  // Filters
  String _statusFilter = 'all'; // all, pending, overdue
  String _typeFilter = 'all'; // all, rent, utility, etc.
  String _sortBy = 'due_date'; // due_date, amount, type
  bool _sortAscending = true;

  @override
  void initState() {
    super.initState();
    _loadTenantDetails();
    _loadBills();
  }

  Future<void> _loadBills() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Get bills from both sources:
      // 1. Direct tenant bills (bills with tenant_id set)
      // 2. Bills through bill_tenants relationship table
      
      List<Bill> allBills = [];
      
      // Get direct tenant bills (single-tenant bills)
      try {
        final directBills = await serviceLocator.billService.getBillsByTenant(widget.tenant.id);
        // Filter for outstanding bills only
        final outstandingDirectBills = directBills.where((bill) => 
          bill.status == BillStatus.pending || bill.status == BillStatus.overdue
        ).toList();
        allBills.addAll(outstandingDirectBills);
      } catch (e) {
        AppLogger.warning('Error loading direct tenant bills: $e');
      }
      
      // Get bills through relationship table (multi-tenant bills)
      try {
        final relationshipBills = await serviceLocator.billTenantService.getBillsByTenantId(
          widget.tenant.id,
          statusFilter: [BillStatus.pending, BillStatus.overdue],
          sortBy: 'due_date',
          ascending: true,
        );
        allBills.addAll(relationshipBills);
      } catch (e) {
        AppLogger.warning('Error loading relationship tenant bills: $e');
      }
      
      // Remove duplicates (in case a bill exists in both sources, though this shouldn't happen)
      final uniqueBills = <String, Bill>{};
      for (final bill in allBills) {
        uniqueBills[bill.id] = bill;
      }
      
      // Sort by due date (earliest first)
      final sortedBills = uniqueBills.values.toList()
        ..sort((a, b) => a.dueDate.compareTo(b.dueDate));
      
      if (mounted) {
        setState(() {
          _bills = sortedBills;
          _isLoading = false;
          _applyFiltersAndSort();
        });
      }
    } catch (e) {
      AppLogger.error('Error loading bills for tenant ${widget.tenant.id}: $e');
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to load bills: ${e.toString()}'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 5),
          ),
        );
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _toggleBillSelection(String billId, bool isSelected) {
    setState(() {
      _selectedBills[billId] = isSelected;
      _updateTotalSelected();
    });
  }

  Future<void> _loadTenantDetails() async {
    try {
      if (widget.tenant.roomId != null) {
        final room = await serviceLocator.roomService.getRoomById(widget.tenant.roomId!);
        if (room != null) {
          _tenantRoom = room;
          final property = await serviceLocator.propertyService.getPropertyById(room.propertyId);
          _tenantProperty = property;
        }
      }
      if (mounted) {
        setState(() {});
      }
    } catch (e) {
      AppLogger.warning('Failed to load tenant details: $e');
    }
  }
  
  void _applyFiltersAndSort() {
    List<Bill> filtered = List.from(_bills);
    
    // Apply status filter
    if (_statusFilter != 'all') {
      if (_statusFilter == 'pending') {
        filtered = filtered.where((bill) => bill.status == BillStatus.pending && !bill.isOverdue()).toList();
      } else if (_statusFilter == 'overdue') {
        filtered = filtered.where((bill) => bill.isOverdue()).toList();
      }
    }
    
    // Apply type filter
    if (_typeFilter != 'all') {
      final type = BillType.values.firstWhere((t) => t.name == _typeFilter, orElse: () => BillType.other);
      filtered = filtered.where((bill) => bill.type == type).toList();
    }
    
    // Apply sorting
    filtered.sort((a, b) {
      int result;
      switch (_sortBy) {
        case 'due_date':
          result = a.dueDate.compareTo(b.dueDate);
          break;
        case 'amount':
          result = a.getRemainingAmount().compareTo(b.getRemainingAmount());
          break;
        case 'type':
          result = a.type.name.compareTo(b.type.name);
          break;
        default:
          result = a.dueDate.compareTo(b.dueDate);
      }
      return _sortAscending ? result : -result;
    });
    
    setState(() {
      _filteredBills = filtered;
    });
  }
  
  void _updateTotalSelected() {
    double total = 0;
    for (final bill in _filteredBills) {
      if (_selectedBills[bill.id] == true) {
        total += bill.getRemainingAmount();
      }
    }
    _totalSelected = total;
  }

  void _proceedToPayment() {
    final selectedBillIds = _selectedBills.entries
        .where((entry) => entry.value)
        .map((entry) => entry.key)
        .toList();
    
    if (selectedBillIds.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please select at least one bill')),
      );
      return;
    }
    
    final selectedBills = _filteredBills
        .where((bill) => selectedBillIds.contains(bill.id))
        .toList();
    
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => RecordMultipleBillsPaymentPage(
          tenant: widget.tenant,
          bills: selectedBills,
          totalAmount: _totalSelected,
        ),
      ),
    ).then((result) {
      if (result == true && mounted) {
        // Refresh the bills list after payment is recorded
        _loadBills();
        Navigator.of(context).pop(true); // Return true to indicate payment was processed
      }
    });
  }

  Color _getBillTypeColor(BillType type) {
    switch (type) {
      case BillType.rent:
        return Colors.blue;
      case BillType.utility:
        return Colors.orange;
      case BillType.maintenance:
        return Colors.purple;
      case BillType.service:
        return Colors.green;
      case BillType.other:
        return Colors.grey;
    }
  }
  
  String _getBillTypeText(BillType type) {
    switch (type) {
      case BillType.rent:
        return 'RENT';
      case BillType.utility:
        return 'UTILITY';
      case BillType.maintenance:
        return 'MAINTENANCE';
      case BillType.service:
        return 'SERVICE';
      case BillType.other:
        return 'OTHER';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Select Bills'),
      ),
      body: Column(
        children: [
          // Enhanced tenant info card - more compact design
          Card(
            margin: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
            elevation: 1,
            child: Padding(
              padding: const EdgeInsets.all(12.0),
              child: Row(
                children: [
                  CircleAvatar(
                    backgroundColor: Theme.of(context).colorScheme.primary,
                    radius: 20,
                    child: Text(
                      '${widget.tenant.firstName[0]}${widget.tenant.lastName[0]}',
                      style: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '${widget.tenant.firstName} ${widget.tenant.lastName}',
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        if (_tenantRoom != null)
                          Row(
                            children: [
                              Icon(Icons.home, size: 14, color: Colors.grey[600]),
                              const SizedBox(width: 4),
                              Text(
                                'House: ${_tenantRoom!.name}',
                                style: TextStyle(
                                  fontSize: 13,
                                  color: Colors.grey[800],
                                ),
                              ),
                            ],
                          ),
                        if (_tenantProperty != null)
                          Row(
                            children: [
                              Icon(Icons.location_on, size: 14, color: Colors.grey[600]),
                              const SizedBox(width: 4),
                              Expanded(
                                child: Text(
                                  'Property: ${_tenantProperty!.name}',
                                  style: TextStyle(
                                    fontSize: 13,
                                    color: Colors.grey[800],
                                  ),
                                ),
                              ),
                            ],
                          ),
                        if (widget.tenant.phoneNumber != null && widget.tenant.phoneNumber!.isNotEmpty)
                          Row(
                            children: [
                              Icon(Icons.phone, size: 14, color: Colors.grey[600]),
                              const SizedBox(width: 4),
                              Text(
                                widget.tenant.phoneNumber!,
                                style: TextStyle(
                                  fontSize: 13,
                                  color: Colors.grey[800],
                                ),
                              ),
                            ],
                          ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
          
          // Filters card - more compact design
          Card(
            margin: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 4.0),
            elevation: 1,
            child: Padding(
              padding: const EdgeInsets.all(12.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Filters & Sorting',
                    style: Theme.of(context).textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Expanded(
                        child: DropdownButtonFormField<String>(
                          decoration: InputDecoration(
                            labelText: 'Status',
                            border: const OutlineInputBorder(),
                            contentPadding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
                            labelStyle: TextStyle(fontSize: 13, color: Colors.grey[700]),
                            isDense: true,
                          ),
                          value: _statusFilter,
                          items: const [
                            DropdownMenuItem(value: 'all', child: Text('All')),
                            DropdownMenuItem(value: 'pending', child: Text('Pending')),
                            DropdownMenuItem(value: 'overdue', child: Text('Overdue')),
                          ],
                          onChanged: (value) {
                            if (value != null) {
                              setState(() {
                                _statusFilter = value;
                                _applyFiltersAndSort();
                              });
                            }
                          },
                          style: TextStyle(fontSize: 14, color: Colors.black87),
                          icon: const Icon(Icons.arrow_drop_down, size: 20),
                          isExpanded: true,
                          dropdownColor: Colors.white,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: DropdownButtonFormField<String>(
                          decoration: InputDecoration(
                            labelText: 'Type',
                            border: const OutlineInputBorder(),
                            contentPadding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
                            labelStyle: TextStyle(fontSize: 13, color: Colors.grey[700]),
                            isDense: true,
                          ),
                          value: _typeFilter,
                          items: const [
                            DropdownMenuItem(value: 'all', child: Text('All Types')),
                            DropdownMenuItem(value: 'rent', child: Text('Rent')),
                            DropdownMenuItem(value: 'utility', child: Text('Utility')),
                            DropdownMenuItem(value: 'maintenance', child: Text('Maintenance')),
                            DropdownMenuItem(value: 'service', child: Text('Service')),
                            DropdownMenuItem(value: 'other', child: Text('Other')),
                          ],
                          onChanged: (value) {
                            if (value != null) {
                              setState(() {
                                _typeFilter = value;
                                _applyFiltersAndSort();
                              });
                            }
                          },
                          style: TextStyle(fontSize: 14, color: Colors.black87),
                          icon: const Icon(Icons.arrow_drop_down, size: 20),
                          isExpanded: true,
                          dropdownColor: Colors.white,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Expanded(
                        child: DropdownButtonFormField<String>(
                          decoration: InputDecoration(
                            labelText: 'Sort by',
                            border: const OutlineInputBorder(),
                            contentPadding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
                            labelStyle: TextStyle(fontSize: 13, color: Colors.grey[700]),
                            isDense: true,
                          ),
                          value: _sortBy,
                          items: const [
                            DropdownMenuItem(value: 'due_date', child: Text('Due Date')),
                            DropdownMenuItem(value: 'amount', child: Text('Amount')),
                            DropdownMenuItem(value: 'type', child: Text('Type')),
                          ],
                          onChanged: (value) {
                            if (value != null) {
                              setState(() {
                                if (_sortBy == value) {
                                  _sortAscending = !_sortAscending;
                                } else {
                                  _sortBy = value;
                                  _sortAscending = true;
                                }
                                _applyFiltersAndSort();
                              });
                            }
                          },
                          style: TextStyle(fontSize: 14, color: Colors.black87),
                          icon: const Icon(Icons.arrow_drop_down, size: 20),
                          isExpanded: true,
                          dropdownColor: Colors.white,
                        ),
                      ),
                      IconButton(
                        icon: Icon(_sortAscending ? Icons.arrow_upward : Icons.arrow_downward, 
                          size: 20,
                          color: Theme.of(context).primaryColor,
                        ),
                        padding: const EdgeInsets.all(8),
                        constraints: const BoxConstraints(),
                        onPressed: () {
                          setState(() {
                            _sortAscending = !_sortAscending;
                            _applyFiltersAndSort();
                          });
                        },
                        tooltip: 'Toggle sort direction',
                      ),
                      IconButton(
                        icon: Icon(Icons.refresh, 
                          size: 20,
                          color: Theme.of(context).primaryColor,
                        ),
                        padding: const EdgeInsets.all(8),
                        constraints: const BoxConstraints(),
                        onPressed: _loadBills,
                        tooltip: 'Refresh bills',
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          
          // Bills count and selection info - more compact
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  '${_filteredBills.length} bill${_filteredBills.length == 1 ? '' : 's'} found',
                  style: TextStyle(
                    fontSize: 13,
                    color: Colors.grey[700],
                  ),
                ),
                Row(
                  children: [
                    TextButton(
                      onPressed: _filteredBills.isNotEmpty ? () {
                        setState(() {
                          for (final bill in _filteredBills) {
                            _selectedBills[bill.id] = true;
                          }
                          _updateTotalSelected();
                        });
                      } : null,
                      style: TextButton.styleFrom(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
                        minimumSize: Size.zero,
                        tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                      ),
                      child: const Text('Select All', style: TextStyle(fontSize: 13)),
                    ),
                    TextButton(
                      onPressed: _selectedBills.values.contains(true) ? () {
                        setState(() {
                          _selectedBills.clear();
                          _updateTotalSelected();
                        });
                      } : null,
                      style: TextButton.styleFrom(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
                        minimumSize: Size.zero,
                        tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                      ),
                      child: const Text('Clear All', style: TextStyle(fontSize: 13)),
                    ),
                  ],
                ),
              ],
            ),
          ),
          
          // Bills list
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _filteredBills.isEmpty
                    ? Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.receipt_long,
                              size: 64,
                              color: Colors.grey[400],
                            ),
                            const SizedBox(height: 16),
                            Text(
                              _bills.isEmpty ? 'No outstanding bills found' : 'No bills match the current filters',
                              style: TextStyle(
                                fontSize: 16,
                                color: Colors.grey[600],
                              ),
                            ),
                            if (_bills.isNotEmpty && _filteredBills.isEmpty) ...[
                              const SizedBox(height: 8),
                              TextButton(
                                onPressed: () {
                                  setState(() {
                                    _statusFilter = 'all';
                                    _typeFilter = 'all';
                                    _applyFiltersAndSort();
                                  });
                                },
                                child: const Text('Clear Filters'),
                              ),
                            ],
                          ],
                        ),
                      )
                    : RefreshIndicator(
                        onRefresh: _loadBills,
                        child: Column(
                          children: [
                            Expanded(
                              child: ListView.builder(
                                itemCount: _filteredBills.length,
                                itemBuilder: (context, index) {
                                  final bill = _filteredBills[index];
                                  final isSelected = _selectedBills[bill.id] ?? false;
                                  final remainingAmount = bill.getRemainingAmount();
                                  
                                  return Card(
                                    margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
                                    elevation: isSelected ? 2 : 1,
                                    color: isSelected ? Theme.of(context).colorScheme.primaryContainer : null,
                                    child: CheckboxListTile(
                                      value: isSelected,
                                      onChanged: (value) {
                                        _toggleBillSelection(bill.id, value ?? false);
                                      },
                                      title: Row(
                                        children: [
                                          Expanded(
                                            child: Text(
                                              bill.title,
                                              style: const TextStyle(fontWeight: FontWeight.bold),
                                            ),
                                          ),
                                          Container(
                                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                                            decoration: BoxDecoration(
                                              color: _getBillTypeColor(bill.type).withAlpha(51),
                                              borderRadius: BorderRadius.circular(12),
                                              border: Border.all(color: _getBillTypeColor(bill.type)),
                                            ),
                                            child: Text(
                                              _getBillTypeText(bill.type),
                                              style: TextStyle(
                                                color: _getBillTypeColor(bill.type),
                                                fontSize: 10,
                                                fontWeight: FontWeight.bold,
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                      subtitle: Column(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          if (bill.description.isNotEmpty)
                                            Text(
                                              bill.description,
                                              maxLines: 2,
                                              overflow: TextOverflow.ellipsis,
                                            ),
                                          const SizedBox(height: 4),
                                          Row(
                                            children: [
                                              Icon(
                                                Icons.calendar_today,
                                                size: 14,
                                                color: bill.isOverdue() ? Colors.red : Colors.grey[600],
                                              ),
                                              const SizedBox(width: 4),
                                              Text(
                                                'Due: ${bill.dueDate.toString().split(' ')[0]}',
                                                style: TextStyle(
                                                  color: bill.isOverdue() ? Colors.red : Colors.grey[600],
                                                  fontWeight: bill.isOverdue() ? FontWeight.bold : FontWeight.normal,
                                                ),
                                              ),
                                            ],
                                          ),
                                          const SizedBox(height: 4),
                                          Wrap(
                                            spacing: 4,
                                            runSpacing: 4,
                                            children: [
                                              if (bill.isOverdue())
                                                Container(
                                                  padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                                                  decoration: BoxDecoration(
                                                    color: Colors.red.withAlpha(51),
                                                    borderRadius: BorderRadius.circular(8),
                                                  ),
                                                  child: const Text(
                                                    'OVERDUE',
                                                    style: TextStyle(
                                                      color: Colors.red,
                                                      fontSize: 10,
                                                      fontWeight: FontWeight.bold,
                                                    ),
                                                  ),
                                                ),
                                              if (bill.status == BillStatus.pending)
                                                Container(
                                                  padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                                                  decoration: BoxDecoration(
                                                    color: Colors.amber.withAlpha(26),
                                                    borderRadius: BorderRadius.circular(8),
                                                    border: Border.all(color: Colors.amber),
                                                  ),
                                                  child: const Text(
                                                    'PENDING',
                                                    style: TextStyle(
                                                      color: Colors.amber,
                                                      fontSize: 10,
                                                      fontWeight: FontWeight.bold,
                                                    ),
                                                  ),
                                                ),
                                              if (bill.isShared)
                                                Container(
                                                  padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                                                  decoration: BoxDecoration(
                                                    color: Colors.blueGrey.withAlpha(26),
                                                    borderRadius: BorderRadius.circular(8),
                                                    border: Border.all(color: Colors.blueGrey),
                                                  ),
                                                  child: Row(
                                                    mainAxisSize: MainAxisSize.min,
                                                    children: [
                                                      Icon(Icons.people, size: 12, color: Colors.blueGrey[700]),
                                                      const SizedBox(width: 2),
                                                      const Text(
                                                        'SHARED',
                                                        style: TextStyle(
                                                          color: Colors.blueGrey,
                                                          fontSize: 10,
                                                          fontWeight: FontWeight.bold,
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                            ],
                                          ),
                                        ],
                                      ),
                                      secondary: Column(
                                        mainAxisAlignment: MainAxisAlignment.center,
                                        crossAxisAlignment: CrossAxisAlignment.end,
                                        children: [
                                          Text(
                                            CurrencyFormatter.formatCurrency(remainingAmount),
                                            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                          if (bill.isPartiallyPaid())
                                            Text(
                                              'Partially paid',
                                              style: TextStyle(
                                                color: Colors.orange[700],
                                                fontSize: 12,
                                              ),
                                            ),
                                        ],
                                      ),
                                      controlAffinity: ListTileControlAffinity.leading,
                                    ),
                                  );
                                },
                              ),
                            ),
                          ],
                        ),
                      ),
          ),
          
          // Bottom bar with total and proceed button
          Container(
            padding: const EdgeInsets.all(16.0),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withAlpha(26),
                  blurRadius: 4,
                  offset: const Offset(0, -2),
                ),
              ],
            ),
            child: Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Text('Total Selected'),
                      Text(
                        CurrencyFormatter.formatCurrency(_totalSelected),
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                    ],
                  ),
                ),
                ElevatedButton(
                  onPressed: _selectedBills.values.contains(true)
                      ? _proceedToPayment
                      : null,
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 24,
                      vertical: 12,
                    ),
                  ),
                  child: const Text('Proceed to Payment'),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
} 