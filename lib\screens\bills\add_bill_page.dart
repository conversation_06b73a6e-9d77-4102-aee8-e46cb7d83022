import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../models/bill/bill.dart';
import '../../models/property/property_model.dart';
import '../../models/room/room_model.dart';
import '../../models/tenant/tenant.dart';
import '../../services/service_locator.dart';
import '../../utils/currency_formatter.dart';
import '../../utils/form_validators.dart';
import '../../utils/logger.dart';
import '../../widgets/loading_indicator.dart';

class AddBillPage extends StatefulWidget {
  final Bill? bill;
  final String? initialTenantId;

  const AddBillPage({super.key, this.bill, this.initialTenantId});

  @override
  State<AddBillPage> createState() => _AddBillPageState();
}

class _AddBillPageState extends State<AddBillPage> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _amountController = TextEditingController();
  final _notesController = TextEditingController();
  final _previousMeterController = TextEditingController();
  final _currentMeterController = TextEditingController();
  final _unitsConsumedController = TextEditingController();
  final _ratePerUnitController = TextEditingController();
  final _billNumberController = TextEditingController();
  
  // Initialize due date to the first day of the current month by default
  late DateTime _dueDate;
  BillType _type = BillType.rent;
  RecurrenceType _recurrence = RecurrenceType.monthly;
  bool _isLoading = false;
  bool _isLoadingProperty = false;
  bool _includeInRent = false;
  bool _includeInRentReadOnly = false;
  UtilityType _utilityType = UtilityType.water;
  bool _showUtilityFields = false;
  bool _useManualUnits = false;
  Bill? _existingRentBill;
  
  // To store the property and its utility rates
  String? _selectedPropertyId;
  Property? _selectedProperty;
  List<UtilityBill>? _utilityBills;
  UtilityBill? _selectedUtilityBill;
  
  // To store tenant and room information
  Tenant? _tenant;
  Room? _tenantRoom;
  
  // For bill summary calculation
  double _totalBillAmount = 0.0;

  // For custom utility types from property
  String _customUtilityType = '';

  @override
  void initState() {
    super.initState();
    // Initialize due date to today + 30 days by default
    _dueDate = DateTime.now().add(const Duration(days: 30));
    
    // Set default previous meter to 0
    _previousMeterController.text = '0';
    
    // Initialize amount to 0.00 for clean display
    if (_amountController.text.isEmpty) {
      _amountController.text = '0.00';
    }
    
    _initializeBillNumber();
    
    if (widget.bill != null) {
      _titleController.text = widget.bill!.title;
      _descriptionController.text = widget.bill!.description;
      _amountController.text = widget.bill!.amount.toString();
      _notesController.text = widget.bill!.notes ?? '';
      _dueDate = widget.bill!.dueDate;
      _recurrence = widget.bill!.recurrence;
      _includeInRent = widget.bill!.includeInRent;
      _selectedPropertyId = widget.bill!.propertyId;
      
      // If including in rent, we can't have bill type as rent
      if (_includeInRent) {
        // Always use utility type if include in rent is checked
        _type = BillType.utility;
      } else {
        // Otherwise use the bill's original type
        _type = widget.bill!.type;
      }
      
      // Initialize utility fields if they exist
      if (widget.bill!.utilityType != null) {
        _utilityType = widget.bill!.utilityType!;
        _showUtilityFields = true;
        
        if (widget.bill!.previousMeterReading != null) {
          _previousMeterController.text = widget.bill!.previousMeterReading.toString();
        }
        
        if (widget.bill!.currentMeterReading != null) {
          _currentMeterController.text = widget.bill!.currentMeterReading.toString();
        }
        
        if (widget.bill!.unitConsumed != null) {
          _unitsConsumedController.text = widget.bill!.unitConsumed.toString();
          _useManualUnits = true;
        }
        
        if (widget.bill!.ratePerUnit != null) {
          _ratePerUnitController.text = widget.bill!.ratePerUnit.toString();
        }
      }
      
      // Load property details if propertyId is available
      if (_selectedPropertyId != null) {
        _loadPropertyDetails(_selectedPropertyId!);
      }
      
      // If this is editing a bill, try to load the tenant's room
      if (widget.bill!.tenantId != null && widget.bill!.roomId != null) {
        _loadTenantById(widget.bill!.tenantId!);
      }
    } else if (widget.initialTenantId != null) {
      // Try to fetch tenant's room and property details
      _loadTenantRoomAndProperty();
    }
    
    // Set initial visibility based on bill type
    _updateUtilityFieldsVisibility(_type);
    
    // Calculate initial bill summary
    _updateBillSummary();
    
    // Log the initial state
    AppLogger.debug('InitState completed. Bill type: ${_type.name}, Show utility fields: $_showUtilityFields');
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _amountController.dispose();
    _notesController.dispose();
    _previousMeterController.dispose();
    _currentMeterController.dispose();
    _unitsConsumedController.dispose();
    _ratePerUnitController.dispose();
    _billNumberController.dispose();
    super.dispose();
  }
  
  /// Initialize bill number
  Future<void> _initializeBillNumber() async {
    try {
      if (widget.bill != null) {
        // Use existing bill number if available, otherwise generate a new one
        if (widget.bill!.billNumber != null) {
          _billNumberController.text = widget.bill!.billNumber!;
        } else {
          _billNumberController.text = await serviceLocator.billService.generateBillNumber();
        }
      } else {
        // Generate a new bill number for new bills
        _billNumberController.text = await serviceLocator.billService.generateBillNumber();
      }
    } catch (e) {
      // Fallback to a simple format if there's an error
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final numberPart = (timestamp % 10000000).toString().padLeft(7, '0');
      _billNumberController.text = 'BILL - $numberPart';
      AppLogger.error('Error generating bill number: $e');
    }
  }
  
  /// Load tenant by ID
  Future<void> _loadTenantById(String tenantId) async {
    try {
      final tenant = await serviceLocator.tenantService.getTenantById(tenantId);
      if (tenant != null) {
        _tenant = tenant;
        if (tenant.roomId != null) {
          await _loadTenantRoom(tenant.roomId!);
        }
        
        // Check for existing rent bill for this tenant in the current month
        await _checkExistingRentBill(tenantId);
      }
    } catch (e) {
      AppLogger.error('Error loading tenant: $e');
    }
  }
  
  /// Check if a rent bill already exists for this tenant in the current month
  Future<void> _checkExistingRentBill(String tenantId) async {
    try {
      // Only check if this is a new bill or if the current bill is not a rent bill
      if (widget.bill == null || widget.bill!.type != BillType.rent) {
        final existingBill = await serviceLocator.billService.checkExistingRentBill(
          tenantId, 
          _dueDate,
          excludeBillId: widget.bill?.id,
        );
        
        setState(() {
          _existingRentBill = existingBill;
          
          // If there's an existing rent bill, the "Include in Rent" checkbox should be checked
          // and read-only (unless we're editing that specific rent bill)
          if (existingBill != null) {
            _includeInRent = true;
            _includeInRentReadOnly = true;
            
            // Force bill type to utility since rent bill already exists
            if (_type == BillType.rent) {
              _type = BillType.utility;
              _updateUtilityFieldsVisibility(_type);
            }
            
            // Set amount to 0.00 since it's included in rent
            _amountController.text = '0.00';
            
            // Update UI to reflect this state
            AppLogger.debug('Found existing rent bill: ${existingBill.id}, making "Include in Rent" read-only');
            
            // Show a snackbar to inform the user
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('A rent bill already exists for this month. Additional bills will be added as components.'),
                  duration: const Duration(seconds: 5),
                ),
              );
            }
          }
        });
      }
    } catch (e) {
      AppLogger.error('Error checking for existing rent bill: $e');
    }
  }
  
  /// Load tenant's room
  Future<void> _loadTenantRoom(String roomId) async {
    try {
      final room = await serviceLocator.roomService.getRoomById(roomId);
      if (room != null) {
        setState(() {
          _tenantRoom = room;
          
          // Auto-update amount for rent bills if empty
          if (_type == BillType.rent && _amountController.text.isEmpty) {
            _amountController.text = room.rentalPrice.toString();
            _updateBillSummary();
          }
          
          // Load property details
          if (_selectedPropertyId == null) {
            _selectedPropertyId = room.propertyId;
            _loadPropertyDetails(room.propertyId);
          }
        });
        
        AppLogger.debug('Loaded room with rental price: ${room.rentalPrice}');
      }
    } catch (e) {
      AppLogger.error('Error loading room: $e');
    }
  }
  
  /// Load tenant's room and property information
  Future<void> _loadTenantRoomAndProperty() async {
    setState(() {
      _isLoadingProperty = true;
    });
    
    try {
      final tenant = await serviceLocator.tenantService.getTenantById(widget.initialTenantId!);
      
      if (tenant != null) {
        setState(() {
          _tenant = tenant;
        });
        
        if (tenant.roomId != null) {
          final room = await serviceLocator.roomService.getRoomById(tenant.roomId!);
          if (room != null) {
            setState(() {
              _tenantRoom = room;
              _selectedPropertyId = room.propertyId;
            });
            
            // If this is a rent bill, set the amount from room rental price
            if (_type == BillType.rent && _amountController.text.isEmpty) {
              _amountController.text = room.rentalPrice.toString();
              _updateBillSummary();
            }
            
            AppLogger.debug('Loaded room with rental price: ${room.rentalPrice}');
            
            await _loadPropertyDetails(room.propertyId);
          }
        }
        
        // Check for existing rent bill for this tenant in the current month
        await _checkExistingRentBill(tenant.id);
      }
    } catch (e) {
      AppLogger.error('Error loading tenant data: $e');
    } finally {
      setState(() {
        _isLoadingProperty = false;
      });
    }
  }
  
  /// Load property details including utility rates
  Future<void> _loadPropertyDetails(String propertyId) async {
    setState(() {
      _isLoadingProperty = true;
    });
    
    try {
      final property = await serviceLocator.propertyService.getPropertyById(propertyId);
      if (property != null) {
        setState(() {
          _selectedProperty = property;
          _utilityBills = property.utilityBills;
          
          AppLogger.debug('Loaded property with ${property.utilityBills.length} utility bills');
          if (property.utilityBills.isNotEmpty) {
            for (var bill in property.utilityBills) {
              AppLogger.debug('Utility bill: ${bill.name}, rate: ${bill.rate}');
            }
          }
          
          // Try to find matching utility bill for current utility type
          if (_showUtilityFields && _utilityBills != null && _utilityBills!.isNotEmpty) {
            _findMatchingUtilityBill();
          }
        });
      }
    } catch (e) {
      AppLogger.error('Error loading property data: $e');
    } finally {
      setState(() {
        _isLoadingProperty = false;
      });
    }
  }
  
  /// Find a matching utility bill based on the selected utility type
  void _findMatchingUtilityBill() {
    if (_utilityBills == null || _utilityBills!.isEmpty) return;
    
    try {
      String searchTerm = _utilityType.name;
      
      // If we have a custom utility type set, use that instead
      if (_customUtilityType.isNotEmpty) {
        searchTerm = _customUtilityType;
      }
      
      // Try to find exact match first (case insensitive)
      _selectedUtilityBill = _utilityBills!.firstWhere(
        (bill) => bill.name.toLowerCase() == searchTerm.toLowerCase(),
        orElse: () {
          // If no exact match, try to find a bill that contains the utility type name
          return _utilityBills!.firstWhere(
            (bill) => bill.name.toLowerCase().contains(searchTerm.toLowerCase()) ||
                     searchTerm.toLowerCase().contains(bill.name.toLowerCase()),
            orElse: () => _utilityBills!.first
          );
        }
      );
      
      // Update rate from selected utility bill
      _updateRateFromSelectedUtility();
      
      // Set the custom utility type to match the selected bill
      _customUtilityType = _selectedUtilityBill!.name;
      
      AppLogger.debug('Auto-selected utility bill: ${_selectedUtilityBill?.name}, rate: ${_selectedUtilityBill?.rate}');
    } catch (e) {
      AppLogger.error('Error finding matching utility bill: $e');
    }
  }
  
  void _updateRateFromSelectedUtility() {
    if (_selectedUtilityBill != null) {
      setState(() {
        _ratePerUnitController.text = _selectedUtilityBill!.rate.toString();
        AppLogger.debug('Updated rate from utility: ${_selectedUtilityBill!.name}, rate: ${_selectedUtilityBill!.rate}');
      });
      _calculateAmount();
    }
  }
  
  void _updateUtilityFieldsVisibility(BillType type) {
    setState(() {
      _showUtilityFields = type == BillType.utility;
      
      // If switching to rent type and we have tenant's room info, update amount
      if (type == BillType.rent && _tenantRoom != null && _amountController.text.isEmpty) {
        _amountController.text = _tenantRoom!.rentalPrice.toString();
      }
      
      _updateBillSummary();
    });
  }
  
  void _calculateAmount() {
    if (_useManualUnits) {
      if (_unitsConsumedController.text.isNotEmpty && _ratePerUnitController.text.isNotEmpty) {
        try {
          final units = double.parse(_unitsConsumedController.text);
          final rate = double.parse(_ratePerUnitController.text);
          final amount = units * rate;
          
          AppLogger.debug('Calculating amount (manual): $units units × $rate rate = ${amount.toStringAsFixed(2)}');
          
          // Always update amount regardless of Include in Rent setting
            setState(() {
              _amountController.text = amount.toStringAsFixed(2);
              _totalBillAmount = amount;
            });
          
          _updateBillSummary();
        } catch (e) {
          AppLogger.error('Error calculating amount with manual units: $e');
        }
      }
    } else {
      if (_previousMeterController.text.isNotEmpty && 
          _currentMeterController.text.isNotEmpty && 
          _ratePerUnitController.text.isNotEmpty) {
        try {
          final previous = double.parse(_previousMeterController.text);
          final current = double.parse(_currentMeterController.text);
          final rate = double.parse(_ratePerUnitController.text);
          
          if (current >= previous) {
            final units = current - previous;
            setState(() {
              _unitsConsumedController.text = units.toString();
              final amount = units * rate;
              
              AppLogger.debug('Calculating amount (meter): $current - $previous = $units units × $rate rate = ${amount.toStringAsFixed(2)}');
              
              // Always update amount regardless of Include in Rent setting
                _amountController.text = amount.toStringAsFixed(2);
                _totalBillAmount = amount;
            });
            
            _updateBillSummary();
          } else {
            AppLogger.warning('Current meter reading ($current) is less than previous ($previous)');
            // Show warning if current is less than previous
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Current meter reading cannot be less than previous reading'),
                backgroundColor: Colors.orange,
                duration: Duration(seconds: 3),
              ),
            );
          }
        } catch (e) {
          AppLogger.error('Error calculating amount with meter readings: $e');
        }
      }
    }
  }
  
  void _updateBillSummary() {
    try {
      double total = 0.0;
      
      // Always add bill amount to total regardless of Include in Rent setting
      if (_amountController.text.isNotEmpty) {
        double amount = double.parse(_amountController.text);
          total += amount;
      }
      
      // Update state
      setState(() {
        _totalBillAmount = total;
      });
      
      AppLogger.debug('Updated bill summary: Total: $total, Included in rent: $_includeInRent');
    } catch (e) {
      AppLogger.error('Error updating bill summary: $e');
    }
  }
  
  // Method to update total due
  void _updateTotalDue() {
    double total = double.tryParse(_amountController.text) ?? 0.0;
    setState(() {
      _totalBillAmount = total;
    });
  }
  
  // Handle include in rent toggle - update to not modify amount
  void _handleIncludeInRentChanged(bool? value) {
    // If the checkbox is read-only, don't allow changes
    if (_includeInRentReadOnly) {
      return;
    }
    
    if (value != null) {
      setState(() {
        _includeInRent = value;
        
        // If including in rent, we can't have bill type as rent
        if (value) {
          // Always change to utility type when including in rent
          if (_type == BillType.rent) {
            _type = BillType.utility;
            _updateUtilityFieldsVisibility(_type);
          }
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    // Safety check: If "Include in Rent" is checked, bill type can't be rent
    if (_includeInRent && _type == BillType.rent) {
      _type = BillType.utility;
    }
    
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.bill == null ? 'Add Bill' : 'Edit Bill'),
      ),
      body:
          _isLoading || _isLoadingProperty
              ? const LoadingIndicator()
              : SingleChildScrollView(
                padding: const EdgeInsets.all(16.0),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      // Tenant Information
                      if (_tenant != null)
                        Card(
                          margin: const EdgeInsets.only(bottom: 16),
                          child: Padding(
                            padding: const EdgeInsets.all(16.0),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Tenant Information',
                                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                const SizedBox(height: 8),
                                Text('${_tenant!.firstName} ${_tenant!.lastName}'),
                                if (_tenantRoom != null) ...[
                                  const SizedBox(height: 8),
                                  Text(
                                    'Room: ${_tenantRoom!.name}',
                                    style: const TextStyle(fontWeight: FontWeight.w500),
                                  ),
                                  const SizedBox(height: 4),
                                  Row(
                                    children: [
                                      const Text('Monthly Rent: '),
                                      Text(
                                        CurrencyFormatter.formatAmount(_tenantRoom!.rentalPrice),
                                        style: const TextStyle(fontWeight: FontWeight.bold),
                                      ),
                                    ],
                                  ),
                                ],
                              ],
                            ),
                          ),
                        ),
                      
                      // Bill Summary (full width)
                      Card(
                        margin: const EdgeInsets.only(bottom: 16),
                        elevation: 2,
                        color: Theme.of(context).colorScheme.primaryContainer.withAlpha(51),
                        child: Padding(
                          padding: const EdgeInsets.all(16),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Bill Summary',
                                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 16),
                              Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                children: [
                                  const Expanded(
                                    child: Text(
                                      'Amount:',
                                      style: TextStyle(fontWeight: FontWeight.w500),
                                    ),
                                  ),
                                  Text(
                                    'KSH ${double.tryParse(_amountController.text)?.toStringAsFixed(2) ?? '0.00'}',
                                    style: const TextStyle(fontWeight: FontWeight.w500),
                                  ),
                                ],
                              ),
                              const Divider(height: 24),
                              Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                children: [
                                  const Expanded(
                                    child: Text(
                                      'Total Due:',
                                      style: TextStyle(
                                        fontWeight: FontWeight.bold,
                                        fontSize: 18,
                                      ),
                                    ),
                                  ),
                                  Text(
                                    'KSH ${_totalBillAmount.toStringAsFixed(2)}',
                                    style: TextStyle(
                                      fontWeight: FontWeight.bold,
                                      fontSize: 18,
                                      color: Theme.of(context).colorScheme.primary,
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ),
                      
                      // Include in Rent checkbox
                      Card(
                        elevation: 2,
                        color: _includeInRent ? Theme.of(context).colorScheme.primaryContainer : null,
                        margin: const EdgeInsets.only(bottom: 16),
                        child: Column(
                          children: [
                            CheckboxListTile(
                              title: const Text('Include in Rent'),
                              subtitle: _existingRentBill != null 
                                ? Text('Rent bill already exists for this month (Bill #${_existingRentBill!.billNumber ?? "N/A"})')
                                : const Text('This bill will be added to tenant\'s monthly rent payment'),
                              value: _includeInRent,
                              onChanged: _includeInRentReadOnly ? null : _handleIncludeInRentChanged,
                              controlAffinity: ListTileControlAffinity.leading,
                              activeColor: Theme.of(context).colorScheme.primary,
                            ),
                            if (_includeInRentReadOnly)
                              Padding(
                                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                                child: Text(
                                  'This setting cannot be changed because a rent bill already exists for this tenant in the current month.',
                                  style: TextStyle(
                                    color: Theme.of(context).colorScheme.error,
                                    fontStyle: FontStyle.italic,
                                  ),
                                ),
                              ),
                          ],
                        ),
                      ),
                      
                      // Main bill information
                      Card(
                        elevation: 2,
                        child: Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Bill Details',
                                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 16),
                              TextFormField(
                                controller: _billNumberController,
                                decoration: const InputDecoration(
                                  labelText: 'Bill Number',
                                  border: OutlineInputBorder(),
                                  prefixIcon: Icon(Icons.numbers),
                                  helperText: 'Unique identifier for this bill',
                                ),
                                readOnly: true, // Bill number should not be editable
                                textInputAction: TextInputAction.next,
                              ),
                              const SizedBox(height: 16),
                              TextFormField(
                                controller: _titleController,
                                decoration: const InputDecoration(
                                  labelText: 'Title',
                                  border: OutlineInputBorder(),
                                  prefixIcon: Icon(Icons.title),
                                ),
                                validator: FormValidators.required,
                                textInputAction: TextInputAction.next,
                              ),
                              const SizedBox(height: 16),
                              TextFormField(
                                controller: _descriptionController,
                                decoration: const InputDecoration(
                                  labelText: 'Description',
                                  border: OutlineInputBorder(),
                                  prefixIcon: Icon(Icons.description),
                                ),
                                validator: FormValidators.required,
                                maxLines: 3,
                                textInputAction: TextInputAction.next,
                              ),
                              const SizedBox(height: 16),
                              DropdownButtonFormField<BillType>(
                                value: _type,
                                decoration: const InputDecoration(
                                  labelText: 'Bill Type',
                                  border: OutlineInputBorder(),
                                  prefixIcon: Icon(Icons.category),
                                ),
                                items:
                                    BillType.values
                                        .where((type) => _includeInRent ? type != BillType.rent : true)
                                        .map(
                                          (type) => DropdownMenuItem(
                                            value: type,
                                            child: Text(type.name.toUpperCase()),
                                          ),
                                        )
                                        .toList(),
                                onChanged: (value) {
                                  if (value != null) {
                                    setState(() => _type = value);
                                    _updateUtilityFieldsVisibility(value);
                                    
                                    // Auto-fill rent amount if type is rent and we have room info
                                    if (value == BillType.rent && _tenantRoom != null) {
                                      _amountController.text = _tenantRoom!.rentalPrice.toString();
                                      _updateBillSummary();
                                    }
                                  }
                                },
                              ),
                              const SizedBox(height: 16),
                              
                              // Utility Type Selection (moved up when bill type is utility)
                              if (_type == BillType.utility) ...[
                                if (_selectedProperty != null && _utilityBills != null && _utilityBills!.isNotEmpty) ...[
                                  // Standard utility types dropdown
                                DropdownButtonFormField<UtilityType>(
                                  value: _utilityType,
                                  decoration: const InputDecoration(
                                    labelText: 'Utility Type',
                                    border: OutlineInputBorder(),
                                    prefixIcon: Icon(Icons.electrical_services),
                                      helperText: 'Select utility type or choose "OTHER" for custom property rates',
                                  ),
                                  items: UtilityType.values.map((type) {
                                    // Get appropriate icon for each utility type
                                    IconData icon;
                                    switch (type) {
                                      case UtilityType.water:
                                        icon = Icons.water_drop;
                                        break;
                                      case UtilityType.electricity:
                                        icon = Icons.electric_bolt;
                                        break;
                                      case UtilityType.gas:
                                        icon = Icons.local_fire_department;
                                        break;
                                      default:
                                        icon = Icons.miscellaneous_services;
                                    }
                                    
                                      // Show a special label for the 'other' type to indicate custom property rates
                                      String label = type == UtilityType.other ? 
                                          'OTHER (CUSTOM RATES)' : 
                                          type.name.toUpperCase();
                                    
                                    return DropdownMenuItem(
                                      value: type,
                                      child: Container(
                                        constraints: BoxConstraints(maxWidth: 200),
                                      child: Row(
                                          mainAxisSize: MainAxisSize.min,
                                        children: [
                                          Icon(icon, size: 18, color: Theme.of(context).colorScheme.primary),
                                          SizedBox(width: 8),
                                            Expanded(
                                              child: Text(
                                                label,
                                                overflow: TextOverflow.ellipsis,
                                              ),
                                            ),
                                        ],
                                        ),
                                      ),
                                    );
                                  }).toList(),
                                  onChanged: (value) {
                                    if (value != null) {
                                      setState(() {
                                        _utilityType = value;
                                          // Clear any custom utility type when selecting a standard one
                                          _customUtilityType = '';
                                        
                                        // Try to find matching utility bill from property
                                        if (_utilityBills != null && _utilityBills!.isNotEmpty) {
                                          _findMatchingUtilityBill();
                                          
                                          AppLogger.debug('Selected utility type: ${value.name}, found bill: ${_selectedUtilityBill?.name}, rate: ${_selectedUtilityBill?.rate}');
                                        } else {
                                          AppLogger.debug('No utility bills available for the property');
                                        }
                                      });
                                    }
                                  },
                                ),
                                const SizedBox(height: 16),
                                
                                  // Property utility bills dropdown
                                  Card(
                                    margin: const EdgeInsets.only(bottom: 16),
                                    color: Theme.of(context).colorScheme.surfaceContainerHighest.withAlpha(51),
                                    child: Padding(
                                      padding: const EdgeInsets.all(16),
                                      child: Column(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          Row(
                                            children: [
                                              Icon(
                                                Icons.business,
                                                size: 20,
                                                color: Theme.of(context).colorScheme.primary,
                                              ),
                                              const SizedBox(width: 8),
                                              Text(
                                                'Property Utility Rates',
                                                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                                  fontWeight: FontWeight.bold,
                                                ),
                                              ),
                                            ],
                                          ),
                                          const SizedBox(height: 8),
                                          Text(
                                            'Property: ${_selectedProperty?.name}',
                                            style: TextStyle(
                                              fontSize: 14,
                                              fontWeight: FontWeight.w500,
                                              color: Colors.black87,
                                            ),
                                          ),
                                          const SizedBox(height: 8),
                                          const Text(
                                            'Select from utility rates configured for this property:',
                                            style: TextStyle(fontSize: 13, color: Colors.black54),
                                          ),
                                          const SizedBox(height: 12),
                                  DropdownButtonFormField<UtilityBill>(
                                    value: _selectedUtilityBill,
                                    decoration: const InputDecoration(
                                              labelText: 'Property Utility Bill',
                                      border: OutlineInputBorder(),
                                              prefixIcon: Icon(Icons.receipt_long),
                                              helperText: 'Select from property-specific utility rates',
                                    ),
                                    items: _utilityBills!.map((bill) => DropdownMenuItem(
                                      value: bill,
                                              child: Container(
                                                constraints: BoxConstraints(maxWidth: 200),
                                                child: Text(
                                                  '${bill.name} (${bill.formattedRate}${bill.unit != null ? '/${bill.unit}' : ''})',
                                                  overflow: TextOverflow.ellipsis,
                                                ),
                                              ),
                                    )).toList(),
                                    onChanged: (value) {
                                      if (value != null) {
                                        setState(() {
                                          _selectedUtilityBill = value;
                                                  _customUtilityType = value.name;
                                          
                                                  // Update utility type if it matches a standard one
                                                  bool foundMatch = false;
                                          for (var type in UtilityType.values) {
                                            if (value.name.toLowerCase().contains(type.name.toLowerCase())) {
                                              _utilityType = type;
                                                      foundMatch = true;
                                              break;
                                            }
                                          }
                                                  
                                                  // If no match with standard types, set to Other
                                                  if (!foundMatch) {
                                                    _utilityType = UtilityType.other;
                                          }
                                          
                                          // Update rate
                                          _updateRateFromSelectedUtility();
                                          
                                          AppLogger.debug('Selected utility bill: ${value.name}, rate: ${value.rate}');
                                        });
                                      }
                                    },
                                  ),
                                          if (_selectedUtilityBill != null) ...[
                                  const SizedBox(height: 8),
                                  Container(
                                    padding: const EdgeInsets.all(8),
                                    decoration: BoxDecoration(
                                      color: Theme.of(context).colorScheme.primaryContainer.withAlpha(51),
                                      borderRadius: BorderRadius.circular(4),
                                    ),
                                    child: Row(
                                      children: [
                                        Icon(Icons.info_outline, size: 16, color: Theme.of(context).colorScheme.primary),
                                        const SizedBox(width: 8),
                                        Expanded(
                                                    child: Column(
                                                      crossAxisAlignment: CrossAxisAlignment.start,
                                                      children: [
                                                        Text(
                                                          'Selected: ${_selectedUtilityBill!.name}',
                                            style: TextStyle(
                                              fontWeight: FontWeight.bold,
                                              color: Theme.of(context).colorScheme.primary,
                                            ),
                                                        ),
                                                        Text(
                                                          'Rate: ${_selectedUtilityBill!.formattedRate}${_selectedUtilityBill!.unit != null ? '/${_selectedUtilityBill!.unit}' : ''}',
                                                          style: TextStyle(
                                                            fontSize: 13,
                                                            color: Theme.of(context).colorScheme.primary,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                                ],
                                              ),
                                            ),
                                          ],
                                        ],
                                      ),
                                    ),
                                  ),
                                ],
                                
                                // Meter readings section (moved here when utility is selected)
                                Card(
                                  margin: const EdgeInsets.only(bottom: 16),
                                  color: Theme.of(context).colorScheme.surfaceContainerHighest.withAlpha(51),
                                  child: Padding(
                                    padding: const EdgeInsets.all(16),
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          'Meter Readings',
                                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                        const SizedBox(height: 16),
                                        SwitchListTile(
                                          title: const Text('Enter Units Manually'),
                                          subtitle: const Text('Toggle to directly enter units consumed'),
                                          value: _useManualUnits,
                                          onChanged: (value) {
                                            setState(() {
                                              _useManualUnits = value;
                                            });
                                          },
                                        ),
                                        const SizedBox(height: 8),
                                        
                                        // Show either meter readings or direct units based on toggle
                                        if (!_useManualUnits) ...[
                                          Row(
                                            children: [
                                              Expanded(
                                                child: TextFormField(
                                                  controller: _previousMeterController,
                                                  decoration: const InputDecoration(
                                                    labelText: 'Previous Meter Reading',
                                                    border: OutlineInputBorder(),
                                                    prefixIcon: Icon(Icons.history),
                                                  ),
                                                  keyboardType: const TextInputType.numberWithOptions(decimal: true),
                                                  inputFormatters: [
                                                    FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d{0,2}')),
                                                  ],
                                                  onChanged: (_) {
                                                    _calculateAmount();
                                                    setState(() {}); // Refresh UI to update calculation display
                                                  },
                                                ),
                                              ),
                                              const SizedBox(width: 16),
                                              Expanded(
                                                child: TextFormField(
                                                  controller: _currentMeterController,
                                                  decoration: const InputDecoration(
                                                    labelText: 'Current Meter Reading',
                                                    border: OutlineInputBorder(),
                                                    prefixIcon: Icon(Icons.speed),
                                                  ),
                                                  keyboardType: const TextInputType.numberWithOptions(decimal: true),
                                                  inputFormatters: [
                                                    FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d{0,2}')),
                                                  ],
                                                  onChanged: (_) {
                                                    _calculateAmount();
                                                    setState(() {}); // Refresh UI to update calculation display
                                                  },
                                                ),
                                              ),
                                            ],
                                          ),
                                          // Add calculation summary for meter readings
                                          if (_previousMeterController.text.isNotEmpty &&
                                              _currentMeterController.text.isNotEmpty &&
                                              _ratePerUnitController.text.isNotEmpty)
                                            Padding(
                                              padding: const EdgeInsets.only(top: 8.0),
                                              child: Container(
                                                padding: const EdgeInsets.all(8.0),
                                                decoration: BoxDecoration(
                                                  color: Theme.of(context).colorScheme.surfaceContainerHighest.withAlpha(77),
                                                  borderRadius: BorderRadius.circular(8.0),
                                                ),
                                                child: Column(
                                                  crossAxisAlignment: CrossAxisAlignment.start,
                                                  children: [
                                                    Text(
                                                      'Calculation:',
                                                      style: TextStyle(
                                                        fontWeight: FontWeight.bold,
                                                        color: Theme.of(context).colorScheme.primary,
                                                      ),
                                                    ),
                                                    const SizedBox(height: 4),
                                                    Text(
                                                      'Current (${_currentMeterController.text}) - Previous (${_previousMeterController.text}) = ${_unitsConsumedController.text} units',
                                                      style: const TextStyle(fontSize: 13),
                                                    ),
                                                    const SizedBox(height: 2),
                                                    Text(
                                                      '${_unitsConsumedController.text} units × KSH ${_ratePerUnitController.text} = KSH ${_amountController.text}',
                                                      style: const TextStyle(fontSize: 13),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            ),
                                        ] else ...[
                                          TextFormField(
                                            controller: _unitsConsumedController,
                                            decoration: const InputDecoration(
                                              labelText: 'Units Consumed',
                                              border: OutlineInputBorder(),
                                              prefixIcon: Icon(Icons.straighten),
                                            ),
                                            keyboardType: const TextInputType.numberWithOptions(decimal: true),
                                            inputFormatters: [
                                              FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d{0,2}')),
                                            ],
                                            onChanged: (_) {
                                              _calculateAmount();
                                              setState(() {}); // Refresh UI to update calculation display
                                            },
                                          ),
                                          // Add calculation summary for manual units
                                          if (_unitsConsumedController.text.isNotEmpty &&
                                              _ratePerUnitController.text.isNotEmpty)
                                            Padding(
                                              padding: const EdgeInsets.only(top: 8.0),
                                              child: Container(
                                                padding: const EdgeInsets.all(8.0),
                                                decoration: BoxDecoration(
                                                  color: Theme.of(context).colorScheme.surfaceContainerHighest.withAlpha(77),
                                                  borderRadius: BorderRadius.circular(8.0),
                                                ),
                                                child: Column(
                                                  crossAxisAlignment: CrossAxisAlignment.start,
                                                  children: [
                                                    Text(
                                                      'Calculation:',
                                                      style: TextStyle(
                                                        fontWeight: FontWeight.bold,
                                                        color: Theme.of(context).colorScheme.primary,
                                                      ),
                                                    ),
                                                    const SizedBox(height: 4),
                                                    Text(
                                                      '${_unitsConsumedController.text} units × KSH ${_ratePerUnitController.text} = KSH ${_amountController.text}',
                                                      style: const TextStyle(fontSize: 13),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            ),
                                        ],
                                        const SizedBox(height: 16),
                                        
                                        TextFormField(
                                          controller: _ratePerUnitController,
                                          decoration: InputDecoration(
                                            labelText: _selectedUtilityBill?.unit != null 
                                                ? 'Rate per ${_selectedUtilityBill!.unit}' 
                                                : 'Rate per Unit',
                                            border: const OutlineInputBorder(),
                                            prefixText: 'KSH ',
                                            prefixIcon: const Icon(Icons.attach_money),
                                            helperText: _selectedUtilityBill != null 
                                                ? 'Using rate from ${_selectedUtilityBill!.name}' 
                                                : 'Enter the cost per unit',
                                            suffixText: _selectedUtilityBill?.unit,
                                          ),
                                          keyboardType: const TextInputType.numberWithOptions(decimal: true),
                                          inputFormatters: [
                                            FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d{0,2}')),
                                          ],
                                          onChanged: (value) {
                                            _calculateAmount();
                                            setState(() {}); // Refresh UI to update calculation display
                                          },
                                          readOnly: _selectedUtilityBill != null,
                                          enabled: _selectedUtilityBill == null,
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ],
                            ],
                          ),
                        ),
                      ),
                      
                      // Amount field
                      Card(
                        elevation: 2,
                        margin: const EdgeInsets.symmetric(vertical: 16),
                        child: Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Amount',
                                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 16),
                              TextFormField(
                                controller: _amountController,
                                decoration: const InputDecoration(
                                  labelText: 'Amount',
                                  border: OutlineInputBorder(),
                                  prefixText: 'KSH ',
                                  prefixIcon: Icon(Icons.attach_money),
                                ),
                                keyboardType: const TextInputType.numberWithOptions(decimal: true),
                                inputFormatters: [
                                  FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}')),
                                ],
                                validator: FormValidators.required,
                                onChanged: (value) {
                                  _updateBillSummary();
                                  _updateTotalDue();
                                },
                                enabled: true,
                                ),
                            ],
                          ),
                        ),
                      ),
                      
                      // Due date and recurrence
                      Card(
                        elevation: 2,
                        margin: const EdgeInsets.symmetric(vertical: 16),
                        child: Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Payment Schedule',
                                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 16),
                              // Due date picker
                              InkWell(
                                onTap: () async {
                                  final now = DateTime.now();
                                  final initialDate = _dueDate.isBefore(now) ? now : _dueDate;
                                  
                                  final pickedDate = await showDatePicker(
                                    context: context,
                                    initialDate: initialDate,
                                    firstDate: now,
                                    lastDate: DateTime.now().add(const Duration(days: 365 * 5)),
                                  );
                                  
                                  if (pickedDate != null) {
                                    setState(() => _dueDate = pickedDate);
                                  }
                                },
                                child: InputDecorator(
                                  decoration: const InputDecoration(
                                    labelText: 'Due Date',
                                    border: OutlineInputBorder(),
                                    prefixIcon: Icon(Icons.calendar_today),
                                  ),
                                  child: Text(
                                    _dueDate.toString().split(' ')[0], // Format date as YYYY-MM-DD
                                  ),
                                ),
                              ),
                              const SizedBox(height: 16),
                              // Recurrence dropdown
                              DropdownButtonFormField<RecurrenceType>(
                                value: _recurrence,
                                decoration: const InputDecoration(
                                  labelText: 'Recurrence',
                                  border: OutlineInputBorder(),
                                  prefixIcon: Icon(Icons.repeat),
                                ),
                                items:
                                    RecurrenceType.values
                                        .map(
                                          (type) => DropdownMenuItem(
                                            value: type,
                                            child: Text(type.name.toUpperCase()),
                                          ),
                                        )
                                        .toList(),
                                onChanged: (value) {
                                  if (value != null) {
                                    setState(() => _recurrence = value);
                                  }
                                },
                              ),
                            ],
                          ),
                        ),
                      ),
                      
                      // Notes field - moved outside its original card
                      Card(
                        elevation: 2,
                        margin: const EdgeInsets.only(bottom: 16),
                        child: Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Additional Notes',
                                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 16),
                              TextFormField(
                                controller: _notesController,
                                decoration: const InputDecoration(
                                  labelText: 'Notes (Optional)',
                                  border: OutlineInputBorder(),
                                  prefixIcon: Icon(Icons.note),
                                ),
                                maxLines: 3,
                              ),
                            ],
                          ),
                        ),
                      ),
                      
                      const SizedBox(height: 24),
                      // Save button - ensure it's always visible at the bottom
                      ElevatedButton.icon(
                        icon: const Icon(Icons.save),
                        label: Text(
                          widget.bill == null ? 'Add Bill' : 'Update Bill',
                          style: const TextStyle(fontSize: 16),
                        ),
                        style: ElevatedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          backgroundColor: Theme.of(context).colorScheme.primary,
                          foregroundColor: Colors.white,
                        ),
                        onPressed: _saveBill,
                      ),
                      const SizedBox(height: 16),
                    ],
                  ),
                ),
              ),
    );
  }

  Future<void> _saveBill() async {
    if (!_formKey.currentState!.validate()) {
      AppLogger.error('Form validation failed.');
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please fix the errors in the form before saving.'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() => _isLoading = true);

    try {
      // Prepare utility-related fields
      double? previousMeterReading;
      double? currentMeterReading;
      double? unitConsumed;
      double? ratePerUnit;
      UtilityType? utilityType;
      
      if (_showUtilityFields) {
        // Set utility type - use the standard enum if it matches, otherwise use 'other'
        utilityType = _utilityType;
        
        if (_ratePerUnitController.text.isNotEmpty) {
          ratePerUnit = double.tryParse(_ratePerUnitController.text);
        }
        
        if (_useManualUnits) {
          if (_unitsConsumedController.text.isNotEmpty) {
            unitConsumed = double.tryParse(_unitsConsumedController.text);
          }
        } else {
          if (_previousMeterController.text.isNotEmpty) {
            previousMeterReading = double.tryParse(_previousMeterController.text);
          }
          
          if (_currentMeterController.text.isNotEmpty) {
            currentMeterReading = double.tryParse(_currentMeterController.text);
          }
          
          if (previousMeterReading != null && currentMeterReading != null) {
            unitConsumed = currentMeterReading - previousMeterReading;
          }
        }
      }
      
      // If we're using a custom utility type that doesn't match standard types,
      // add it to the notes field if notes are empty
      String? billNotes = _notesController.text;
      if (_showUtilityFields && _customUtilityType.isNotEmpty && 
          _utilityType == UtilityType.other && billNotes.isEmpty) {
        billNotes = "Custom utility type: $_customUtilityType";
      } else if (_showUtilityFields && _customUtilityType.isNotEmpty && 
                _utilityType == UtilityType.other && billNotes.isNotEmpty) {
        billNotes = "$billNotes\nCustom utility type: $_customUtilityType";
      }
      
      final bill = Bill(
        id: widget.bill?.id,
        title: _titleController.text,
        description: _descriptionController.text,
        amount: double.parse(_amountController.text),
        dueDate: _dueDate,
        status: widget.bill?.status ?? BillStatus.pending,
        type: _type,
        recurrence: _recurrence,
        tenantId: widget.initialTenantId ?? widget.bill?.tenantId,
        propertyId: _selectedPropertyId ?? widget.bill?.propertyId,
        roomId: _tenantRoom?.id ?? widget.bill?.roomId,
        notes: billNotes,
        createdAt: widget.bill?.createdAt ?? DateTime.now(),
        updatedAt: DateTime.now(),
        includeInRent: _includeInRent,
        utilityType: utilityType,
        previousMeterReading: previousMeterReading,
        currentMeterReading: currentMeterReading,
        unitConsumed: unitConsumed,
        ratePerUnit: ratePerUnit,
        billNumber: _billNumberController.text,
      );

      AppLogger.debug('Saving bill: ${bill.toJson()}');

      if (widget.bill == null) {
        await serviceLocator.billService.createBill(bill);
      } else {
        await serviceLocator.billService.updateBill(bill);
      }

      if (mounted) {
        Navigator.of(context).pop(true);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              widget.bill == null
                  ? 'Bill added successfully'
                  : 'Bill updated successfully',
            ),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e, stackTrace) {
      AppLogger.error('Error saving bill: $e', stackTrace);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }
}
