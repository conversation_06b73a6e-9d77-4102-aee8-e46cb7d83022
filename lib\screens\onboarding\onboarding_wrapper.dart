import 'package:flutter/material.dart';
import '../../services/onboarding_service.dart';
import '../../utils/logger.dart';
import 'onboarding_page_1.dart';
import 'onboarding_page_2.dart';

class OnboardingWrapper extends StatefulWidget {
  const OnboardingWrapper({super.key});

  @override
  State<OnboardingWrapper> createState() => _OnboardingWrapperState();
}

class _OnboardingWrapperState extends State<OnboardingWrapper> {
  final PageController _pageController = PageController();
  final OnboardingService _onboardingService = OnboardingService();
  int _currentPage = 0;
  bool _isLoading = false;

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  void _nextPage() {
    if (_currentPage < 1) {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    } else {
      _completeOnboarding();
    }
  }

  void _previousPage() {
    if (_currentPage > 0) {
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _skipOnboarding() {
    _completeOnboarding();
  }

  Future<void> _completeOnboarding() async {
    if (_isLoading) return;
    
    setState(() {
      _isLoading = true;
    });

    try {
      // Mark onboarding as complete
      await _onboardingService.markOnboardingComplete();
      
      if (mounted) {
        // Navigate to auth screen
        Navigator.of(context).pushReplacementNamed('/auth');
      }
    } catch (e) {
      AppLogger.error('Error completing onboarding: $e');
      if (mounted) {
        // Even if there's an error, proceed to auth screen
        Navigator.of(context).pushReplacementNamed('/auth');
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _onPageChanged(int page) {
    setState(() {
      _currentPage = page;
    });
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Scaffold(
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const CircularProgressIndicator(),
              const SizedBox(height: 16),
              Text(
                'Setting up your experience...',
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                ),
              ),
            ],
          ),
        ),
      );
    }

    return Scaffold(
      body: PageView(
        controller: _pageController,
        onPageChanged: _onPageChanged,
        children: [
          OnboardingPage1(
            onNext: _nextPage,
            onSkip: _skipOnboarding,
          ),
          OnboardingPage2(
            onNext: _completeOnboarding,
            onPrevious: _previousPage,
            onSkip: _skipOnboarding,
          ),
        ],
      ),
    );
  }
}
