import 'package:flutter/material.dart';

/// A widget that displays a Supabase connection error notice
class SupabaseErrorNotice extends StatelessWidget {
  /// The original error message
  final String errorMessage;
  
  /// Optional retry callback
  final VoidCallback? onRetry;
  
  /// Optional additional actions
  final List<Widget>? additionalActions;

  const SupabaseErrorNotice({
    super.key,
    required this.errorMessage,
    this.onRetry,
    this.additionalActions,
  });

  /// Extract the relevant parts from a detailed error message
  String _getSimplifiedErrorMessage() {
    if (errorMessage.contains('Connection reset by peer')) {
      return 'Unable to connect to the database server. Your internet connection may be unstable.';
    } else if (errorMessage.contains('ClientException')) {
      return 'Network connection error. Please check your internet and try again.';
    } else {
      return 'An error occurred while connecting to the database. Please try again later.';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.red.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.red.shade200),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: Colors.red.shade100,
              borderRadius: const BorderRadius.vertical(top: Radius.circular(11)),
            ),
            child: Row(
              children: [
                Icon(Icons.cloud_off, color: Colors.red.shade700),
                const SizedBox(width: 12),
                const Text(
                  'Connection Error',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
              ],
            ),
          ),
          
          // Error message
          Padding(
            padding: const EdgeInsets.all(16),
            child: Text(
              _getSimplifiedErrorMessage(),
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[800],
              ),
            ),
          ),
          
          // Actions
          Padding(
            padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                if (onRetry != null)
                  TextButton.icon(
                    onPressed: onRetry,
                    icon: const Icon(Icons.refresh),
                    label: const Text('Retry'),
                    style: TextButton.styleFrom(
                      foregroundColor: Colors.red.shade700,
                    ),
                  ),
                if (additionalActions != null) ...additionalActions!,
              ],
            ),
          ),
        ],
      ),
    );
  }
} 