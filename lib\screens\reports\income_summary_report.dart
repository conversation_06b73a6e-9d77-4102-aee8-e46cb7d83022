import 'package:flutter/material.dart';
import '../../widgets/navigation/app_drawer.dart';
import '../../services/service_locator.dart';
import '../../models/payment/payment_model.dart';
import '../../utils/currency_formatter.dart';
import '../../models/bill/bill.dart';
import 'income_type_view.dart';
import 'date_range_selector.dart';
import '../../utils/logger.dart';

class IncomeSummaryReport extends StatefulWidget {
  const IncomeSummaryReport({super.key});

  @override
  State<IncomeSummaryReport> createState() => _IncomeSummaryReportState();
}

class _IncomeSummaryReportState extends State<IncomeSummaryReport> {
  bool _isLoading = true;
  bool _hasError = false;
  String _errorMessage = '';
  List<Payment> _payments = [];
  List<Payment> _filteredPayments = [];
  String _selectedPeriod = 'year'; // Changed from 'month' to 'year'
  String _selectedView = 'table'; // 'table', 'property', 'payment_type', 'income_type'
  Map<String, double> _incomeByProperty = {};
  Map<PaymentMethod, double> _incomeByPaymentMethod = {};
  DateTimeRange? _selectedDateRange;
  bool _hasShownViewHint = false;  // Track if the view hint has been shown
  
  @override
  void initState() {
    super.initState();
    // Initialize with current year date range instead of month
    final now = DateTime.now();
    _selectedDateRange = DateTimeRange(
      start: DateTime(now.year, 1, 1),
      end: DateTime(now.year, 12, 31),
    );
    _loadData();
    
    // Show initial view hint after a short delay
    Future.delayed(const Duration(seconds: 1), () {
      if (mounted && !_hasShownViewHint) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                Icon(Icons.info_outline,
                  color: Theme.of(context).colorScheme.onPrimary),
                const SizedBox(width: 8),
                const Expanded(
                  child: Text(
                    'Tip: Use the view selector to switch between different income breakdowns',
                  ),
                ),
              ],
            ),
            duration: const Duration(seconds: 5),
            behavior: SnackBarBehavior.floating,
            action: SnackBarAction(
              label: 'Got it',
              onPressed: () {
                setState(() {
                  _hasShownViewHint = true;
                });
                ScaffoldMessenger.of(context).hideCurrentSnackBar();
              },
            ),
          ),
        );
      }
    });
  }
  
  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
      _hasError = false;
      _errorMessage = '';
    });
    
    try {
      // Load payments from service with explicit date range
      AppLogger.info('Loading payments for date range: ${_selectedDateRange?.start} to ${_selectedDateRange?.end}');
      
      final payments = await serviceLocator.paymentService.getAllPayments(
        startDate: _selectedDateRange?.start,
        endDate: _selectedDateRange?.end,
      );
      
      AppLogger.info('Loaded ${payments.length} total payments');
      
      // Log payment details for debugging
      for (var payment in payments) {
        AppLogger.info('Payment ID: ${payment.id}, Amount: ${payment.amount}, Date: ${payment.paymentDate}, Bill IDs: ${payment.billIds}');
        
        // Try to get bill details for each payment
        for (var billId in payment.billIds) {
          try {
            final bill = await serviceLocator.billService.getBill(billId);
            AppLogger.info('Associated Bill: ID: $billId, Type: ${bill.type.name}, Amount: ${bill.amount}');
          } catch (e) {
            AppLogger.error('Failed to get bill $billId details: $e');
          }
        }
      }
      
      if (payments.isEmpty) {
        AppLogger.warning('No payments found for the selected date range');
        setState(() {
          _isLoading = false;
          _payments = [];
          _filteredPayments = [];
          _incomeByProperty = {};
          _incomeByPaymentMethod = {};
        });
        return;
      }
      
      // Filter payments based on selected date range
      _filterPaymentsByDateRange(payments);
      
      if (mounted) {
        setState(() {
          _payments = payments;
          _isLoading = false;
        });
      }
    } catch (e) {
      AppLogger.error('Failed to load payments: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
          _hasError = true;
          _errorMessage = e.toString();
        });
        
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading payment data: ${e.toString()}'),
            backgroundColor: Colors.red,
            action: SnackBarAction(
              label: 'Retry',
              onPressed: _loadData,
              textColor: Colors.white,
            ),
          ),
        );
      }
    }
  }
  
  void _filterPaymentsByDateRange(List<Payment> payments) async {
    if (_selectedDateRange == null) return;
    
    final filteredPayments = payments.where((payment) {
      return payment.paymentDate.isAfter(_selectedDateRange!.start.subtract(const Duration(days: 1))) && 
             payment.paymentDate.isBefore(_selectedDateRange!.end.add(const Duration(days: 1)));
    }).toList();
    
    // Calculate income by property
    final incomeByProperty = <String, double>{};
    final incomeByPaymentMethod = <PaymentMethod, double>{};
    
    // First get all properties to ensure we have their names
    final allProperties = await serviceLocator.propertyService.getAllProperties();
    final propertyNameMap = {for (var p in allProperties) p.id: p.name};
    
    AppLogger.info('Found ${allProperties.length} properties');
    
    for (final payment in filteredPayments) {
      // Add to payment method totals (all payments for method breakdown)
      incomeByPaymentMethod[payment.method] =
          (incomeByPaymentMethod[payment.method] ?? 0) + payment.amount;
      
      String? propertyId;
      String propertyName = 'Other';
      
      try {
        // Try to get property ID from bill relation
        if (payment.billIds.isNotEmpty) {
          final billId = payment.billIds.first;
          try {
            final bill = await serviceLocator.billService.getBill(billId);
            if (bill.propertyId != null) {
              propertyId = bill.propertyId;
            } else if (bill.tenantId != null) {
              // Try to get property through tenant's room
              final tenant = await serviceLocator.tenantService.getTenantById(bill.tenantId!);
              if (tenant != null && tenant.roomId != null) {
                final room = await serviceLocator.roomService.getRoomById(tenant.roomId!);
                if (room != null) {
                  propertyId = room.propertyId;
                }
              }
            }
            
            // Look up property name from our map
            if (propertyId != null) {
              propertyName = propertyNameMap[propertyId] ?? 'Other';
              AppLogger.info('Found property for payment ${payment.id}: $propertyName');
            } else {
              AppLogger.warning('No property found for payment ${payment.id}');
            }
          } catch (billError) {
            AppLogger.error('Error getting bill details: $billError');
          }
        }
      } catch (e) {
        AppLogger.error('Error getting property for payment: $e');
      }
      
      // Add to property totals (only verified payments for income)
      if (payment.status == PaymentStatus.verified) {
        incomeByProperty[propertyName] = (incomeByProperty[propertyName] ?? 0) + payment.amount;
      }
    }
    
    // Log property income distribution
    for (final entry in incomeByProperty.entries) {
      AppLogger.info('Property ${entry.key}: ${entry.value}');
    }
    
    if (mounted) {
      setState(() {
        _filteredPayments = filteredPayments;
        _incomeByProperty = incomeByProperty;
        _incomeByPaymentMethod = incomeByPaymentMethod;
      });
    }
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Income Summary'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        actions: [
          PopupMenuButton<String>(
            icon: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(_getViewIcon()),
                const SizedBox(width: 4),
                Icon(Icons.arrow_drop_down,
                  color: Theme.of(context).colorScheme.primary),
              ],
            ),
            onSelected: (String value) {
              setState(() {
                _selectedView = value;
                _hasShownViewHint = true;
                // Show loading indicator when switching views
                _isLoading = true;
              });
              
              // Show a snackbar with loading status
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Row(
                    children: [
                      const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Text('Loading ${_getViewName(value)} view...'),
                    ],
                  ),
                  duration: const Duration(milliseconds: 800),
                  behavior: SnackBarBehavior.floating,
                ),
              );
              
              // Allow UI to update with loading state before processing
              Future.microtask(() async {
                // Simulate a brief loading time for smoother UX
                await Future.delayed(const Duration(milliseconds: 800));
                if (mounted) {
                  setState(() {
                    _isLoading = false;
                  });
                }
              });
            },
            itemBuilder: (BuildContext context) => <PopupMenuEntry<String>>[
              _buildMenuItem('Monthly View', 'table', Icons.table_chart),
              _buildMenuItem('Property View', 'property', Icons.business),
              _buildMenuItem('Payment Method', 'payment_method', Icons.payment),
              _buildMenuItem('Payment Status', 'payment_status', Icons.verified),
              _buildMenuItem('Income Type', 'income_type', Icons.category),
            ],
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadData,
            tooltip: 'Refresh data',
          ),
        ],
      ),
      drawer: const AppDrawer(),
      body: _buildContent(),
    );
  }
  
  Widget _buildErrorView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 60,
            color: Theme.of(context).colorScheme.error,
          ),
          const SizedBox(height: 16),
          Text(
            'Failed to load payment data',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Theme.of(context).colorScheme.error,
            ),
          ),
          const SizedBox(height: 8),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 32.0),
            child: Text(
              _errorMessage,
              textAlign: TextAlign.center,
              style: TextStyle(
                color: Theme.of(context).colorScheme.error.withValues(alpha: 196),
              ),
            ),
          ),
          const SizedBox(height: 16),
          ElevatedButton.icon(
            onPressed: _loadData,
            icon: const Icon(Icons.refresh),
            label: const Text('Retry'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.error,
              foregroundColor: Theme.of(context).colorScheme.onError,
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildEmptyDataView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.payment_outlined,
            size: 60,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          const Text(
            'No payment data available',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'Record payments to see income reports',
            style: TextStyle(color: Colors.grey),
          ),
          const SizedBox(height: 16),
          ElevatedButton.icon(
            onPressed: () {
              Navigator.pushNamed(context, '/select-tenant-for-payment');
            },
            icon: const Icon(Icons.add),
            label: const Text('Record Payment'),
          ),
        ],
      ),
    );
  }
  
  Widget _buildSummaryCards() {
    // Calculate total income (only verified payments)
    final verifiedPayments = _filteredPayments.where((payment) => payment.status == PaymentStatus.verified);
    final totalIncome = verifiedPayments.fold<double>(
      0,
      (sum, payment) => sum + payment.amount,
    );

    // Calculate rejected payments
    final rejectedPayments = _filteredPayments.where((payment) => payment.status == PaymentStatus.rejected);
    final totalRejected = rejectedPayments.fold<double>(
      0,
      (sum, payment) => sum + payment.amount,
    );

    // Calculate pending payments
    final pendingPayments = _filteredPayments.where((payment) => payment.status == PaymentStatus.pending);
    final totalPending = pendingPayments.fold<double>(
      0,
      (sum, payment) => sum + payment.amount,
    );

    // Calculate this month's income (verified only)
    final now = DateTime.now();
    final thisMonth = verifiedPayments.where((payment) {
      final paymentDate = payment.paymentDate;
      return paymentDate.year == now.year && paymentDate.month == now.month;
    }).fold<double>(0, (sum, payment) => sum + payment.amount);

    // Calculate last month's income (verified only)
    final lastMonthDate = DateTime(now.year, now.month - 1);
    final lastMonth = _payments.where((payment) {
      final paymentDate = payment.paymentDate;
      return paymentDate.year == lastMonthDate.year &&
             paymentDate.month == lastMonthDate.month &&
             payment.status == PaymentStatus.verified;
    }).fold<double>(0, (sum, payment) => sum + payment.amount);

    // Calculate change percentage
    final changePercentage = lastMonth > 0
        ? ((thisMonth - lastMonth) / lastMonth * 100).toStringAsFixed(1)
        : '0.0';

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      child: Column(
        children: [
          // First row - Total Income and This Month
          Row(
            children: [
              Expanded(
                child: _buildSummaryCard(
                  'Total Income',
                  CurrencyFormatter.formatCompactAmount(totalIncome),
                  Icons.attach_money,
                  Colors.green,
                  amount: totalIncome,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildSummaryCard(
                  'This Month',
                  CurrencyFormatter.formatCompactAmount(thisMonth),
                  Icons.calendar_today,
                  Colors.blue,
                  subtitle: '${thisMonth > lastMonth ? '+' : ''}$changePercentage% vs last month',
                  subtitleColor: thisMonth >= lastMonth ? Colors.green : Colors.red,
                  amount: thisMonth,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          // Second row - Rejected and Pending
          Row(
            children: [
              Expanded(
                child: _buildSummaryCard(
                  'Rejected',
                  CurrencyFormatter.formatCompactAmount(totalRejected),
                  Icons.cancel,
                  Colors.red,
                  subtitle: '${rejectedPayments.length} payments',
                  amount: totalRejected,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildSummaryCard(
                  'Pending',
                  CurrencyFormatter.formatCompactAmount(totalPending),
                  Icons.pending,
                  Colors.orange,
                  subtitle: '${pendingPayments.length} payments',
                  amount: totalPending,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
  
  Widget _buildSummaryCard(
    String title,
    String value,
    IconData icon,
    Color color, {
    String? subtitle,
    Color? subtitleColor,
    double? amount,
  }) {
    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(color: Colors.grey.withValues(alpha: 26)),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: color, size: 24),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            GestureDetector(
              onTap: amount != null ? () => _showFullAmountDialog(amount, title) : null,
              child: Text(
              value,
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: color,
                  decoration: amount != null ? TextDecoration.underline : null,
                ),
              ),
            ),
            if (subtitle != null) ...[
              const SizedBox(height: 4),
              Text(
                subtitle,
                style: TextStyle(
                  fontSize: 12,
                  color: subtitleColor ?? Colors.grey,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
  
  Widget _buildPropertyList() {
    // Sort properties by income (highest first)
    final sortedProperties = _incomeByProperty.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));
    
    return sortedProperties.isEmpty
        ? Center(child: Text('No property data available', style: TextStyle(color: Colors.grey[600])))
        : ListView.builder(
            padding: const EdgeInsets.all(16.0),
            itemCount: sortedProperties.length,
            itemBuilder: (context, index) {
              final entry = sortedProperties[index];
              final propertyName = entry.key;
              final amount = entry.value;
              
              // Calculate percentage of total income (verified payments only)
              final totalIncome = _filteredPayments
                  .where((payment) => payment.status == PaymentStatus.verified)
                  .fold<double>(0, (sum, payment) => sum + payment.amount);
              final percentage = totalIncome > 0 
                  ? (amount / totalIncome * 100).toStringAsFixed(1) 
                  : '0.0';
              
              return Card(
                margin: const EdgeInsets.only(bottom: 12.0),
                elevation: 0,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                  side: BorderSide(color: Colors.grey.withValues(alpha: 26)),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(12.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(
                            child: Text(
                              propertyName,
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: 16,
                              ),
                            ),
                          ),
                          GestureDetector(
                            onTap: () => _showFullAmountDialog(amount, 'Income from $propertyName'),
                            child: Text(
                              CurrencyFormatter.formatCompactAmount(amount),
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              color: Colors.green,
                              fontSize: 16,
                                decoration: TextDecoration.underline,
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          Expanded(
                            child: ClipRRect(
                              borderRadius: BorderRadius.circular(4),
                              child: LinearProgressIndicator(
                                value: totalIncome > 0 ? amount / totalIncome : 0,
                                backgroundColor: Colors.grey.shade200,
                                color: Theme.of(context).colorScheme.primary,
                                minHeight: 8,
                              ),
                            ),
                          ),
                          const SizedBox(width: 8),
                          Text(
                            '$percentage%',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey.shade600,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              );
            },
          );
  }
  
  Widget _buildTable() {
    // Group payments by month
    final paymentsByMonth = <int, double>{};
    final pendingBillsByMonth = <int, List<Bill>>{};
    final pendingAmountByMonth = <int, double>{};
    
    // Only count verified payments in income
    for (final payment in _filteredPayments.where((p) => p.status == PaymentStatus.verified)) {
      final month = payment.paymentDate.month;
      final year = payment.paymentDate.year;
      // Use a composite key for month and year (e.g., 202401 for January 2024)
      final monthYearKey = year * 100 + month;
      paymentsByMonth[monthYearKey] = (paymentsByMonth[monthYearKey] ?? 0) + payment.amount;
    }
    
    // Load pending bills data
    return FutureBuilder<List<Bill>>(
      future: serviceLocator.billService.getBillsByStatus(BillStatus.pending),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }
        
        // Process pending bills
        if (snapshot.hasData) {
          final pendingBills = snapshot.data!;
          
          for (final bill in pendingBills) {
            final month = bill.dueDate.month;
            final year = bill.dueDate.year;
            final monthYearKey = year * 100 + month;
            pendingBillsByMonth[monthYearKey] = [...(pendingBillsByMonth[monthYearKey] ?? []), bill];
            pendingAmountByMonth[monthYearKey] = (pendingAmountByMonth[monthYearKey] ?? 0) + bill.amount;
          }
        }
    
        // Get all months from both payments and pending bills
        final allMonthYears = <int>{
          ...paymentsByMonth.keys,
          ...pendingBillsByMonth.keys,
        }.toList()..sort();
        
        // Limit to the most recent 12 months
        final now = DateTime.now();
        final currentMonthYear = now.year * 100 + now.month;
        
        // Filter months to include only the last 12 months up to the current month
        final filteredMonthYears = allMonthYears
            .where((monthYear) => monthYear <= currentMonthYear)
            .toList()
            ..sort((a, b) => b.compareTo(a)); // Sort in descending order (newest first)
        
        // Take only the last 12 months
        final limitedMonthYears = filteredMonthYears.take(12).toList()..sort(); // Sort in ascending order for display
        
        return limitedMonthYears.isEmpty
        ? Center(child: Text('No monthly data available', style: TextStyle(color: Colors.grey[600])))
        : SingleChildScrollView(
            padding: const EdgeInsets.all(16.0),
            child: Card(
              elevation: 0,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
                side: BorderSide(color: Colors.grey.withValues(alpha: 26)),
              ),
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                          'Monthly Income & Pending Bills',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    Table(
                      columnWidths: const {
                            0: FlexColumnWidth(3),    // Month
                            1: FlexColumnWidth(3),    // Income
                            2: FlexColumnWidth(3),    // Pending Bills
                      },
                      children: [
                        // Header row
                        TableRow(
                          decoration: BoxDecoration(
                            border: Border(
                              bottom: BorderSide(
                                color: Colors.grey.withValues(alpha: 51),
                              ),
                            ),
                          ),
                          children: const [
                            Padding(
                              padding: EdgeInsets.symmetric(vertical: 12.0),
                              child: Text(
                                'Month',
                                style: TextStyle(fontWeight: FontWeight.bold),
                              ),
                            ),
                            Padding(
                              padding: EdgeInsets.symmetric(vertical: 12.0),
                              child: Text(
                                'Income',
                                style: TextStyle(fontWeight: FontWeight.bold),
                                textAlign: TextAlign.right,
                              ),
                            ),
                                Padding(
                                  padding: EdgeInsets.symmetric(vertical: 12.0),
                                  child: Text(
                                    'Pending Bills',
                                style: TextStyle(fontWeight: FontWeight.bold),
                                textAlign: TextAlign.right,
                              ),
                            ),
                          ],
                        ),
                        // Data rows
                            ...limitedMonthYears.map((monthYear) {
                              final month = monthYear % 100;
                              final year = monthYear ~/ 100;
                          final monthNames = [
                            'January', 'February', 'March', 'April', 'May', 'June',
                            'July', 'August', 'September', 'October', 'November', 'December'
                          ];
                              
                              final income = paymentsByMonth[monthYear] ?? 0.0;
                              final pendingBills = pendingBillsByMonth[monthYear] ?? [];
                              final pendingAmount = pendingAmountByMonth[monthYear] ?? 0.0;
                              final pendingCount = pendingBills.length;
                              
                              // Check if this is the current month
                              final isCurrentMonth = month == now.month && year == now.year;
                              
                          return TableRow(
                            decoration: BoxDecoration(
                                  color: isCurrentMonth ? Colors.blue.withValues(alpha: 38) : null,
                              border: Border(
                                bottom: BorderSide(
                                  color: Colors.grey.withValues(alpha: 26),
                                ),
                              ),
                            ),
                            children: [
                                  // Month
                              Padding(
                                padding: const EdgeInsets.symmetric(vertical: 12.0),
                                    child: Row(
                                      children: [
                                        if (isCurrentMonth)
                                          Padding(
                                            padding: const EdgeInsets.only(right: 6.0),
                                            child: Icon(
                                              Icons.calendar_today,
                                              size: 16,
                                              color: Theme.of(context).colorScheme.primary,
                                            ),
                                          ),
                                        Expanded(
                                          child: Text(
                                            '${monthNames[month - 1]} ${year}',
                                            style: TextStyle(
                                              fontWeight: isCurrentMonth ? FontWeight.bold : FontWeight.normal,
                                              color: isCurrentMonth ? Theme.of(context).colorScheme.primary : null,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  // Income
                              Padding(
                                padding: const EdgeInsets.symmetric(vertical: 12.0),
                                    child: Container(
                                      padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
                                      decoration: income > 0 ? BoxDecoration(
                                        color: Colors.green.withValues(alpha: 40),
                                        borderRadius: BorderRadius.circular(4),
                                      ) : null,
                                child: GestureDetector(
                                  onTap: income > 0 ? () => _showFullAmountDialog(income, '${monthNames[month - 1]} ${year} Income') : null,
                                  child: Material(
                                    color: Colors.transparent,
                                    child: InkWell(
                                      onTap: income > 0 ? () => _showFullAmountDialog(income, '${monthNames[month - 1]} ${year} Income') : null,
                                      borderRadius: BorderRadius.circular(4),
                                      splashColor: Colors.green.withValues(alpha: 77),
                                      child: Padding(
                                        padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                                        child: Text(
                                          CurrencyFormatter.formatCompactAmount(income),
                                          style: TextStyle(
                                            color: income > 0 ? Colors.green.shade700 : Colors.grey,
                                            fontWeight: FontWeight.w500,
                                            decoration: income > 0 ? TextDecoration.underline : null,
                                          ),
                                          textAlign: TextAlign.right,
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                                ),
                                  ),
                                  // Pending Bills - Redesigned to show full amount
                                  Padding(
                                    padding: const EdgeInsets.symmetric(vertical: 8.0),
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.end,
                                      children: [
                                        // Amount
                                        Container(
                                          padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
                                          decoration: pendingAmount > 0 ? BoxDecoration(
                                            color: Colors.orange.withValues(alpha: 40),
                                            borderRadius: BorderRadius.circular(4),
                                          ) : null,
                                          child: GestureDetector(
                                            onTap: pendingAmount > 0 ? () => _showFullAmountDialog(pendingAmount, '${monthNames[month - 1]} ${year} Pending Bills') : null,
                                            child: Material(
                                              color: Colors.transparent,
                                              child: InkWell(
                                                onTap: pendingAmount > 0 ? () => _showFullAmountDialog(pendingAmount, '${monthNames[month - 1]} ${year} Pending Bills') : null,
                                                borderRadius: BorderRadius.circular(4),
                                                splashColor: Colors.orange.withValues(alpha: 77),
                                                child: Padding(
                                                  padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                                                  child: Text(
                                                    CurrencyFormatter.formatCompactAmount(pendingAmount),
                                                    style: TextStyle(
                                                      color: pendingAmount > 0 ? Colors.orange.shade800 : Colors.grey,
                                                      fontWeight: FontWeight.w500,
                                                      decoration: pendingAmount > 0 ? TextDecoration.underline : null,
                                                    ),
                                                    textAlign: TextAlign.right,
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ),
                                        ),
                                        // Count badge
                                        if (pendingCount > 0)
                                          Padding(
                                            padding: const EdgeInsets.only(top: 4.0),
                                            child: Container(
                                              padding: const EdgeInsets.symmetric(horizontal: 6.0, vertical: 2.0),
                                              decoration: BoxDecoration(
                                                color: Colors.orange.withValues(alpha: 60),
                                                borderRadius: BorderRadius.circular(12),
                                              ),
                                              child: Text(
                                                '$pendingCount bills',
                                                style: TextStyle(
                                                  color: Colors.orange.shade800,
                                                  fontWeight: FontWeight.bold,
                                                  fontSize: 12,
                                                ),
                                              ),
                                            ),
                                          ),
                                      ],
                                ),
                              ),
                            ],
                          );
                        }),
                        // Total row
                        TableRow(
                          decoration: BoxDecoration(
                            color: Colors.grey.withValues(alpha: 13),
                          ),
                          children: [
                                // Total label
                            const Padding(
                              padding: EdgeInsets.symmetric(vertical: 12.0),
                              child: Text(
                                'Total',
                                style: TextStyle(fontWeight: FontWeight.bold),
                              ),
                            ),
                                // Total income
                            Padding(
                              padding: const EdgeInsets.symmetric(vertical: 12.0),
                              child: GestureDetector(
                                onTap: () {
                                  final totalIncome = limitedMonthYears.fold<double>(
                                    0,
                                    (sum, monthYear) => sum + (paymentsByMonth[monthYear] ?? 0),
                                  );
                                  _showFullAmountDialog(totalIncome, 'Total Income');
                                },
                                child: Material(
                                  color: Colors.transparent,
                                  child: InkWell(
                                    onTap: () {
                                      final totalIncome = limitedMonthYears.fold<double>(
                                        0,
                                        (sum, monthYear) => sum + (paymentsByMonth[monthYear] ?? 0),
                                      );
                                      _showFullAmountDialog(totalIncome, 'Total Income');
                                    },
                                    borderRadius: BorderRadius.circular(4),
                                    splashColor: Colors.green.withValues(alpha: 77),
                                    child: Padding(
                                      padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                                      child: Text(
                                        CurrencyFormatter.formatCompactAmount(
                                          limitedMonthYears.fold<double>(
                                            0,
                                            (sum, monthYear) => sum + (paymentsByMonth[monthYear] ?? 0),
                                          ),
                                        ),
                                        style: const TextStyle(
                                          fontWeight: FontWeight.bold,
                                          color: Colors.green,
                                          decoration: TextDecoration.underline,
                                        ),
                                        textAlign: TextAlign.right,
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                                ),
                                // Total pending - Redesigned to show full amount
                                Padding(
                                  padding: const EdgeInsets.symmetric(vertical: 8.0),
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.end,
                                    children: [
                                      // Total amount
                                      GestureDetector(
                                        onTap: () {
                                          final totalPending = limitedMonthYears.fold<double>(
                                            0,
                                            (sum, monthYear) => sum + (pendingAmountByMonth[monthYear] ?? 0),
                                          );
                                          _showFullAmountDialog(totalPending, 'Total Pending Bills');
                                        },
                                        child: Material(
                                          color: Colors.transparent,
                                          child: InkWell(
                                            onTap: () {
                                              final totalPending = limitedMonthYears.fold<double>(
                                                0,
                                                (sum, monthYear) => sum + (pendingAmountByMonth[monthYear] ?? 0),
                                              );
                                              _showFullAmountDialog(totalPending, 'Total Pending Bills');
                                            },
                                            borderRadius: BorderRadius.circular(4),
                                            splashColor: Colors.orange.withValues(alpha: 77),
                                            child: Padding(
                                              padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                                              child: Text(
                                                CurrencyFormatter.formatCompactAmount(
                                                  limitedMonthYears.fold<double>(
                                                    0,
                                                    (sum, monthYear) => sum + (pendingAmountByMonth[monthYear] ?? 0),
                                                  ),
                                                ),
                                                style: const TextStyle(
                                                  fontWeight: FontWeight.bold,
                                                  color: Colors.orange,
                                                  decoration: TextDecoration.underline,
                                                ),
                                                textAlign: TextAlign.right,
                                              ),
                                            ),
                                          ),
                                        ),
                                      ),
                                      // Total count
                                      Padding(
                                        padding: const EdgeInsets.only(top: 4.0),
                                        child: Container(
                                          padding: const EdgeInsets.symmetric(horizontal: 6.0, vertical: 2.0),
                                          decoration: BoxDecoration(
                                            color: Colors.orange.withValues(alpha: 60),
                                            borderRadius: BorderRadius.circular(12),
                                          ),
                                          child: Text(
                                            '${limitedMonthYears.fold<int>(0, (sum, monthYear) => sum + (pendingBillsByMonth[monthYear]?.length ?? 0))} bills',
                                            style: TextStyle(
                                              color: Colors.orange.shade800,
                                              fontWeight: FontWeight.bold,
                                              fontSize: 12,
                                            ),
                                          ),
                              ),
                            ),
                          ],
                                  ),
                        ),
                      ],
                    ),
                          ],
                        ),
                        
                        // Add bill type breakdown
                        if (snapshot.hasData && snapshot.data!.isNotEmpty) ...[
                          const SizedBox(height: 24),
                          const Text(
                            'Pending Bills by Type',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 12),
                          _buildPendingBillsTypeBreakdown(snapshot.data!),
                        ],
                  ],
                ),
              ),
            ),
              );
      },
    );
  }
  
  Widget _buildPendingBillsTypeBreakdown(List<Bill> pendingBills) {
    // Group bills by type
    final billsByType = <BillType, List<Bill>>{};
    final amountByType = <BillType, double>{};
    
    for (final bill in pendingBills) {
      billsByType[bill.type] = [...(billsByType[bill.type] ?? []), bill];
      amountByType[bill.type] = (amountByType[bill.type] ?? 0) + bill.amount;
    }
    
    // Sort by amount (highest first)
    final sortedTypes = amountByType.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));
    
    return Column(
      children: sortedTypes.map((entry) {
        final type = entry.key;
        final amount = entry.value;
        final count = billsByType[type]!.length;
        final totalAmount = amountByType.values.fold<double>(0, (sum, val) => sum + val);
        final percentage = totalAmount > 0 ? (amount / totalAmount * 100).toStringAsFixed(1) : '0.0';
        
        return Padding(
          padding: const EdgeInsets.only(bottom: 12.0),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: _getBillTypeColor(type).withValues(alpha: 60),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  _getBillTypeIcon(type),
                  color: _getBillTypeColor(type),
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _getBillTypeName(type),
                      style: const TextStyle(
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    Text(
                      '$count bills',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  GestureDetector(
                    onTap: () => _showFullAmountDialog(amount, 'Pending ${_getBillTypeName(type)} Bills'),
                    child: Text(
                      CurrencyFormatter.formatCompactAmount(amount),
                      style: TextStyle(
                        fontWeight: FontWeight.w500,
                        color: _getBillTypeColor(type),
                        decoration: TextDecoration.underline,
                      ),
                    ),
                  ),
                  Text(
                    '$percentage%',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ],
          ),
        );
      }).toList(),
    );
  }
  
  Color _getBillTypeColor(BillType type) {
    switch (type) {
      case BillType.rent:
        return Colors.blue;
      case BillType.utility:
        return Colors.orange;
      case BillType.maintenance:
        return Colors.purple;
      case BillType.service:
        return Colors.teal;
      case BillType.other:
        return Colors.grey;
    }
  }
  
  IconData _getBillTypeIcon(BillType type) {
    switch (type) {
      case BillType.rent:
        return Icons.home;
      case BillType.utility:
        return Icons.electric_bolt;
      case BillType.maintenance:
        return Icons.build;
      case BillType.service:
        return Icons.miscellaneous_services;
      case BillType.other:
        return Icons.receipt;
    }
  }
  
  String _getBillTypeName(BillType type) {
    switch (type) {
      case BillType.rent:
        return 'Rent';
      case BillType.utility:
        return 'Utility';
      case BillType.maintenance:
        return 'Maintenance';
      case BillType.service:
        return 'Service';
      case BillType.other:
        return 'Other';
    }
  }

  PopupMenuItem<String> _buildMenuItem(String label, String value, IconData icon) {
    return PopupMenuItem<String>(
      value: value,
      child: Row(
        children: [
          Icon(icon,
            color: _selectedView == value
              ? Theme.of(context).colorScheme.primary
              : null),
          const SizedBox(width: 8),
          Text(label),
          if (_selectedView == value)
            Icon(Icons.check,
              color: Theme.of(context).colorScheme.primary),
        ],
      ),
    );
  }

  Widget _buildContent() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }
    if (_hasError) {
      return _buildErrorView();
    }
    if (_payments.isEmpty) {
      return _buildEmptyDataView();
    }

    return Column(
      children: [
        if (_selectedDateRange != null)
          DateRangeSelector(
            selectedRange: _selectedDateRange!,
            onRangeSelected: (range) {
              setState(() {
                _selectedDateRange = range;
              });
              _filterPaymentsByDateRange(_payments);
            },
            selectedPeriod: _selectedPeriod,
            onPeriodChanged: (period) {
              setState(() {
                _selectedPeriod = period;
              });
            },
          ),
        _buildSummaryCards(),
        Expanded(
          child: _getSelectedView(),
        ),
      ],
    );
  }

  Widget _getSelectedView() {
    switch (_selectedView) {
      case 'table':
        return Stack(
          children: [
            _buildTable(),
            if (_isLoading)
              Container(
                color: Colors.black26,
                child: const Center(
                  child: CircularProgressIndicator(),
                ),
              ),
          ],
        );
      case 'property':
        return Stack(
          children: [
            _buildPropertyList(),
            if (_isLoading)
              Container(
                color: Colors.black26,
                child: const Center(
                  child: CircularProgressIndicator(),
                ),
              ),
          ],
        );
      case 'payment_method':
        return Stack(
          children: [
            _buildPaymentMethodList(),
            if (_isLoading)
              Container(
                color: Colors.black26,
                child: const Center(
                  child: CircularProgressIndicator(),
                ),
              ),
          ],
        );
      case 'payment_status':
        return Stack(
          children: [
            _buildPaymentStatusList(),
            if (_isLoading)
              Container(
                color: Colors.black26,
                child: const Center(
                  child: CircularProgressIndicator(),
                ),
              ),
          ],
        );
      case 'income_type':
        return _selectedDateRange == null
            ? const Center(child: Text('Please select a date range'))
            : Stack(
                children: [
                  IncomeTypeView(
                    payments: _filteredPayments,
                    selectedDateRange: _selectedDateRange!,
                    onAmountTap: _showFullAmountDialog,
                  ),
                  if (_isLoading)
                    Container(
                      color: Colors.black26,
                      child: const Center(
                        child: CircularProgressIndicator(),
                      ),
                    ),
                ],
              );
      default:
        return _buildTable();
    }
  }
  
  void _showFullAmountDialog(double amount, String title) {
    final currencyCode = CurrencyFormatter.getCurrencyCode();
    final currencySymbol = CurrencyFormatter.getCurrencySymbol();
    
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        elevation: 0,
        backgroundColor: Colors.transparent,
        child: TweenAnimationBuilder<double>(
          duration: const Duration(milliseconds: 300),
          tween: Tween<double>(begin: 0.0, end: 1.0),
          builder: (context, value, child) {
            return Transform.scale(
              scale: value,
              child: Opacity(
                opacity: value,
                child: child,
              ),
            );
          },
          child: Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black26,
                  blurRadius: 10,
                  offset: const Offset(0, 5),
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(10),
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.primaryContainer,
                        shape: BoxShape.circle,
                      ),
                      child: Icon(
                        Icons.attach_money,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        title,
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    IconButton(
                      icon: const Icon(Icons.close, size: 20),
                      onPressed: () => Navigator.of(context).pop(),
                      padding: EdgeInsets.zero,
                      constraints: const BoxConstraints(),
                      splashRadius: 24,
                    ),
                  ],
                ),
                const SizedBox(height: 20),
                TweenAnimationBuilder<double>(
                  duration: const Duration(milliseconds: 600),
                  tween: Tween<double>(begin: 0.0, end: amount),
                  curve: Curves.easeOutCubic,
                  builder: (context, animatedAmount, _) {
                    return Container(
                      padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 20),
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.surfaceContainerHighest,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Column(
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text(
                                currencySymbol,
                                style: TextStyle(
                                  fontSize: 20,
                                  fontWeight: FontWeight.bold,
                                  color: Theme.of(context).colorScheme.primary,
                                ),
                              ),
                              const SizedBox(width: 4),
                              Text(
                                CurrencyFormatter.formatNumber(animatedAmount),
                                style: TextStyle(
                                  fontSize: 28,
                                  fontWeight: FontWeight.bold,
                                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                                ),
                              ),
                            ],
                          ),
                          Text(
                            currencyCode,
                            style: TextStyle(
                              fontSize: 14,
                              color: Theme.of(context).colorScheme.onSurfaceVariant.withValues(alpha: 126),
                            ),
                          ),
                        ],
                      ),
                    );
                  },
                ),
                const SizedBox(height: 20),
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: () => Navigator.of(context).pop(),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Theme.of(context).colorScheme.primary,
                      foregroundColor: Theme.of(context).colorScheme.onPrimary,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: const Text(
                      'Close',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildPaymentMethodList() {
    // Sort payment methods by income (highest first)
    final sortedMethods = _incomeByPaymentMethod.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));
    
    return sortedMethods.isEmpty
        ? Center(child: Text('No payment type data available', style: TextStyle(color: Colors.grey[600])))
        : ListView.builder(
            padding: const EdgeInsets.all(16.0),
            itemCount: sortedMethods.length,
            itemBuilder: (context, index) {
              final entry = sortedMethods[index];
              final method = entry.key;
              final amount = entry.value;
              
              // Calculate percentage of total income
              final totalIncome = _filteredPayments.fold<double>(
                0, (sum, payment) => sum + payment.amount);
              final percentage = totalIncome > 0 
                  ? (amount / totalIncome * 100).toStringAsFixed(1) 
                  : '0.0';
              
              return Card(
                margin: const EdgeInsets.only(bottom: 12.0),
                elevation: 0,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                  side: BorderSide(color: Colors.grey.withValues(alpha: 26)),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(12.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(
                            child: Row(
                              children: [
                                Icon(
                                  _getPaymentMethodIcon(method),
                                  size: 24,
                                  color: Theme.of(context).colorScheme.primary,
                                ),
                                const SizedBox(width: 12),
                                Text(
                                  _getPaymentMethodName(method),
                                  style: const TextStyle(
                                    fontWeight: FontWeight.bold,
                                    fontSize: 16,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          GestureDetector(
                            onTap: () => _showFullAmountDialog(amount, 'Income from ${_getPaymentMethodName(method)}'),
                            child: Text(
                              CurrencyFormatter.formatCompactAmount(amount),
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              color: Colors.green,
                              fontSize: 16,
                                decoration: TextDecoration.underline,
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          Expanded(
                            child: ClipRRect(
                              borderRadius: BorderRadius.circular(4),
                              child: LinearProgressIndicator(
                                value: totalIncome > 0 ? amount / totalIncome : 0,
                                backgroundColor: Colors.grey.shade200,
                                color: Theme.of(context).colorScheme.primary,
                                minHeight: 8,
                              ),
                            ),
                          ),
                          const SizedBox(width: 8),
                          Text(
                            '$percentage%',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey.shade600,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              );
            },
          );
  }

  Widget _buildPaymentStatusList() {
    // Group payments by status
    final paymentsByStatus = <PaymentStatus, List<Payment>>{};
    final amountByStatus = <PaymentStatus, double>{};

    for (final payment in _filteredPayments) {
      paymentsByStatus[payment.status] = [...(paymentsByStatus[payment.status] ?? []), payment];
      amountByStatus[payment.status] = (amountByStatus[payment.status] ?? 0) + payment.amount;
    }

    // Sort by amount (highest first)
    final sortedStatuses = amountByStatus.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));

    return sortedStatuses.isEmpty
        ? Center(child: Text('No payment status data available', style: TextStyle(color: Colors.grey[600])))
        : ListView.builder(
            padding: const EdgeInsets.all(16.0),
            itemCount: sortedStatuses.length,
            itemBuilder: (context, index) {
              final entry = sortedStatuses[index];
              final status = entry.key;
              final amount = entry.value;
              final count = paymentsByStatus[status]!.length;

              // Calculate percentage of total payments
              final totalAmount = _filteredPayments.fold<double>(
                0, (sum, payment) => sum + payment.amount);
              final percentage = totalAmount > 0
                  ? (amount / totalAmount * 100).toStringAsFixed(1)
                  : '0.0';

              return Card(
                margin: const EdgeInsets.only(bottom: 12.0),
                elevation: 0,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                  side: BorderSide(color: Colors.grey.withAlpha(26)),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(12.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(
                            child: Row(
                              children: [
                                Icon(
                                  _getPaymentStatusIcon(status),
                                  size: 24,
                                  color: _getPaymentStatusColor(status),
                                ),
                                const SizedBox(width: 12),
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      _getPaymentStatusName(status),
                                      style: const TextStyle(
                                        fontWeight: FontWeight.bold,
                                        fontSize: 16,
                                      ),
                                    ),
                                    Text(
                                      '$count payments',
                                      style: TextStyle(
                                        fontSize: 12,
                                        color: Colors.grey[600],
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                          GestureDetector(
                            onTap: () => _showFullAmountDialog(amount, '${_getPaymentStatusName(status)} Payments'),
                            child: Text(
                              CurrencyFormatter.formatCompactAmount(amount),
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                color: _getPaymentStatusColor(status),
                                fontSize: 16,
                                decoration: TextDecoration.underline,
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          Expanded(
                            child: ClipRRect(
                              borderRadius: BorderRadius.circular(4),
                              child: LinearProgressIndicator(
                                value: totalAmount > 0 ? amount / totalAmount : 0,
                                backgroundColor: Colors.grey.shade200,
                                color: _getPaymentStatusColor(status),
                                minHeight: 8,
                              ),
                            ),
                          ),
                          const SizedBox(width: 8),
                          Text(
                            '$percentage%',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey.shade600,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              );
            },
          );
  }

  IconData _getPaymentMethodIcon(PaymentMethod method) {
    switch (method) {
      case PaymentMethod.cash:
        return Icons.money;
      case PaymentMethod.bankTransfer:
        return Icons.account_balance;
      case PaymentMethod.mobileMoney:
        return Icons.phone_android;
      case PaymentMethod.check:
        return Icons.receipt_long;
      case PaymentMethod.other:
        return Icons.credit_card; // Treat 'other' as card payments for now
    }
  }

  String _getPaymentMethodName(PaymentMethod method) {
    switch (method) {
      case PaymentMethod.cash:
        return 'Cash';
      case PaymentMethod.bankTransfer:
        return 'Bank Transfer';
      case PaymentMethod.mobileMoney:
        return 'Mobile Money';
      case PaymentMethod.check:
        return 'Check';
      case PaymentMethod.other:
        return 'Card/Other'; // Include card payments in other category
    }
  }

  IconData _getPaymentStatusIcon(PaymentStatus status) {
    switch (status) {
      case PaymentStatus.verified:
        return Icons.check_circle;
      case PaymentStatus.pending:
        return Icons.pending;
      case PaymentStatus.rejected:
        return Icons.cancel;
    }
  }

  Color _getPaymentStatusColor(PaymentStatus status) {
    switch (status) {
      case PaymentStatus.verified:
        return Colors.green;
      case PaymentStatus.pending:
        return Colors.orange;
      case PaymentStatus.rejected:
        return Colors.red;
    }
  }

  String _getPaymentStatusName(PaymentStatus status) {
    switch (status) {
      case PaymentStatus.verified:
        return 'Verified';
      case PaymentStatus.pending:
        return 'Pending';
      case PaymentStatus.rejected:
        return 'Rejected';
    }
  }

  IconData _getViewIcon() {
    switch (_selectedView) {
      case 'table':
        return Icons.table_chart;
      case 'property':
        return Icons.business;
      case 'payment_method':
        return Icons.payment;
      case 'payment_status':
        return Icons.verified;
      case 'income_type':
        return Icons.category;
      default:
        return Icons.table_chart;
    }
  }

  String _getViewName(String viewKey) {
    switch (viewKey) {
      case 'table':
        return 'Monthly';
      case 'property':
        return 'Property';
      case 'payment_method':
        return 'Payment Method';
      case 'payment_status':
        return 'Payment Status';
      case 'income_type':
        return 'Income Type';
      default:
        return 'Selected';
    }
  }
} 