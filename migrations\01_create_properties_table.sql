-- Drop existing tables if they exist (in reverse order of dependencies)
DROP TABLE IF EXISTS public.utility_bills CASCADE;
DROP TABLE IF EXISTS public.properties CASCADE;

-- Create properties table with comprehensive schema including payment details
CREATE TABLE public.properties (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL,
    address TEXT NOT NULL,
    city TEXT NOT NULL,
    state TEXT NOT NULL,
    zip_code TEXT NOT NULL,
    description TEXT,
    image_url TEXT,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    payment_method TEXT DEFAULT 'cash' CHECK (payment_method = ANY (ARRAY['cash'::text, 'mobilePayment'::text, 'bank'::text])),
    mobile_payment_type TEXT CHECK (mobile_payment_type = ANY (ARRAY['Till'::text, 'Paybill'::text, 'Poshi La Biashara'::text, 'Mobile Number'::text])),
    paybill_number TEXT,
    till_number TEXT,
    mobile_number TEXT,
    bank_name TEXT,
    account_number TEXT,
    payment_notes TEXT,
    payment_details JSONB DEFAULT '[]'::jsonb CHECK (jsonb_typeof(payment_details) = 'array' OR payment_details IS NULL),
    payment_options_list JSONB DEFAULT '[]'::jsonb CHECK (jsonb_typeof(payment_options_list) = 'array' OR payment_options_list IS NULL),
    additional_info JSONB,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Add comment for properties table
COMMENT ON TABLE properties IS 'Table contains property information - RLS policies optimized for performance';
COMMENT ON COLUMN properties.payment_options_list IS 'JSON array of payment options for the property';

-- Create utility_bills table (related to properties)
CREATE TABLE public.utility_bills (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    property_id UUID NOT NULL REFERENCES public.properties(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    rate DECIMAL(10, 2) NOT NULL,
    unit TEXT,
    notes TEXT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Add indexes
CREATE INDEX idx_properties_user_id ON public.properties(user_id);
CREATE INDEX idx_utility_bills_property_id ON public.utility_bills(property_id);

-- Enable Row-Level Security
ALTER TABLE public.properties ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.utility_bills ENABLE ROW LEVEL SECURITY;

-- Create optimized RLS policies for properties (wrapped auth functions for better performance)
-- Users can view only their own properties
CREATE POLICY "Users can view their own properties" 
    ON public.properties FOR SELECT TO public
    USING (user_id = (SELECT auth.uid()));

-- Users can insert their own properties
CREATE POLICY "Users can insert their own properties" 
    ON public.properties FOR INSERT TO public
    WITH CHECK (user_id = (SELECT auth.uid()));

-- Users can update their own properties
CREATE POLICY "Users can update their own properties" 
    ON public.properties FOR UPDATE TO public
    USING (user_id = (SELECT auth.uid()));

-- Users can delete their own properties
CREATE POLICY "Users can delete their own properties" 
    ON public.properties FOR DELETE TO public
    USING (user_id = (SELECT auth.uid()));

-- Create optimized RLS policies for utility_bills
-- Users can view utility bills for their own properties
CREATE POLICY "Users can view utility bills for their own properties" 
    ON public.utility_bills FOR SELECT TO public
    USING (EXISTS (
        SELECT 1 FROM public.properties 
        WHERE properties.id = utility_bills.property_id 
        AND properties.user_id = (SELECT auth.uid())
    ));

-- Users can insert utility bills for their own properties
CREATE POLICY "Users can insert utility bills for their own properties" 
    ON public.utility_bills FOR INSERT TO public
    WITH CHECK (EXISTS (
        SELECT 1 FROM public.properties 
        WHERE properties.id = utility_bills.property_id 
        AND properties.user_id = (SELECT auth.uid())
    ));

-- Users can update utility bills for their own properties
CREATE POLICY "Users can update utility bills for their own properties" 
    ON public.utility_bills FOR UPDATE TO public
    USING (EXISTS (
        SELECT 1 FROM public.properties 
        WHERE properties.id = utility_bills.property_id 
        AND properties.user_id = (SELECT auth.uid())
    ));

-- Users can delete utility bills for their own properties
CREATE POLICY "Users can delete utility bills for their own properties" 
    ON public.utility_bills FOR DELETE TO public
    USING (EXISTS (
        SELECT 1 FROM public.properties 
        WHERE properties.id = utility_bills.property_id 
        AND properties.user_id = (SELECT auth.uid())
    ));

-- Create function to automatically update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers to automatically update the updated_at timestamp
CREATE TRIGGER update_properties_updated_at
BEFORE UPDATE ON public.properties
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_utility_bills_updated_at
BEFORE UPDATE ON public.utility_bills
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- Create a secure view that properly joins properties with utility bills
-- This view uses explicit SECURITY INVOKER to respect user permissions and RLS policies
CREATE VIEW properties_with_utility_bills 
WITH (security_invoker = true) AS
SELECT 
    p.id,
    p.name,
    p.address,
    p.city,
    p.state,
    p.zip_code,
    p.description,
    p.image_url,
    p.user_id,
    p.payment_method,
    p.mobile_payment_type,
    p.paybill_number,
    p.till_number,
    p.mobile_number,
    p.bank_name,
    p.account_number,
    p.payment_notes,
    p.additional_info,
    p.created_at,
    p.updated_at,
    p.payment_details,
    p.payment_options_list,
    COALESCE(
        jsonb_agg(
            CASE 
                WHEN ub.id IS NOT NULL THEN 
                    jsonb_build_object(
                        'id', ub.id,
                        'name', ub.name,
                        'rate', ub.rate,
                        'unit', ub.unit,
                        'notes', ub.notes,
                        'created_at', ub.created_at,
                        'updated_at', ub.updated_at
                    )
                ELSE NULL
            END
        ) FILTER (WHERE ub.id IS NOT NULL),
        '[]'::jsonb
    ) AS utility_bills
FROM properties p
LEFT JOIN utility_bills ub ON p.id = ub.property_id
GROUP BY 
    p.id, p.name, p.address, p.city, p.state, p.zip_code, 
    p.description, p.image_url, p.user_id, p.payment_method, 
    p.mobile_payment_type, p.paybill_number, p.till_number, 
    p.mobile_number, p.bank_name, p.account_number, p.payment_notes, 
    p.additional_info, p.created_at, p.updated_at, p.payment_details, 
    p.payment_options_list;

-- Ensure the view respects RLS policies from the underlying tables with security barrier
ALTER VIEW properties_with_utility_bills SET (security_barrier = true);

-- Grant appropriate permissions
GRANT SELECT ON properties_with_utility_bills TO authenticated;

-- Create secure functions with SECURITY INVOKER and fixed search_path to address security warnings
CREATE OR REPLACE FUNCTION get_property_by_id(property_id uuid)
RETURNS TABLE(
    id uuid,
    name text,
    address text,
    city text,
    state text,
    zip_code text,
    description text,
    image_url text,
    user_id uuid,
    payment_method text,
    mobile_payment_type text,
    paybill_number text,
    till_number text,
    mobile_number text,
    bank_name text,
    account_number text,
    payment_notes text,
    additional_info jsonb,
    created_at timestamptz,
    updated_at timestamptz,
    payment_details jsonb,
    payment_options_list jsonb,
    utility_bills jsonb
)
LANGUAGE plpgsql
SECURITY INVOKER
SET search_path = public, pg_temp
AS $$
BEGIN
    RETURN QUERY
    SELECT * FROM properties_with_utility_bills 
    WHERE properties_with_utility_bills.id = property_id
    AND properties_with_utility_bills.user_id = auth.uid();
END;
$$;

CREATE OR REPLACE FUNCTION get_user_properties()
RETURNS TABLE(
    id uuid,
    name text,
    address text,
    city text,
    state text,
    zip_code text,
    description text,
    image_url text,
    user_id uuid,
    payment_method text,
    mobile_payment_type text,
    paybill_number text,
    till_number text,
    mobile_number text,
    bank_name text,
    account_number text,
    payment_notes text,
    additional_info jsonb,
    created_at timestamptz,
    updated_at timestamptz,
    payment_details jsonb,
    payment_options_list jsonb,
    utility_bills jsonb
)
LANGUAGE plpgsql
SECURITY INVOKER
SET search_path = public, pg_temp
AS $$
BEGIN
    RETURN QUERY
    SELECT * FROM properties_with_utility_bills 
    WHERE properties_with_utility_bills.user_id = auth.uid()
    ORDER BY properties_with_utility_bills.created_at DESC;
END;
$$;

-- Grant execute permissions on functions
GRANT EXECUTE ON FUNCTION get_property_by_id(uuid) TO authenticated;
GRANT EXECUTE ON FUNCTION get_user_properties() TO authenticated;

-- Add helpful comments for documentation
COMMENT ON VIEW properties_with_utility_bills IS 'Secure view that combines properties with their utility bills, uses SECURITY INVOKER to respect user permissions and RLS policies';
COMMENT ON FUNCTION get_property_by_id(uuid) IS 'Secure function to get a single property with utility bills by ID, uses SECURITY INVOKER with fixed search_path';
COMMENT ON FUNCTION get_user_properties() IS 'Secure function to get all properties for the current user with utility bills, uses SECURITY INVOKER with fixed search_path';

-- CONSOLIDATED IMPROVEMENTS FROM MIGRATIONS 20, 21, 22:
-- 1. Security Advisory Fixes Applied:
--    - security_definer_view: Changed view from SECURITY DEFINER to explicit SECURITY INVOKER
--    - function_search_path_mutable: Changed functions from SECURITY DEFINER to SECURITY INVOKER with fixed search_path 'public, pg_temp'
-- 2. Property Model Compatibility:
--    - payment_details and payment_options_list are properly structured as JSONB arrays
--    - Proper constraints ensure data integrity
-- 3. RLS Performance Optimizations:
--    - All RLS policies use wrapped auth functions for better performance
--    - Essential indexes added for optimal query performance
--
-- Note: Auth-related warnings (leaked password protection and MFA options) need to be configured 
-- in the Supabase dashboard as they are Auth service configuration settings, not database schema issues. 