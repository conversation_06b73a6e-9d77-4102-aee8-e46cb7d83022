import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../models/bill/bill.dart';
import '../../models/bill/bill_template.dart';
import '../../models/property/property_model.dart';
import '../../services/service_locator.dart';
import '../../utils/form_validators.dart';
import '../../utils/logger.dart';
import '../../widgets/loading_indicator.dart';

class TemplateFormPage extends StatefulWidget {
  final BillTemplate? template;
  
  const TemplateFormPage({
    super.key,
    this.template,
  });

  @override
  State<TemplateFormPage> createState() => _TemplateFormPageState();
}

class _TemplateFormPageState extends State<TemplateFormPage> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _amountController = TextEditingController();
  final _notesController = TextEditingController();
  final _ratePerUnitController = TextEditingController();
  final _dayOfMonthController = TextEditingController();
  final _daysBeforeDueController = TextEditingController();
  
  BillType _type = BillType.rent;
  RecurrenceType _recurrence = RecurrenceType.monthly;
  UtilityType? _utilityType;
  bool _includeInRent = false;
  bool _applyToAllTenants = false;
  bool _autoGenerate = false;
  
  String? _selectedPropertyId;
  List<Property> _properties = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    
    // Set default values
    _dayOfMonthController.text = '1';
    _daysBeforeDueController.text = '7';
    _amountController.text = '0.00';
    
    _loadData();
    
    // If editing an existing template, populate the form
    if (widget.template != null) {
      final template = widget.template!;
      _nameController.text = template.name;
      _descriptionController.text = template.description;
      _amountController.text = template.amount.toString();
      _notesController.text = template.notes ?? '';
      _ratePerUnitController.text = template.ratePerUnit?.toString() ?? '';
      _dayOfMonthController.text = template.dueDayOfMonth.toString();
      _daysBeforeDueController.text = template.daysBeforeDue.toString();
      
      _type = template.type;
      _recurrence = template.recurrence;
      _utilityType = template.utilityType;
      _includeInRent = template.includeInRent;
      _applyToAllTenants = template.applyToAllTenants;
      _autoGenerate = template.autoGenerate;
      _selectedPropertyId = template.propertyId;
    }
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });
    
    try {
      // Load properties
      _properties = await serviceLocator.propertyService.getAllProperties();
      
      if (!mounted) return;
      
      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      AppLogger.error('Error loading data: $e');
      if (!mounted) return;
      
      setState(() {
        _isLoading = false;
      });
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error loading data: $e')),
      );
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _amountController.dispose();
    _notesController.dispose();
    _ratePerUnitController.dispose();
    _dayOfMonthController.dispose();
    _daysBeforeDueController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.template != null ? 'Edit Template' : 'Create Template'),
        actions: [
          if (widget.template != null) // Only show for existing templates
            IconButton(
              icon: const Icon(Icons.play_arrow),
              tooltip: 'Generate Bills Now',
              onPressed: _showGenerateBillDialog,
            ),
        ],
      ),
      body: _isLoading
          ? const Center(child: LoadingIndicator())
          : Form(
              key: _formKey,
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildBasicDetailsSection(),
                    const SizedBox(height: 24),
                    _buildBillingDetailsSection(),
                    const SizedBox(height: 24),
                    _buildAutomationSection(),
                    const SizedBox(height: 32),
                    _buildSubmitButton(),
                  ],
                ),
              ),
            ),
    );
  }

  Widget _buildBasicDetailsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Template Details',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        TextFormField(
          controller: _nameController,
          decoration: const InputDecoration(
            labelText: 'Template Name',
            hintText: 'e.g., Monthly Rent, Water Bill',
            border: OutlineInputBorder(),
          ),
          validator: (value) {
            // Use comprehensive validation
            final requiredError = FormValidators.required(value);
            if (requiredError != null) return requiredError;
            
            final minLengthError = FormValidators.minLength(value, 3);
            if (minLengthError != null) return minLengthError;
            
            // Additional custom validation for template names
            if (value!.trim().length > 50) {
              return 'Template name must be 50 characters or less';
            }
            
            // Check for valid characters (letters, numbers, spaces, hyphens, underscores)
            if (!RegExp(r'^[a-zA-Z0-9\s\-_]+$').hasMatch(value.trim())) {
              return 'Template name can only contain letters, numbers, spaces, hyphens, and underscores';
            }
            
            return null;
          },
        ),
        const SizedBox(height: 16),
        TextFormField(
          controller: _descriptionController,
          decoration: const InputDecoration(
            labelText: 'Description',
            hintText: 'Describe the purpose of this bill template',
            border: OutlineInputBorder(),
          ),
          maxLines: 2,
          validator: (value) {
            // Use comprehensive validation
            final requiredError = FormValidators.required(value);
            if (requiredError != null) return requiredError;
            
            final minLengthError = FormValidators.minLength(value, 10);
            if (minLengthError != null) return minLengthError;
            
            // Additional custom validation for descriptions
            if (value!.trim().length > 200) {
              return 'Description must be 200 characters or less';
            }
            
            // Check for meaningful content (not just spaces or repeated characters)
            final trimmedValue = value.trim();
            if (RegExp(r'^(.)\1{4,}$').hasMatch(trimmedValue)) {
              return 'Please provide a meaningful description';
            }
            
            return null;
          },
        ),
        const SizedBox(height: 16),
        DropdownButtonFormField<BillType>(
          value: _type,
          decoration: const InputDecoration(
            labelText: 'Bill Type',
            border: OutlineInputBorder(),
          ),
          items: BillType.values.where((type) => type != BillType.utility).map((type) {
            return DropdownMenuItem(
              value: type,
              child: Text(_getBillTypeDisplayName(type)),
            );
          }).toList(),
          onChanged: (value) {
            setState(() {
              _type = value!;
              // Reset utility type if not a utility bill
              if (_type != BillType.utility) {
                _utilityType = null;
              } else {
                _utilityType ??= UtilityType.water;
              }
            });
          },
        ),
        if (_type == BillType.utility) ...[
          const SizedBox(height: 16),
          DropdownButtonFormField<UtilityType>(
            value: _utilityType ?? UtilityType.water,
            decoration: const InputDecoration(
              labelText: 'Utility Type',
              border: OutlineInputBorder(),
            ),
            items: UtilityType.values.map((type) {
              return DropdownMenuItem(
                value: type,
                child: Text(_getUtilityTypeDisplayName(type)),
              );
            }).toList(),
            onChanged: (value) {
              setState(() {
                _utilityType = value;
              });
            },
          ),
          const SizedBox(height: 16),
          _buildUtilityRateField(),
          const SizedBox(height: 16),
          SwitchListTile(
            title: const Text('Include in Rent'),
            subtitle: const Text('Include this utility in the rent payment'),
            value: _includeInRent,
            onChanged: (value) {
              setState(() {
                _includeInRent = value;
              });
            },
          ),
        ],
      ],
    );
  }

  Widget _buildBillingDetailsSection() {
    final currencySymbol = serviceLocator.settingsService.selectedCurrency.symbol;
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Billing Details',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        TextFormField(
          controller: _amountController,
          decoration: InputDecoration(
            labelText: _type == BillType.rent ? 'Default Amount (Optional)' : 'Amount',
            border: const OutlineInputBorder(),
            prefixText: '${currencySymbol} ',
            helperText: _type == BillType.rent 
                ? 'For rent bills, each tenant\'s room rental price will be used automatically' 
                : 'Fixed amount for this bill type',
          ),
          keyboardType: const TextInputType.numberWithOptions(decimal: true),
          inputFormatters: [
            FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d{0,2}')),
          ],
          validator: _type == BillType.rent 
              ? (value) {
                  // For rent bills, amount is optional but if provided, must be valid
                  if (value != null && value.trim().isNotEmpty) {
                    final amount = double.tryParse(value.trim());
                    if (amount == null) {
                      return 'Please enter a valid amount';
                    }
                    if (amount < 0) {
                      return 'Amount cannot be negative';
                    }
                    if (amount > 999999999) {
                      return 'Amount is too large';
                    }
                  }
                  return null;
                }
              : (value) {
                  // For non-rent bills, amount is required and must be positive
                  final requiredError = FormValidators.required(value);
                  if (requiredError != null) return requiredError;
                  
                  final nonZeroError = FormValidators.nonZeroAmount(value);
                  if (nonZeroError != null) return nonZeroError;
                  
                  final amount = double.tryParse(value!.trim());
                  if (amount != null && amount > 999999999) {
                    return 'Amount is too large';
                  }
                  
                  return null;
                },
          enabled: _type != BillType.rent,
        ),
        const SizedBox(height: 16),
        DropdownButtonFormField<RecurrenceType>(
          value: _recurrence,
          decoration: const InputDecoration(
            labelText: 'Recurrence',
            border: OutlineInputBorder(),
          ),
          items: RecurrenceType.values.map((type) {
            return DropdownMenuItem(
              value: type,
              child: Text(_getRecurrenceDisplayName(type)),
            );
          }).toList(),
          onChanged: (value) {
            setState(() {
              _recurrence = value!;
            });
          },
        ),
        const SizedBox(height: 16),
        TextFormField(
          controller: _dayOfMonthController,
          decoration: const InputDecoration(
            labelText: 'Due Day of Month',
            hintText: 'Day between 1-31',
            border: OutlineInputBorder(),
          ),
          keyboardType: TextInputType.number,
          inputFormatters: [
            FilteringTextInputFormatter.digitsOnly,
          ],
          validator: (value) {
            // Use comprehensive validation for due day
            final requiredError = FormValidators.required(value);
            if (requiredError != null) return requiredError;
            
            final day = int.tryParse(value!.trim());
            if (day == null) {
              return 'Please enter a valid number';
            }
            
            if (day < 1 || day > 31) {
              return 'Day must be between 1 and 31';
            }
            
            // Additional validation for specific months
            final now = DateTime.now();
            try {
              // Test if the day is valid for the current month
              DateTime(now.year, now.month, day);
            } catch (e) {
              return 'Day $day is not valid for some months';
            }
            
            return null;
          },
        ),
        const SizedBox(height: 16),
        TextFormField(
          controller: _notesController,
          decoration: const InputDecoration(
            labelText: 'Notes (Optional)',
            border: OutlineInputBorder(),
          ),
          maxLines: 3,
        ),
      ],
    );
  }

  Widget _buildAutomationSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Automation Settings',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        DropdownButtonFormField<String>(
          value: _selectedPropertyId,
          decoration: const InputDecoration(
            labelText: 'Property (Optional)',
            border: OutlineInputBorder(),
          ),
          items: [
            const DropdownMenuItem(
              value: null,
              child: Text('No specific property'),
            ),
            ..._properties.map((property) {
              return DropdownMenuItem(
                value: property.id,
                child: Text(property.name),
              );
            }),
          ],
          onChanged: (value) {
            setState(() {
              _selectedPropertyId = value;
            });
          },
        ),
        const SizedBox(height: 16),
        SwitchListTile(
          title: const Text('Apply to All Tenants'),
          subtitle: const Text('Apply this template to all tenants in the selected property'),
          value: _applyToAllTenants,
          onChanged: _selectedPropertyId == null
              ? null
              : (value) {
                  setState(() {
                    _applyToAllTenants = value;
                  });
                },
        ),
        const SizedBox(height: 8),
        SwitchListTile(
          title: const Text('Auto-Generate Bills'),
          subtitle: const Text('Automatically generate bills based on this template'),
          value: _autoGenerate,
          onChanged: (value) {
            setState(() {
              _autoGenerate = value;
            });
          },
        ),
        if (_autoGenerate) ...[
          const SizedBox(height: 16),
          TextFormField(
            controller: _daysBeforeDueController,
            decoration: const InputDecoration(
              labelText: 'Days Before Due Date',
              hintText: 'Generate bill this many days before due date',
              border: OutlineInputBorder(),
            ),
            keyboardType: TextInputType.number,
            inputFormatters: [
              FilteringTextInputFormatter.digitsOnly,
            ],
            validator: (value) {
              // Use comprehensive validation for days before due
              final requiredError = FormValidators.required(value);
              if (requiredError != null) return requiredError;
              
              final days = int.tryParse(value!.trim());
              if (days == null) {
                return 'Please enter a valid number';
              }
              
              if (days < 0) {
                return 'Days cannot be negative';
              }
              
              if (days > 90) {
                return 'Days before due cannot exceed 90 days';
              }
              
              // Warning for unusual values
              if (days > 30) {
                // This is just a validation, not a warning, so we allow it but with a note
                // The user might want to generate bills very early
              }
              
              return null;
            },
          ),
        ],
      ],
    );
  }

  Widget _buildSubmitButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: _saveTemplate,
        style: ElevatedButton.styleFrom(
          padding: const EdgeInsets.symmetric(vertical: 16),
        ),
        child: Text(widget.template != null ? 'Update Template' : 'Create Template'),
      ),
    );
  }

  String _getBillTypeDisplayName(BillType type) {
    switch (type) {
      case BillType.rent:
        return 'Rent';
      case BillType.utility:
        return 'Utility';
      case BillType.maintenance:
        return 'Maintenance';
      case BillType.service:
        return 'Service';
      case BillType.other:
        return 'Other';
    }
  }

  String _getUtilityTypeDisplayName(UtilityType type) {
    switch (type) {
      case UtilityType.water:
        return 'Water';
      case UtilityType.electricity:
        return 'Electricity';
      case UtilityType.gas:
        return 'Gas';
      case UtilityType.other:
        return 'Other Utility';
    }
  }

  String _getRecurrenceDisplayName(RecurrenceType type) {
    switch (type) {
      case RecurrenceType.oneTime:
        return 'One-time';
      case RecurrenceType.monthly:
        return 'Monthly';
      case RecurrenceType.quarterly:
        return 'Quarterly';
      case RecurrenceType.yearly:
        return 'Yearly';
    }
  }

  Future<void> _saveTemplate() async {
    if (!_formKey.currentState!.validate()) {
      // Show specific validation error message
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please fix the errors in the form before saving'),
          backgroundColor: Colors.orange,
          duration: Duration(seconds: 3),
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final name = _nameController.text.trim();
      final description = _descriptionController.text.trim();
      
      // For rent bills, amount can be 0 (will use room rental prices)
      double amount = 0.0;
      if (_type != BillType.rent) {
        amount = double.parse(_amountController.text.trim());
      } else {
        // For rent bills, use amount from form or default to 0
        amount = double.tryParse(_amountController.text.trim()) ?? 0.0;
      }
      
      final dueDay = int.parse(_dayOfMonthController.text.trim());
      final daysBeforeDue = _daysBeforeDueController.text.trim().isNotEmpty 
          ? int.parse(_daysBeforeDueController.text.trim()) 
          : 7;
      
      // Parse rate per unit if provided
      double? ratePerUnit;
      if (_ratePerUnitController.text.trim().isNotEmpty) {
        ratePerUnit = double.parse(_ratePerUnitController.text.trim());
      }
      
      // Additional validation
      if (_applyToAllTenants && _selectedPropertyId == null) {
        throw Exception('Property must be selected when applying to all tenants');
      }
      
      // Only validate amount > 0 for non-rent bills
      if (_type != BillType.rent && amount <= 0) {
        throw Exception('Amount must be greater than 0');
      }
      
      final template = BillTemplate(
        id: widget.template?.id,
        name: name,
        description: description,
        type: _type,
        recurrence: _recurrence,
        amount: amount,
        dueDayOfMonth: dueDay,
        daysBeforeDue: daysBeforeDue,
        propertyId: _selectedPropertyId,
        applyToAllTenants: _applyToAllTenants && _selectedPropertyId != null,
        autoGenerate: _autoGenerate,
        notes: _notesController.text.trim().isNotEmpty ? _notesController.text.trim() : null,
        utilityType: _type == BillType.utility ? _utilityType : null,
        ratePerUnit: _type == BillType.utility ? ratePerUnit : null,
        includeInRent: _type == BillType.utility ? _includeInRent : false,
        createdAt: widget.template?.createdAt,
        updatedAt: DateTime.now(),
      );
      
      AppLogger.info('Saving template: ${template.name}');
      
      if (widget.template == null) {
        await serviceLocator.billTemplateService.createTemplate(template);
        AppLogger.info('Template created successfully');
      } else {
        await serviceLocator.billTemplateService.updateTemplate(template);
        AppLogger.info('Template updated successfully');
      }
      
      if (!mounted) return;
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'Template ${widget.template == null ? 'created' : 'updated'} successfully!'),
          backgroundColor: Colors.green,
          duration: const Duration(seconds: 3),
        ),
      );
      Navigator.of(context).pop(true);
      
    } catch (e) {
      AppLogger.error('Error saving template: $e');
      if (!mounted) return;
      
      setState(() {
        _isLoading = false;
      });
      
      // Show user-friendly error message
      String errorMessage = 'Failed to save template';
      if (e.toString().contains('Property must be selected')) {
        errorMessage = 'Please select a property when applying to all tenants';
      } else if (e.toString().contains('Amount must be greater')) {
        errorMessage = 'Amount must be greater than 0';
      } else if (e.toString().contains('already exists')) {
        errorMessage = 'A template with this name already exists';
      } else if (e.toString().contains('network')) {
        errorMessage = 'Network error. Please check your connection and try again';
      } else {
        errorMessage = 'Error: ${e.toString()}';
      }
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(errorMessage),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 5),
          action: SnackBarAction(
            label: 'Retry',
            textColor: Colors.white,
            onPressed: _saveTemplate,
          ),
        ),
      );
    }
  }

  /// Show confirmation dialog before generating bills
  void _showGenerateBillDialog() {
    if (widget.template == null) return;
    
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Row(
            children: [
              Icon(Icons.play_arrow, color: Colors.orange),
              SizedBox(width: 8),
              Text('Generate Bills'),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Generate bills now from template "${widget.template!.name}"?',
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 16),
              const Text('This will create bills for:'),
              const SizedBox(height: 8),
              if (widget.template!.applyToAllTenants && widget.template!.propertyId != null)
                const Text('• All tenants in the selected property')
              else
                const Text('• Eligible tenants based on template settings'),
              const SizedBox(height: 8),
              Text('• Due date: ${_getDueDateText()}'),
              if (widget.template!.type == BillType.rent)
                const Text('• Amount: Each tenant\'s room rental price (dynamic)')
              else
                Text('• Amount: ${serviceLocator.settingsService.formatCurrency(widget.template!.amount)}'),
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.orange.withAlpha(26),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.orange),
                ),
                child: const Row(
                  children: [
                    Icon(Icons.warning, color: Colors.orange, size: 20),
                    SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'This action cannot be undone. Bills will be created immediately.',
                        style: TextStyle(fontSize: 13),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            ElevatedButton.icon(
              onPressed: () {
                Navigator.of(context).pop();
                _generateBillsFromTemplate();
              },
              icon: const Icon(Icons.play_arrow),
              label: const Text('Generate'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange,
                foregroundColor: Colors.white,
              ),
            ),
          ],
        );
      },
    );
  }

  /// Generate bills from the current template
  Future<void> _generateBillsFromTemplate() async {
    if (widget.template == null) return;
    
    setState(() {
      _isLoading = true;
    });
    
    try {
      AppLogger.info('Generating bills from template: ${widget.template!.name}');
      
      // Use the new bill generation service method
      final generatedBills = await serviceLocator.billTemplateService.generateBillsFromTemplateNow(
        template: widget.template!,
      );
      
      if (!mounted) return;
      
      final billCount = generatedBills.length;
      
      if (billCount > 0) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Successfully generated $billCount ${billCount == 1 ? 'bill' : 'bills'}!'),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 4),
          ),
        );
        AppLogger.info('Generated $billCount bills successfully from template "${widget.template!.name}"');
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('No bills were generated. Either no eligible tenants found or bills already exist for this period.'),
            backgroundColor: Colors.orange,
            duration: Duration(seconds: 4),
          ),
        );
        AppLogger.info('No bills generated from template "${widget.template!.name}" - no eligible tenants or bills already exist');
      }
      
    } catch (e) {
      AppLogger.error('Error generating bills from template: $e');
      if (!mounted) return;
      
      String errorMessage = 'Failed to generate bills';
      if (e.toString().contains('no eligible tenants')) {
        errorMessage = 'No eligible tenants found for bill generation';
      } else if (e.toString().contains('already exist')) {
        errorMessage = 'Bills already exist for this period';
      } else if (e.toString().contains('network')) {
        errorMessage = 'Network error. Please check your connection and try again';
      } else {
        errorMessage = 'Error: ${e.toString()}';
      }
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(errorMessage),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 5),
          action: SnackBarAction(
            label: 'Retry',
            textColor: Colors.white,
            onPressed: _generateBillsFromTemplate,
          ),
        ),
      );
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// Build utility rate field with property-specific handling
  Widget _buildUtilityRateField() {
    final selectedProperty = _selectedPropertyId != null 
        ? _properties.firstWhere(
            (p) => p.id == _selectedPropertyId, 
            orElse: () => _properties.first
          )
        : null;
    
    // Get matching utility rate from property if available
    UtilityBill? matchingUtilityBill;
    if (selectedProperty != null && _utilityType != null) {
      matchingUtilityBill = selectedProperty.utilityBills.cast<UtilityBill?>().firstWhere(
        (bill) => bill?.name.toLowerCase().contains(_getUtilityTypeDisplayName(_utilityType!).toLowerCase()) == true,
        orElse: () => null,
      );
    }
    
    final hasPropertyRate = matchingUtilityBill != null;
    final currencySymbol = serviceLocator.settingsService.selectedCurrency.symbol;
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextFormField(
          controller: _ratePerUnitController,
          decoration: InputDecoration(
            labelText: 'Rate per Unit (Optional)',
            border: const OutlineInputBorder(),
            prefixText: '${currencySymbol} ',
            helperText: hasPropertyRate 
                ? 'Property rate: ${matchingUtilityBill.formattedRate} will be used when generating bills'
                : 'Cost per unit for utility calculations',
            helperMaxLines: 2,
          ),
          keyboardType: const TextInputType.numberWithOptions(decimal: true),
          inputFormatters: [
            FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d{0,2}')),
          ],
          enabled: !hasPropertyRate,
          validator: hasPropertyRate 
              ? null 
              : (value) {
                  // Utility rate is optional, but if provided, must be valid
                  if (value != null && value.trim().isNotEmpty) {
                    final rate = double.tryParse(value.trim());
                    if (rate == null) {
                      return 'Please enter a valid rate';
                    }
                    if (rate < 0) {
                      return 'Rate cannot be negative';
                    }
                    if (rate > 999999) {
                      return 'Rate is too large';
                    }
                    // Check for reasonable decimal places
                    if (value.contains('.') && value.split('.')[1].length > 4) {
                      return 'Rate can have at most 4 decimal places';
                    }
                  }
                  return null;
                },
        ),
        if (hasPropertyRate) ...[
          const SizedBox(height: 8),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.blue.withAlpha(26),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.blue.withAlpha(128)),
            ),
            child: Row(
              children: [
                const Icon(Icons.info, color: Colors.blue, size: 20),
                const SizedBox(width: 8),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Property Rate Found',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Colors.blue,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Using ${_getUtilityTypeDisplayName(_utilityType!)} rate from ${selectedProperty?.name ?? ''}: ${matchingUtilityBill.formattedRate}${matchingUtilityBill.unit != null ? ' per ${matchingUtilityBill.unit}' : ''}',
                        style: const TextStyle(
                          fontSize: 13,
                          color: Colors.blue,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ] else if (_selectedPropertyId != null && _utilityType != null) ...[
          const SizedBox(height: 8),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.orange.withAlpha(26),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.orange.withAlpha(128)),
            ),
            child: Row(
              children: [
                const Icon(Icons.warning, color: Colors.orange, size: 20),
                const SizedBox(width: 8),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'No Property Rate Found',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Colors.orange,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'No ${_getUtilityTypeDisplayName(_utilityType!)} rate found for ${selectedProperty?.name ?? ''}. You can set a manual rate above or add utility rates to the property.',
                        style: const TextStyle(
                          fontSize: 13,
                          color: Colors.orange,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }
  
  /// Get due date text for the confirmation dialog
  String _getDueDateText() {
    final now = DateTime.now();
    final dueDay = widget.template!.dueDayOfMonth;
    final dueDate = DateTime(now.year, now.month, dueDay);
    
    // If due date has passed this month, show next month
    final actualDueDate = dueDate.isBefore(now) 
        ? DateTime(now.year, now.month + 1, dueDay)
        : dueDate;
    
    return '${actualDueDate.day}/${actualDueDate.month}/${actualDueDate.year}';
  }
} 