import 'package:flutter/material.dart';
import '../../models/room/room_model.dart';
import '../../models/room/amenity_model.dart';
import '../../models/room/room_type_model.dart';
import '../../models/property/property_model.dart';
import '../../services/service_locator.dart';
import '../../utils/currency_formatter.dart';

class RoomFormPage extends StatefulWidget {
  final Room? room;
  final String?
  propertyId; // Used when creating a new room for a specific property

  const RoomFormPage({super.key, this.room, this.propertyId});

  @override
  State<RoomFormPage> createState() => _RoomFormPageState();
}

class _RoomFormPageState extends State<RoomFormPage> {
  final _formKey = GlobalKey<FormState>();

  // Form controllers
  final _nameController = TextEditingController();
  final _rentalPriceController = TextEditingController();
  final _sizeController = TextEditingController();
  final _floorController = TextEditingController();
  final _notesController = TextEditingController();
  final _customAmenityController = TextEditingController();
  final _customRoomTypeController = TextEditingController();

  // Form values
  bool _isFurnished = false;
  RoomOccupancyStatus _occupancyStatus = RoomOccupancyStatus.vacant;
  String? _selectedPropertyId;

  // Room type values
  bool _isCustomRoomType = false;
  String? _selectedRoomTypeId;

  // Properties list
  List<Property> _properties = [];
  bool _loadingProperties = true;

  // Room types list
  List<RoomType> _roomTypes = [];
  bool _loadingRoomTypes = true;

  // Amenities
  final List<String> _predefinedAmenities = [
    'WiFi',
    'Air Conditioning',
    'Private Bathroom',
    'Kitchen Access',
    'Laundry Access',
    'Parking Space',
    'Cable TV',
    'Heating',
    'Balcony',
    'Desk/Workspace',
  ];

  Set<String> _selectedAmenities = {};

  bool _isLoading = false;
  bool _isEditing = false;

  @override
  void initState() {
    super.initState();
    _isEditing = widget.room != null;
    _selectedPropertyId = widget.propertyId;

    // Load properties and room types
    _loadProperties();
    _loadRoomTypes();

    if (_isEditing) {
      // Populate form with existing room data
      _nameController.text = widget.room!.name;
      _rentalPriceController.text = CurrencyFormatter.formatAmount(widget.room!.rentalPrice);
      _sizeController.text = widget.room!.size?.toString() ?? '';
      _floorController.text = widget.room!.floor?.toString() ?? '';
      _notesController.text = widget.room!.notes ?? '';

      // Extract amenity names from RoomAmenity objects
      _selectedAmenities =
          widget.room!.amenities.map((amenity) => amenity.displayName).toSet();

      _isFurnished = widget.room!.isFurnished;
      _occupancyStatus = widget.room!.occupancyStatus;
      _selectedPropertyId = widget.room!.propertyId;

      // Set room type
      if (widget.room!.roomTypeId != null) {
        _selectedRoomTypeId = widget.room!.roomTypeId;
        _isCustomRoomType = false;
      } else if (widget.room!.customRoomType != null) {
        _customRoomTypeController.text = widget.room!.customRoomType!;
        _isCustomRoomType = true;
      }
    }
  }

  Future<void> _loadProperties() async {
    try {
      final properties =
          await serviceLocator.propertyService.getAllProperties();
      if (mounted) {
        setState(() {
          _properties = properties;
          _loadingProperties = false;

          // If we don't have a preselected property and there are properties available,
          // select the first one by default
          if (_selectedPropertyId == null && properties.isNotEmpty) {
            _selectedPropertyId = properties.first.id;
          }
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading properties: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
        setState(() {
          _loadingProperties = false;
        });
      }
    }
  }

  Future<void> _loadRoomTypes() async {
    try {
      final roomTypes = await serviceLocator.roomService.getAllRoomTypes();
      if (mounted) {
        setState(() {
          _roomTypes = roomTypes;
          _loadingRoomTypes = false;

          // If we're not editing and there are room types available,
          // select the first one by default
          if (!_isEditing &&
              _selectedRoomTypeId == null &&
              roomTypes.isNotEmpty) {
            _selectedRoomTypeId = roomTypes.first.id;
          }
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading room types: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
        setState(() {
          _loadingRoomTypes = false;
        });
      }
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _rentalPriceController.dispose();
    _sizeController.dispose();
    _floorController.dispose();
    _notesController.dispose();
    _customAmenityController.dispose();
    _customRoomTypeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_isEditing ? 'Edit Room' : 'Add Room'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body:
          _isLoading || _loadingProperties || _loadingRoomTypes
              ? const Center(child: CircularProgressIndicator())
              : SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      // Property selection dropdown
                      if (!_isEditing) ...[
                        DropdownButtonFormField<String>(
                          decoration: const InputDecoration(
                            labelText: 'Select Property',
                            border: OutlineInputBorder(),
                            prefixIcon: Icon(Icons.home),
                          ),
                          value: _selectedPropertyId,
                          items:
                              _properties.map((property) {
                                return DropdownMenuItem(
                                  value: property.id,
                                  child: Text(property.name),
                                );
                              }).toList(),
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Please select a property';
                            }
                            return null;
                          },
                          onChanged: (value) {
                            if (value != null) {
                              setState(() {
                                _selectedPropertyId = value;
                              });
                            }
                          },
                        ),
                        const SizedBox(height: 16),
                      ],

                      // Room name
                      TextFormField(
                        controller: _nameController,
                        decoration: const InputDecoration(
                          labelText: 'Room Name',
                          border: OutlineInputBorder(),
                          prefixIcon: Icon(Icons.meeting_room),
                        ),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please enter a room name';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),

                      // Room type selection
                      Card(
                        margin: const EdgeInsets.only(bottom: 16),
                        child: Padding(
                          padding: const EdgeInsets.all(16),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text(
                                'Room Type',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 8),

                              // Switch between predefined and custom room types
                              Row(
                                children: [
                                  Expanded(
                                    child: RadioListTile<bool>(
                                      title: const Text('Predefined Type'),
                                      value: false,
                                      groupValue: _isCustomRoomType,
                                      onChanged: (value) {
                                        setState(() {
                                          _isCustomRoomType = value!;
                                        });
                                      },
                                    ),
                                  ),
                                  Expanded(
                                    child: RadioListTile<bool>(
                                      title: const Text('Custom Type'),
                                      value: true,
                                      groupValue: _isCustomRoomType,
                                      onChanged: (value) {
                                        setState(() {
                                          _isCustomRoomType = value!;
                                        });
                                      },
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 8),

                              // Show either predefined dropdown or custom text field
                              if (!_isCustomRoomType)
                                DropdownButtonFormField<String>(
                                  decoration: const InputDecoration(
                                    labelText: 'Select Room Type',
                                    border: OutlineInputBorder(),
                                    prefixIcon: Icon(Icons.category),
                                  ),
                                  value: _selectedRoomTypeId,
                                  items:
                                      _roomTypes.map((type) {
                                        return DropdownMenuItem(
                                          value: type.id,
                                          child: Text(type.name),
                                        );
                                      }).toList(),
                                  validator: (value) {
                                    if (!_isCustomRoomType &&
                                        (value == null || value.isEmpty)) {
                                      return 'Please select a room type';
                                    }
                                    return null;
                                  },
                                  onChanged: (value) {
                                    setState(() {
                                      _selectedRoomTypeId = value;
                                    });
                                  },
                                )
                              else
                                TextFormField(
                                  controller: _customRoomTypeController,
                                  decoration: const InputDecoration(
                                    labelText: 'Custom Room Type',
                                    border: OutlineInputBorder(),
                                    prefixIcon: Icon(Icons.edit),
                                    hintText: 'e.g., Penthouse, Guest Room',
                                  ),
                                  validator: (value) {
                                    if (_isCustomRoomType &&
                                        (value == null || value.isEmpty)) {
                                      return 'Please enter a custom room type';
                                    }
                                    return null;
                                  },
                                ),
                            ],
                          ),
                        ),
                      ),

                      // Rental price
                      _buildRentalPriceField(),
                      const SizedBox(height: 16),

                      // Size
                      TextFormField(
                        controller: _sizeController,
                        decoration: const InputDecoration(
                          labelText: 'Size (sq ft) - Optional',
                          border: OutlineInputBorder(),
                          prefixIcon: Icon(Icons.square_foot),
                        ),
                        keyboardType: TextInputType.number,
                        validator: (value) {
                          if (value != null && value.isNotEmpty) {
                            if (double.tryParse(value) == null) {
                              return 'Please enter a valid number';
                            }
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),

                      // Floor
                      TextFormField(
                        controller: _floorController,
                        decoration: const InputDecoration(
                          labelText: 'Floor - Optional',
                          border: OutlineInputBorder(),
                          prefixIcon: Icon(Icons.layers),
                        ),
                        keyboardType: TextInputType.number,
                        validator: (value) {
                          if (value != null && value.isNotEmpty) {
                            if (int.tryParse(value) == null) {
                              return 'Please enter a valid number';
                            }
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),

                      // Furnished checkbox
                      SwitchListTile(
                        title: const Text('Furnished'),
                        value: _isFurnished,
                        onChanged: (value) {
                          setState(() {
                            _isFurnished = value;
                          });
                        },
                        secondary: const Icon(Icons.chair),
                      ),
                      const SizedBox(height: 16),

                      // Occupancy status dropdown
                      DropdownButtonFormField<RoomOccupancyStatus>(
                        decoration: const InputDecoration(
                          labelText: 'Occupancy Status',
                          border: OutlineInputBorder(),
                          prefixIcon: Icon(Icons.people),
                        ),
                        value: _occupancyStatus,
                        items:
                            RoomOccupancyStatus.values.map((status) {
                              return DropdownMenuItem(
                                value: status,
                                child: Text(status.toString().split('.').last),
                              );
                            }).toList(),
                        onChanged: (value) {
                          if (value != null) {
                            setState(() {
                              _occupancyStatus = value;
                            });
                          }
                        },
                      ),
                      const SizedBox(height: 24),

                      // Amenities section
                      Card(
                        margin: const EdgeInsets.only(bottom: 24),
                        child: Padding(
                          padding: const EdgeInsets.all(16),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text(
                                'Amenities',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 8),
                              const Text(
                                'Select available amenities for this room',
                                style: TextStyle(color: Colors.grey),
                              ),
                              const SizedBox(height: 16),

                              // Predefined amenities
                              Wrap(
                                spacing: 8,
                                runSpacing: 8,
                                children:
                                    _predefinedAmenities.map((amenity) {
                                      final isSelected = _selectedAmenities
                                          .contains(amenity);
                                      return FilterChip(
                                        label: Text(amenity),
                                        selected: isSelected,
                                        onSelected: (selected) {
                                          setState(() {
                                            if (selected) {
                                              _selectedAmenities.add(amenity);
                                            } else {
                                              _selectedAmenities.remove(
                                                amenity,
                                              );
                                            }
                                          });
                                        },
                                      );
                                    }).toList(),
                              ),

                              const SizedBox(height: 16),
                              const Divider(),
                              const SizedBox(height: 16),

                              // Custom amenities
                              const Text(
                                'Custom Amenities',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 8),

                              // Display custom amenities
                              if (_selectedAmenities.any(
                                (a) => !_predefinedAmenities.contains(a),
                              ))
                                Wrap(
                                  spacing: 8,
                                  runSpacing: 8,
                                  children:
                                      _selectedAmenities
                                          .where(
                                            (a) =>
                                                !_predefinedAmenities.contains(
                                                  a,
                                                ),
                                          )
                                          .map((amenity) {
                                            return Chip(
                                              label: Text(amenity),
                                              onDeleted: () {
                                                setState(() {
                                                  _selectedAmenities.remove(
                                                    amenity,
                                                  );
                                                });
                                              },
                                            );
                                          })
                                          .toList(),
                                ),

                              const SizedBox(height: 16),

                              // Add custom amenity
                              Row(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Expanded(
                                    child: TextFormField(
                                      controller: _customAmenityController,
                                      decoration: const InputDecoration(
                                        labelText: 'Add Custom Amenity',
                                        border: OutlineInputBorder(),
                                        hintText:
                                            'e.g., Smart TV, Blackout Curtains',
                                      ),
                                    ),
                                  ),
                                  const SizedBox(width: 8),
                                  ElevatedButton(
                                    onPressed: () {
                                      final amenity =
                                          _customAmenityController.text.trim();
                                      if (amenity.isNotEmpty) {
                                        setState(() {
                                          _selectedAmenities.add(amenity);
                                          _customAmenityController.clear();
                                        });
                                      }
                                    },
                                    child: const Text('Add'),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ),

                      // Notes
                      TextFormField(
                        controller: _notesController,
                        decoration: const InputDecoration(
                          labelText: 'Notes - Optional',
                          border: OutlineInputBorder(),
                          prefixIcon: Icon(Icons.note),
                        ),
                        maxLines: 3,
                      ),
                      const SizedBox(height: 24),

                      // Submit button
                      ElevatedButton(
                        onPressed: _saveRoom,
                        style: ElevatedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 16),
                        ),
                        child: Text(
                          _isEditing ? 'UPDATE ROOM' : 'ADD ROOM',
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
    );
  }

  Widget _buildRentalPriceField() {
    final currency = serviceLocator.settingsService.selectedCurrency;
    final bool isKenyanShilling = (currency.code == 'KSH' || currency.code == 'KES');
    
    return TextFormField(
      controller: _rentalPriceController,
      decoration: InputDecoration(
        labelText: 'Rental Price',
        prefixText: isKenyanShilling ? 'KSH ' : '${currency.symbol} ',
        suffixText: isKenyanShilling ? '' : ' ${currency.code}',
        border: OutlineInputBorder(),
      ),
      keyboardType: const TextInputType.numberWithOptions(decimal: true),
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'Please enter a rental price';
        }
        final amount = CurrencyFormatter.parseAmount(value);
        if (amount <= 0) {
          return 'Please enter a valid rental price';
        }
        return null;
      },
      onChanged: (value) {
        // Only format when the field loses focus, not during typing
        if (value.isNotEmpty) {
          // Remove any existing formatting to allow clean input
          final cleanValue = value.replaceAll(RegExp(r'[^\d.]'), '');
          if (cleanValue != value) {
            _rentalPriceController.value = TextEditingValue(
              text: cleanValue,
              selection: TextSelection.collapsed(offset: cleanValue.length),
            );
          }
        }
      },
      onEditingComplete: () {
        // Format when editing is complete (user presses done/enter)
        if (_rentalPriceController.text.isNotEmpty) {
          final amount = CurrencyFormatter.parseAmount(_rentalPriceController.text);
          if (amount > 0) {
            final formatted = CurrencyFormatter.formatAmount(amount);
            _rentalPriceController.text = formatted;
          }
        }
        FocusScope.of(context).unfocus();
      },
    );
  }

  Future<void> _saveRoom() async {
    if (_formKey.currentState!.validate()) {
      setState(() {
        _isLoading = true;
      });

      try {
        final rentalPrice = CurrencyFormatter.parseAmount(_rentalPriceController.text);
        // Convert string amenities to RoomAmenity objects
        final amenities =
            _selectedAmenities.map((amenityName) {
              // For simplicity, we're treating all amenities as custom amenities
              // In a real app, you'd check if they match predefined amenities
              return RoomAmenity(
                roomId:
                    widget.room?.id ??
                    '', // This will be overridden by the service
                customAmenityName: amenityName,
              );
            }).toList();

        // Determine room type (either ID or custom)
        String? roomTypeId;
        String? customRoomType;

        if (_isCustomRoomType) {
          customRoomType = _customRoomTypeController.text.trim();
        } else {
          roomTypeId = _selectedRoomTypeId;
        }

        if (_isEditing) {
          // Update existing room
          final updatedRoom = await serviceLocator.roomService.updateRoom(
            id: widget.room!.id,
            name: _nameController.text,
            rentalPrice: rentalPrice,
            size:
                _sizeController.text.isNotEmpty
                    ? double.parse(_sizeController.text)
                    : null,
            floor:
                _floorController.text.isNotEmpty
                    ? int.parse(_floorController.text)
                    : null,
            isFurnished: _isFurnished,
            occupancyStatus: _occupancyStatus,
            roomTypeId: roomTypeId,
            customRoomType: customRoomType,
            amenities: amenities,
            notes:
                _notesController.text.isNotEmpty ? _notesController.text : null,
          );

          if (mounted) {
            if (updatedRoom != null) {
              // Show success message
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Row(
                    children: [
                      const Icon(Icons.check_circle, color: Colors.white),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          'Room "${_nameController.text}" updated successfully',
                        ),
                      ),
                    ],
                  ),
                  backgroundColor: Colors.green,
                  duration: const Duration(seconds: 3),
                  behavior: SnackBarBehavior.floating,
                ),
              );
              Navigator.pop(context, true); // Return true to indicate success
            } else {
              // Show error if room couldn't be updated
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Row(
                    children: [
                      Icon(Icons.error, color: Colors.white),
                      SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          'Failed to update room. The room may have been deleted.',
                        ),
                      ),
                    ],
                  ),
                  backgroundColor: Colors.red,
                  duration: Duration(seconds: 5),
                  behavior: SnackBarBehavior.floating,
                ),
              );
            }
          }
        } else {
          // Add new room
          final propertyId = _selectedPropertyId;
          if (propertyId == null) {
            throw Exception('Property ID is required to create a new room');
          }

          final newRoom = await serviceLocator.roomService.addRoom(
            name: _nameController.text,
            propertyId: propertyId,
            rentalPrice: rentalPrice,
            size:
                _sizeController.text.isNotEmpty
                    ? double.parse(_sizeController.text)
                    : null,
            floor:
                _floorController.text.isNotEmpty
                    ? int.parse(_floorController.text)
                    : null,
            isFurnished: _isFurnished,
            occupancyStatus: _occupancyStatus,
            roomTypeId: roomTypeId,
            customRoomType: customRoomType,
            amenities: amenities,
            notes:
                _notesController.text.isNotEmpty ? _notesController.text : null,
          );

          if (mounted) {
            if (newRoom != null) {
              // Show success message
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Row(
                    children: [
                      const Icon(Icons.check_circle, color: Colors.white),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          'Room "${_nameController.text}" added successfully',
                        ),
                      ),
                    ],
                  ),
                  backgroundColor: Colors.green,
                  duration: const Duration(seconds: 3),
                  behavior: SnackBarBehavior.floating,
                ),
              );
              Navigator.pop(context, true); // Return true to indicate success
            } else {
              // Show error if room couldn't be created
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Row(
                    children: [
                      Icon(Icons.error, color: Colors.white),
                      SizedBox(width: 8),
                      Expanded(
                        child: Text('Failed to create room. Please try again.'),
                      ),
                    ],
                  ),
                  backgroundColor: Colors.red,
                  duration: Duration(seconds: 5),
                  behavior: SnackBarBehavior.floating,
                ),
              );
            }
          }
        }
      } catch (e) {
        if (mounted) {
          // Show detailed error message
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  const Icon(Icons.error, color: Colors.white),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          _isEditing
                              ? 'Failed to update room'
                              : 'Failed to add room',
                        ),
                        Text(
                          e.toString(),
                          style: const TextStyle(fontSize: 12),
                          overflow: TextOverflow.ellipsis,
                          maxLines: 2,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              backgroundColor: Colors.red,
              duration: const Duration(seconds: 5),
              behavior: SnackBarBehavior.floating,
              action: SnackBarAction(
                label: 'DISMISS',
                textColor: Colors.white,
                onPressed: () {
                  ScaffoldMessenger.of(context).hideCurrentSnackBar();
                },
              ),
            ),
          );
        }
      } finally {
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
      }
    }
  }
}
