group 'com.llfbandit.app_links'
version '1.0'

buildscript {
    repositories {
        google()
        mavenCentral()
    }

    dependencies {
        // https://developer.android.com/studio/releases/gradle-plugin
        classpath 'com.android.tools.build:gradle:8.5.2'
    }
}

rootProject.allprojects {
    repositories {
        google()
        mavenCentral()
    }
}

apply plugin: 'com.android.library'

android {
    namespace 'com.llfbandit.app_links'

    compileSdk 34

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    defaultConfig {
        minSdkVersion 19
        consumerProguardFiles 'proguard.txt'
    }
}

dependencies {
    implementation 'androidx.annotation:annotation:1.3.0'
    implementation 'org.microg:safe-parcel:1.7.0'
}