import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

/// Authentication utility functions
class AuthUtils {
  /// Check if the current user has the specified role
  static Future<bool> userHasRole(String role) async {
    final client = Supabase.instance.client;

    try {
      // Check if user is authenticated
      final user = client.auth.currentUser;
      if (user == null) return false;

      // First check user metadata for role
      final userMetadata = user.userMetadata;
      if (userMetadata != null && userMetadata['role'] == role) {
        return true;
      }

      // If not found in metadata, check in the user_profiles table
      final response = await client.rpc(
        'user_has_role',
        params: {'required_role': role},
      );
      return response as bool;
    } catch (e) {
      if (kDebugMode) debugPrint('Error checking user role: $e');
      return false;
    }
  }

  /// Check if the current user is a tenant
  static Future<bool> isTenant() async {
    return await userHasRole('tenant');
  }

  /// Check if the current user is an admin
  static Future<bool> isAdmin() async {
    return await userHasRole('admin');
  }

  /// Check if the current user is a manager
  static Future<bool> isManager() async {
    return await userHasRole('manager');
  }

  /// Check if the current user is authenticated
  static bool isAuthenticated() {
    final client = Supabase.instance.client;
    return client.auth.currentUser != null;
  }

  /// Get the current user's ID
  static String? getCurrentUserId() {
    final client = Supabase.instance.client;
    return client.auth.currentUser?.id;
  }

  /// Get the current user's email
  static String? getCurrentUserEmail() {
    final client = Supabase.instance.client;
    return client.auth.currentUser?.email;
  }
}
