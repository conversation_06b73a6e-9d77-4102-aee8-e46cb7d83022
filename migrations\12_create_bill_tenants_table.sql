-- Create bill_tenants table to support multiple tenants per bill
CREATE TABLE IF NOT EXISTS bill_tenants (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  bill_id UUID NOT NULL REFERENCES bills(id) ON DELETE CASCADE,
  tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
  split_amount DECIMAL(10, 2),
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ,
  UNIQUE(bill_id, tenant_id)
);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_bill_tenants_bill_id ON bill_tenants(bill_id);
CREATE INDEX IF NOT EXISTS idx_bill_tenants_tenant_id ON bill_tenants(tenant_id);

-- Drop existing view if it exists
DROP VIEW IF EXISTS tenant_bills_view;

-- Create view for tenants with their assigned bills
CREATE OR REPLACE VIEW tenant_bills_view 
WITH (security_invoker=true)
AS
SELECT 
  bt.id as relation_id,
  bt.bill_id,
  bt.tenant_id,
  bt.split_amount,
  b.title as bill_title,
  b.amount as bill_amount,
  b.due_date,
  b.status as bill_status,
  b.type as bill_type,
  t.first_name || ' ' || t.last_name as tenant_name,
  t.email as tenant_email
FROM 
  bill_tenants bt
JOIN 
  bills b ON bt.bill_id = b.id
JOIN 
  tenants t ON bt.tenant_id = t.id;

-- Add RLS policies for bill_tenants table
ALTER TABLE bill_tenants ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS tenant_bill_select_policy ON bill_tenants;
DROP POLICY IF EXISTS tenant_bill_insert_policy ON bill_tenants;
DROP POLICY IF EXISTS tenant_bill_update_policy ON bill_tenants;
DROP POLICY IF EXISTS tenant_bill_delete_policy ON bill_tenants;

-- Policy for users to see their own bill_tenant relations with optimized performance
CREATE POLICY tenant_bill_select_policy ON bill_tenants
  FOR SELECT 
  USING (
    -- Use a subquery to calculate auth.role() just once
    ((SELECT auth.role()) = 'authenticated') AND
    EXISTS (
      SELECT 1 FROM tenants t
      WHERE t.id = tenant_id AND 
      (t.user_id = (SELECT auth.uid()) OR t.notes LIKE '%Linked to auth user: ' || (SELECT auth.uid()) || '%')
    )
  );

-- Policy for users to insert bill_tenant relations with optimized performance
CREATE POLICY tenant_bill_insert_policy ON bill_tenants
  FOR INSERT 
  WITH CHECK (
    -- Use a subquery to calculate auth.role() just once
    ((SELECT auth.role()) = 'authenticated') AND
    EXISTS (
      SELECT 1 FROM tenants t
      WHERE t.id = tenant_id AND 
      (t.user_id = (SELECT auth.uid()) OR t.notes LIKE '%Linked to auth user: ' || (SELECT auth.uid()) || '%')
    )
  );

-- Policy for users to update bill_tenant relations with optimized performance
CREATE POLICY tenant_bill_update_policy ON bill_tenants
  FOR UPDATE 
  USING (
    -- Use a subquery to calculate auth.role() just once
    ((SELECT auth.role()) = 'authenticated') AND
    EXISTS (
      SELECT 1 FROM tenants t
      WHERE t.id = tenant_id AND 
      (t.user_id = (SELECT auth.uid()) OR t.notes LIKE '%Linked to auth user: ' || (SELECT auth.uid()) || '%')
    )
  );

-- Policy for users to delete bill_tenant relations with optimized performance
CREATE POLICY tenant_bill_delete_policy ON bill_tenants
  FOR DELETE 
  USING (
    -- Use a subquery to calculate auth.role() just once
    ((SELECT auth.role()) = 'authenticated') AND
    EXISTS (
      SELECT 1 FROM tenants t
      WHERE t.id = tenant_id AND 
      (t.user_id = (SELECT auth.uid()) OR t.notes LIKE '%Linked to auth user: ' || (SELECT auth.uid()) || '%')
    )
  ); 