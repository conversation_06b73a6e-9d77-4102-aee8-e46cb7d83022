@echo off
REM Setup script for Tenanta environment configuration (Windows)
echo Setting up Tenanta environment configuration...

REM Check if .env already exists
if exist ".env" (
    echo ⚠️  .env file already exists!
    set /p "overwrite=Do you want to overwrite it? (y/N): "
    if /i not "%overwrite%"=="y" (
        echo Setup cancelled.
        exit /b 0
    )
)

REM Copy .env.example to .env
if exist ".env.example" (
    copy ".env.example" ".env" >nul
    echo ✅ Created .env file from .env.example
) else (
    echo ❌ .env.example file not found!
    exit /b 1
)

echo.
echo 📝 Please edit the .env file and add your Supabase credentials:
echo    - SUPABASE_URL: Your Supabase project URL
echo    - SUPABASE_ANON_KEY: Your Supabase anonymous key
echo.
echo You can find these values in your Supabase project settings.
echo.
echo 🔒 Security reminder: Never commit the .env file to version control!
echo.
echo Setup complete! Run 'flutter run' to start the app.
pause
