import 'package:flutter/material.dart';
import 'dart:math';
import 'package:intl/intl.dart';
import '../../models/bill/bill.dart';
import '../../services/service_locator.dart';
import '../../services/bill_generation_service.dart';
import '../../utils/currency_formatter.dart';
import '../../utils/logger.dart';
import '../bills/bill_detail_page.dart';
import '../bills/add_bill_page.dart';
import 'payment_summary_widget.dart';
import 'record_payment_page.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

class PaymentManagementPage extends StatefulWidget {
  final int? initialTabIndex;

  const PaymentManagementPage({super.key, this.initialTabIndex});

  @override
  State<PaymentManagementPage> createState() => _PaymentManagementPageState();
}

class _PaymentManagementPageState extends State<PaymentManagementPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  bool _isLoading = true;
  List<Bill> _allBills = [];
  List<Bill> _pendingBills = [];
  List<Bill> _paidBills = [];
  List<Bill> _overdueBills = [];

  // Filtered lists for each tab
  List<Bill> _filteredAllBills = [];
  List<Bill> _filteredPendingBills = [];
  List<Bill> _filteredPaidBills = [];
  List<Bill> _filteredOverdueBills = [];

  double _totalPending = 0;
  double _totalPaid = 0;
  double _totalOverdue = 0;

  // Filter and search state
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  String _sortBy = 'dueDate'; // dueDate, amount, title, createdAt
  bool _sortAscending = false; // false = newest/highest first
  BillType? _filterType;
  String? _filterPropertyId;

  // Pagination state
  final int _itemsPerPage = 15;
  int _allBillsCurrentPage = 1;
  int _pendingBillsCurrentPage = 1;
  int _paidBillsCurrentPage = 1;
  int _overdueBillsCurrentPage = 1;

  // Cache for tenant, room, and property details
  final Map<String, String> _tenantNames = {};
  final Map<String, String> _roomNames = {};
  final Map<String, String> _propertyNames = {};

  @override
  void initState() {
    super.initState();
    _tabController = TabController(
      length: 4,
      vsync: this,
      initialIndex: widget.initialTabIndex ?? 0,
    );
    _searchController.addListener(_onSearchChanged);
    _loadBills();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  void _onSearchChanged() {
    setState(() {
      _searchQuery = _searchController.text.toLowerCase();
      _applyFilters();
    });
  }

  void _applyFilters() {
    // Filter all bills based on search query
    _filteredAllBills = _filterBills(_allBills);
    _filteredPendingBills = _filterBills(_pendingBills);
    _filteredPaidBills = _filterBills(_paidBills);
    _filteredOverdueBills = _filterBills(_overdueBills);

    // Reset pagination to first page when filters change
    _allBillsCurrentPage = 1;
    _pendingBillsCurrentPage = 1;
    _paidBillsCurrentPage = 1;
    _overdueBillsCurrentPage = 1;
  }

  List<Bill> _filterBills(List<Bill> bills) {
    if (_searchQuery.isEmpty &&
        _filterType == null &&
        _filterPropertyId == null) {
      return bills;
    }

    return bills.where((bill) {
      // Search filter
      bool matchesSearch = true;
      if (_searchQuery.isNotEmpty) {
        final tenantName =
            bill.tenantId != null
                ? (_tenantNames[bill.tenantId] ?? '').toLowerCase()
                : '';
        final roomName =
            bill.roomId != null
                ? (_roomNames[bill.roomId] ?? '').toLowerCase()
                : '';
        final propertyName =
            bill.propertyId != null
                ? (_propertyNames[bill.propertyId] ?? '').toLowerCase()
                : '';

        matchesSearch =
            bill.title.toLowerCase().contains(_searchQuery) ||
            bill.description.toLowerCase().contains(_searchQuery) ||
            tenantName.contains(_searchQuery) ||
            roomName.contains(_searchQuery) ||
            propertyName.contains(_searchQuery);
      }

      // Type filter
      bool matchesType = _filterType == null || bill.type == _filterType;

      // Property filter
      bool matchesProperty =
          _filterPropertyId == null || bill.propertyId == _filterPropertyId;

      return matchesSearch && matchesType && matchesProperty;
    }).toList();
  }

  List<Bill> _sortBills(List<Bill> bills) {
    final sortedBills = List<Bill>.from(bills);

    sortedBills.sort((a, b) {
      int comparison = 0;

      switch (_sortBy) {
        case 'dueDate':
          comparison = a.dueDate.compareTo(b.dueDate);
          break;
        case 'amount':
          comparison = a.amount.compareTo(b.amount);
          break;
        case 'title':
          comparison = a.title.compareTo(b.title);
          break;
        case 'createdAt':
          comparison = a.createdAt.compareTo(b.createdAt);
          break;
      }

      return _sortAscending ? comparison : -comparison;
    });

    return sortedBills;
  }

  Widget _buildSearchAndFilterControls() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(25),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Search TextField
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'Search bills...',
              prefixIcon: const Icon(Icons.search),
              suffixIcon:
                  _searchQuery.isNotEmpty
                      ? IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () {
                          _searchController.clear();
                        },
                      )
                      : null,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 12,
              ),
            ),
          ),
          const SizedBox(height: 12),
          // First Row - Type and Property Filters
          Row(
            children: [
              // Bill Type Filter
              Expanded(
                child: DropdownButtonFormField<BillType?>(
                  value: _filterType,
                  isExpanded: true,
                  decoration: InputDecoration(
                    labelText: 'Filter by Type',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 12,
                    ),
                  ),
                  items: [
                    const DropdownMenuItem<BillType?>(
                      value: null,
                      child: Text('All Types'),
                    ),
                    ...BillType.values.map(
                      (type) => DropdownMenuItem(
                        value: type,
                        child: Text(type.name.toUpperCase()),
                      ),
                    ),
                  ],
                  onChanged: (value) {
                    setState(() {
                      _filterType = value;
                      _applyFilters();
                    });
                  },
                ),
              ),
              const SizedBox(width: 12),
              // Property Filter
              Expanded(
                child: DropdownButtonFormField<String?>(
                  value: _filterPropertyId,
                  isExpanded: true,
                  decoration: InputDecoration(
                    labelText: 'Filter by Property',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 12,
                    ),
                  ),
                  items: [
                    const DropdownMenuItem<String?>(
                      value: null,
                      child: Text('All Properties'),
                    ),
                    ..._propertyNames.entries.map(
                      (entry) => DropdownMenuItem(
                        value: entry.key,
                        child: Text(entry.value),
                      ),
                    ),
                  ],
                  onChanged: (value) {
                    setState(() {
                      _filterPropertyId = value;
                      _applyFilters();
                    });
                  },
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          // Second Row - Sort Controls
          Row(
            children: [
              // Sort Controls
              Expanded(
                child: DropdownButtonFormField<String>(
                  value: _sortBy,
                  isExpanded: true,
                  decoration: InputDecoration(
                    labelText: 'Sort by',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 12,
                    ),
                  ),
                  items: const [
                    DropdownMenuItem(value: 'dueDate', child: Text('Due Date')),
                    DropdownMenuItem(value: 'amount', child: Text('Amount')),
                    DropdownMenuItem(value: 'title', child: Text('Title')),
                    DropdownMenuItem(
                      value: 'createdAt',
                      child: Text('Created Date'),
                    ),
                  ],
                  onChanged: (value) {
                    setState(() {
                      _sortBy = value!;
                      _applyFilters();
                    });
                  },
                ),
              ),
              const SizedBox(width: 12),
              // Sort Direction Toggle
              Container(
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: IconButton(
                  icon: Icon(
                    _sortAscending ? Icons.arrow_upward : Icons.arrow_downward,
                  ),
                  tooltip:
                      _sortAscending ? 'Sort Descending' : 'Sort Ascending',
                  onPressed: () {
                    setState(() {
                      _sortAscending = !_sortAscending;
                      _applyFilters();
                    });
                  },
                ),
              ),
              const SizedBox(width: 12),
              // Clear Filters Button
              Container(
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: IconButton(
                  icon: const Icon(Icons.clear_all),
                  tooltip: 'Clear All Filters',
                  onPressed: () {
                    setState(() {
                      _filterType = null;
                      _filterPropertyId = null;
                      _searchController.clear();
                      _searchQuery = '';
                      _applyFilters();
                    });
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Future<void> _loadBills() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final bills = await serviceLocator.billService.getAllBills();

      // Categorize bills
      final pending = <Bill>[];
      final paid = <Bill>[];
      final overdue = <Bill>[];

      double pendingAmount = 0;
      double paidAmount = 0;
      double overdueAmount = 0;

      for (final bill in bills) {
        if (bill.status == BillStatus.paid) {
          paid.add(bill);
          paidAmount += bill.amount;
        } else if (bill.isOverdue()) {
          overdue.add(bill);
          overdueAmount += bill.amount;
        } else {
          pending.add(bill);
          pendingAmount += bill.amount;
        }
      }

      // Load tenant, room, and property details for all bills
      await _loadBillDetails(bills);

      if (!mounted) return;

      setState(() {
        _allBills = bills;
        _pendingBills = pending;
        _paidBills = paid;
        _overdueBills = overdue;
        _totalPending = pendingAmount;
        _totalPaid = paidAmount;
        _totalOverdue = overdueAmount;
        _isLoading = false;
      });

      // Apply filters after loading
      _applyFilters();
    } catch (e) {
      if (!mounted) return;
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('Error loading bills: $e')));
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _loadBillDetails(List<Bill> bills) async {
    // Load tenant, room, and property details for each bill
    for (final bill in bills) {
      if (bill.tenantId != null && !_tenantNames.containsKey(bill.tenantId)) {
        try {
          final tenant = await serviceLocator.tenantService.getTenantById(
            bill.tenantId!,
          );
          if (tenant != null) {
            _tenantNames[bill.tenantId!] =
                '${tenant.firstName} ${tenant.lastName}';
          }
        } catch (e) {
          // Handle error silently
        }
      }

      if (bill.roomId != null && !_roomNames.containsKey(bill.roomId)) {
        try {
          final room = await serviceLocator.roomService.getRoomById(
            bill.roomId!,
          );
          if (room != null) {
            _roomNames[bill.roomId!] = room.name;

            if (!_propertyNames.containsKey(room.propertyId)) {
              final property = await serviceLocator.propertyService
                  .getPropertyById(room.propertyId);
              if (property != null) {
                _propertyNames[room.propertyId] = property.name;
              }
            }
          }
        } catch (e) {
          // Handle error silently
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Payment Management'),
        actions: [
          IconButton(icon: const Icon(Icons.refresh), onPressed: _loadBills),
          IconButton(
            icon: const Icon(Icons.auto_awesome),
            tooltip: 'Generate Bills',
            onPressed: _triggerBillGeneration,
          ),
          IconButton(
            icon: const Icon(Icons.approval),
            tooltip: 'Approve/reject payment',
            onPressed: () {
              Navigator.of(context).pushNamed('/payment-history');
            },
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          tabs: [
            const Tab(text: 'All'),
            Tab(text: 'Pending (${_pendingBills.length})'),
            Tab(text: 'Paid (${_paidBills.length})'),
            Tab(text: 'Overdue (${_overdueBills.length})'),
          ],
        ),
      ),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : TabBarView(
                controller: _tabController,
                children: [
                  // All tab with quick actions
                  _buildAllTabWithQuickActions(),
                  // Other tabs without quick actions
                  _buildTabContent(
                    _sortBills(_filteredPendingBills),
                    'Pending Bills',
                  ),
                  _buildTabContent(
                    _sortBills(_filteredPaidBills),
                    'Paid Bills',
                  ),
                  _buildTabContent(
                    _sortBills(_filteredOverdueBills),
                    'Overdue Bills',
                  ),
                ],
              ),
      floatingActionButton: _buildFloatingActionButtons(),
      floatingActionButtonLocation: FloatingActionButtonLocation.endFloat,
    );
  }

  Widget _buildAllTabWithQuickActions() {
    return SingleChildScrollView(
      padding: const EdgeInsets.only(
        bottom: 60,
      ), // Increased bottom padding for FAB
      child: Column(
        children: [
          // Quick Actions Section
          Container(
            padding: const EdgeInsets.symmetric(
              vertical: 24.0,
              horizontal: 16.0,
            ),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: const EdgeInsets.only(bottom: 16.0),
                  child: Text(
                    'Quick Actions',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    // Verify Payment Button
                    ElevatedButton.icon(
                      onPressed: () {
                        Navigator.of(
                          context,
                        ).pushNamed('/payment-history').then((result) {
                          if (result == true) {
                            _loadBills();
                          }
                        });
                      },
                      icon: const Icon(Icons.verified),
                      label: const Text('Verify Payment'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor:
                            Theme.of(context).colorScheme.secondaryContainer,
                        foregroundColor:
                            Theme.of(context).colorScheme.onSecondaryContainer,
                        padding: const EdgeInsets.symmetric(
                          horizontal: 24,
                          vertical: 12,
                        ),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(28),
                        ),
                      ),
                    ),

                    // Add Button
                    ElevatedButton.icon(
                      onPressed: () {
                        Navigator.of(
                          context,
                        ).pushNamed('/select-tenant-for-bill').then((result) {
                          if (result == true) {
                            _loadBills();
                          }
                        });
                      },
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 12,
                        ),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(28),
                        ),
                        backgroundColor:
                            Theme.of(context).colorScheme.tertiaryContainer,
                        foregroundColor:
                            Theme.of(context).colorScheme.onTertiaryContainer,
                      ),
                      icon: const Icon(Icons.add),
                      label: const Text('Add New Bill'),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    // Create Group Bill Button
                    ElevatedButton.icon(
                      onPressed: () {
                        Navigator.of(
                          context,
                        ).pushNamed('/multi-tenant-bill').then((result) {
                          if (result == true) {
                            _loadBills();
                          }
                        });
                      },
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 12,
                        ),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(28),
                        ),
                        backgroundColor:
                            Theme.of(context).colorScheme.primaryContainer,
                        foregroundColor:
                            Theme.of(context).colorScheme.onPrimaryContainer,
                      ),
                      icon: const Icon(Icons.people),
                      label: const Text('Group Bill'),
                    ),

                    // Approve/reject payment Button
                    ElevatedButton.icon(
                      onPressed: () {
                        Navigator.of(context).pushNamed('/payment-history');
                      },
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 12,
                        ),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(28),
                        ),
                        backgroundColor:
                            Theme.of(
                              context,
                            ).colorScheme.surfaceContainerHighest,
                        foregroundColor:
                            Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                      icon: const Icon(Icons.approval),
                      label: const Text('Approve/reject payment'),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                Row(
                  children: [
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: () {
                          AppLogger.info('Navigating to bill templates page');
                          Navigator.of(
                            context,
                          ).pushNamed('/bill-templates').then((result) {
                            if (result == true) {
                              _loadBills();
                            }
                          });
                        },
                        style: ElevatedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 16,
                            vertical: 12,
                          ),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(28),
                          ),
                          backgroundColor:
                              Theme.of(context).colorScheme.errorContainer,
                          foregroundColor:
                              Theme.of(context).colorScheme.onErrorContainer,
                        ),
                        icon: const Icon(Icons.auto_awesome),
                        label: const Text('Bill Templates'),
                      ),
                    ),
                    const SizedBox(width: 8),
                    ElevatedButton(
                      onPressed: _triggerBillGeneration,
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 12,
                        ),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(28),
                        ),
                        backgroundColor:
                            Theme.of(context).colorScheme.errorContainer,
                        foregroundColor:
                            Theme.of(context).colorScheme.onErrorContainer,
                      ),
                      child: const Icon(Icons.play_arrow, size: 20),
                    ),
                  ],
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),
          // Payment Summary Widget
          PaymentSummaryWidget(
            totalPending: _totalPending,
            totalPaid: _totalPaid,
            totalOverdue: _totalOverdue,
            pendingCount: _pendingBills.length,
            paidCount: _paidBills.length,
            overdueCount: _overdueBills.length,
          ),

          // Search and Filter Controls
          _buildSearchAndFilterControls(),

          // Bills List
          _buildBillsList(_sortBills(_filteredAllBills), 'All Bills'),
        ],
      ),
    );
  }

  Widget _buildTabContent(List<Bill> bills, String title) {
    return SingleChildScrollView(
      padding: const EdgeInsets.only(
        bottom: 120,
      ), // Increased bottom padding for FAB
      child: Column(
        children: [
          // Payment Summary Widget
          PaymentSummaryWidget(
            totalPending: _totalPending,
            totalPaid: _totalPaid,
            totalOverdue: _totalOverdue,
            pendingCount: _pendingBills.length,
            paidCount: _paidBills.length,
            overdueCount: _overdueBills.length,
          ),

          // Search and Filter Controls
          _buildSearchAndFilterControls(),

          // Bills List
          _buildBillsList(bills, title),
        ],
      ),
    );
  }

  Widget _buildBillsList(List<Bill> bills, String title) {
    double totalAmount = bills.fold(0, (sum, bill) => sum + bill.amount);

    // Determine current page based on tab title
    int currentPage;
    switch (title) {
      case 'All Bills':
        currentPage = _allBillsCurrentPage;
        break;
      case 'Pending Bills':
        currentPage = _pendingBillsCurrentPage;
        break;
      case 'Paid Bills':
        currentPage = _paidBillsCurrentPage;
        break;
      case 'Overdue Bills':
        currentPage = _overdueBillsCurrentPage;
        break;
      default:
        currentPage = 1;
    }

    // Calculate pagination values
    final int totalItems = bills.length;
    final int totalPages = (totalItems / _itemsPerPage).ceil();
    final int startIndex = (currentPage - 1) * _itemsPerPage;
    final int endIndex = min(startIndex + _itemsPerPage, totalItems);

    // Get current page items
    final List<Bill> currentPageItems =
        bills.isEmpty ? [] : bills.sublist(startIndex, endIndex);

    // Function to update current page
    void updatePage(int newPage) {
      setState(() {
        switch (title) {
          case 'All Bills':
            _allBillsCurrentPage = newPage;
            break;
          case 'Pending Bills':
            _pendingBillsCurrentPage = newPage;
            break;
          case 'Paid Bills':
            _paidBillsCurrentPage = newPage;
            break;
          case 'Overdue Bills':
            _overdueBillsCurrentPage = newPage;
            break;
        }
      });
    }

    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(16),
          color: Theme.of(context).colorScheme.surfaceContainerHighest,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(title, style: Theme.of(context).textTheme.titleMedium),
              Text(
                CurrencyFormatter.formatCurrency(totalAmount),
                style: Theme.of(
                  context,
                ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
              ),
            ],
          ),
        ),
        bills.isEmpty
            ? Container(
              padding: const EdgeInsets.all(32),
              child: Center(child: Text('No ${title.toLowerCase()} found')),
            )
            : Column(
              children: [
                SizedBox(
                  height: MediaQuery.of(context).size.height * 0.5,
                  child: ListView.builder(
                    shrinkWrap: true,
                    physics: const ClampingScrollPhysics(),
                    itemCount: currentPageItems.length,
                    itemBuilder:
                        (context, index) =>
                            _buildBillCard(currentPageItems[index]),
                  ),
                ),
                // Pagination controls
                if (totalPages > 1)
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: 8.0),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        // Previous button
                        IconButton(
                          icon: const Icon(Icons.chevron_left),
                          onPressed:
                              currentPage > 1
                                  ? () => updatePage(currentPage - 1)
                                  : null,
                          tooltip: 'Previous page',
                        ),
                        // Page indicator
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 16,
                            vertical: 8,
                          ),
                          decoration: BoxDecoration(
                            color:
                                Theme.of(
                                  context,
                                ).colorScheme.surfaceContainerHighest,
                            borderRadius: BorderRadius.circular(16),
                          ),
                          child: Text(
                            'Page $currentPage of $totalPages',
                            style: Theme.of(context).textTheme.bodyMedium,
                          ),
                        ),
                        // Next button
                        IconButton(
                          icon: const Icon(Icons.chevron_right),
                          onPressed:
                              currentPage < totalPages
                                  ? () => updatePage(currentPage + 1)
                                  : null,
                          tooltip: 'Next page',
                        ),
                      ],
                    ),
                  ),
                // Item count indicator
                const SizedBox(height: 8),
                Text(
                  'Showing ${startIndex + 1}-${endIndex} of $totalItems bills',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ),
                const SizedBox(
                  height: 8,
                ), // Add SizedBox for consistent spacing
              ],
            ),
      ],
    );
  }

  Widget _buildBillCard(Bill bill) {
    final isOverdue = bill.isOverdue();

    // Get tenant, room, and property names from cache
    final tenantName =
        bill.tenantId != null ? _tenantNames[bill.tenantId] : null;
    final roomName = bill.roomId != null ? _roomNames[bill.roomId] : null;
    final propertyName =
        bill.propertyId != null ? _propertyNames[bill.propertyId] : null;

    Color statusColor;
    switch (bill.status) {
      case BillStatus.paid:
        statusColor = Colors.green;
        break;
      case BillStatus.pending:
        statusColor = isOverdue ? Colors.red : Colors.orange;
        break;
      case BillStatus.overdue:
        statusColor = Colors.red;
        break;
    }

    return Card(
      margin: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
      child: ListTile(
        title: Text(
          bill.title,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 4),
            if (tenantName != null)
              Text(
                'Tenant: $tenantName',
                style: TextStyle(color: Colors.grey[800], fontSize: 13),
              ),
            if (roomName != null)
              Text(
                'House: $roomName',
                style: TextStyle(color: Colors.grey[800], fontSize: 13),
              ),
            if (propertyName != null)
              Text(
                'Property: $propertyName',
                style: TextStyle(color: Colors.grey[800], fontSize: 13),
              ),
            const SizedBox(height: 4),
            Text(
              'Due: ${DateFormat.yMMMd().format(bill.dueDate)}',
              style: TextStyle(color: Colors.grey[700], fontSize: 13),
            ),
            Text(
              'Amount: ${CurrencyFormatter.formatCurrency(bill.amount)}',
              style: TextStyle(fontWeight: FontWeight.w500, fontSize: 13),
            ),
            if (bill.isPartiallyPaid())
              Text(
                'Paid: ${CurrencyFormatter.formatCurrency(bill.paidAmount!)} (${((bill.paidAmount! / bill.amount) * 100).toStringAsFixed(0)}%)',
                style: TextStyle(color: Colors.green[700], fontSize: 13),
              ),
          ],
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Action menu button
            IconButton(
              icon: const Icon(Icons.more_vert),
              onPressed: () => _showBillOptions(bill),
            ),
            Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: statusColor.withAlpha(51),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: statusColor),
                  ),
                  child: Text(
                    isOverdue && bill.status != BillStatus.paid
                        ? 'OVERDUE'
                        : bill.status.name.toUpperCase(),
                    style: TextStyle(
                      color: statusColor,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  bill.type.name.toUpperCase(),
                  style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                ),
              ],
            ),
          ],
        ),
        onTap: () async {
          final result = await Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) => BillDetailPage(billId: bill.id),
            ),
          );
          if (result == true) {
            _loadBills();
          }
        },
      ),
    );
  }

  void _showBillOptions(Bill bill) {
    showModalBottomSheet(
      context: context,
      builder:
          (context) => SafeArea(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                ListTile(
                  leading: const Icon(Icons.edit),
                  title: const Text('Edit Bill'),
                  onTap: () {
                    Navigator.pop(context);
                    _editBill(bill);
                  },
                ),
                if (bill.status != BillStatus.paid)
                  ListTile(
                    leading: const Icon(Icons.payment),
                    title: const Text('Record Payment'),
                    onTap: () {
                      Navigator.pop(context);
                      _recordPayment(bill);
                    },
                  ),
                ListTile(
                  leading: const Icon(Icons.delete, color: Colors.red),
                  title: const Text(
                    'Delete Bill',
                    style: TextStyle(color: Colors.red),
                  ),
                  onTap: () {
                    Navigator.pop(context);
                    _confirmDeleteBill(bill);
                  },
                ),
              ],
            ),
          ),
    );
  }

  void _editBill(Bill bill) {
    // Try to navigate using named route first
    try {
      Navigator.of(context).pushNamed('/edit-bill', arguments: bill).then((
        result,
      ) {
        if (result == true) {
          _loadBills();
        }
      });
    } catch (e) {
      // Fallback to using MaterialPageRoute if named route fails
      Navigator.of(context)
          .push(
            MaterialPageRoute(builder: (context) => AddBillPage(bill: bill)),
          )
          .then((result) {
            if (result == true) {
              _loadBills();
            }
          });
    }
  }

  void _recordPayment(Bill bill) {
    Navigator.of(context)
        .push(
          MaterialPageRoute(
            builder: (context) => RecordPaymentPage(bill: bill),
          ),
        )
        .then((result) {
          if (result == true) {
            _loadBills();
          }
        });
  }

  void _confirmDeleteBill(Bill bill) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Delete Bill'),
            content: Text('Are you sure you want to delete "${bill.title}"?'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('CANCEL'),
              ),
              TextButton(
                onPressed: () {
                  Navigator.pop(context);
                  _deleteBill(bill);
                },
                child: const Text(
                  'DELETE',
                  style: TextStyle(color: Colors.red),
                ),
              ),
            ],
          ),
    );
  }

  Future<void> _deleteBill(Bill bill) async {
    try {
      await serviceLocator.billService.deleteBill(bill.id);

      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Bill deleted successfully')),
      );

      _loadBills();
    } catch (e) {
      if (!mounted) return;

      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('Error deleting bill: $e')));
    }
  }

  Widget _buildFloatingActionButtons() {
    return ExpandableFab(
      distance: 112.0,
      children: [
        LabeledActionButton(
          onPressed: () {
            Navigator.of(context).pushNamed('/select-tenant-for-payment').then((
              result,
            ) {
              if (result == true) {
                _loadBills();
              }
            });
          },
          icon: const Icon(Icons.payment),
          label: 'Record Payment',
          tooltip: 'Record Payment',
          backgroundColor: Theme.of(context).colorScheme.secondaryContainer,
          foregroundColor: Theme.of(context).colorScheme.onSecondaryContainer,
        ),
        LabeledActionButton(
          onPressed: () {
            Navigator.of(context).pushNamed('/multi-tenant-bill').then((
              result,
            ) {
              if (result == true) {
                _loadBills();
              }
            });
          },
          icon: const Icon(Icons.people),
          label: 'Group Bill',
          tooltip: 'Create Group Bill',
          backgroundColor: Theme.of(context).colorScheme.primaryContainer,
          foregroundColor: Theme.of(context).colorScheme.onPrimaryContainer,
        ),
        LabeledActionButton(
          onPressed: () {
            Navigator.of(context).pushNamed('/select-tenant-for-bill').then((
              result,
            ) {
              if (result == true) {
                _loadBills();
              }
            });
          },
          icon: const Icon(Icons.receipt_long),
          label: 'Add Bill',
          tooltip: 'Add  New  Bill',
          backgroundColor: Theme.of(context).colorScheme.tertiaryContainer,
          foregroundColor: Theme.of(context).colorScheme.onTertiaryContainer,
        ),
        LabeledActionButton(
          onPressed: () {
            AppLogger.info('Navigating to bill templates page from FAB');
            Navigator.of(context).pushNamed('/bill-templates').then((result) {
              if (result == true) {
                _loadBills();
              }
            });
          },
          icon: const Icon(Icons.auto_awesome),
          label: 'Templates',
          tooltip: 'Bill  Templates',
          backgroundColor: Theme.of(context).colorScheme.errorContainer,
          foregroundColor: Theme.of(context).colorScheme.onErrorContainer,
        ),
      ],
    );
  }

  // Function to manually trigger bill generation
  Future<void> _triggerBillGeneration() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Get the count of active templates directly from Supabase
      final response = await Supabase.instance.client
          .from('bill_templates')
          .select('id')
          .eq('auto_generate', true);

      final activeTemplatesCount = response.length;

      if (!mounted) return;

      setState(() {
        _isLoading = false;
      });

      if (activeTemplatesCount <= 0) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text(
              'No active bill templates found. Please create and activate templates first.',
            ),
            duration: Duration(seconds: 4),
          ),
        );
        return;
      }

      // Show confirmation dialog first
      final bool? proceed = await showDialog<bool>(
        context: context,
        builder: (BuildContext context) {
          return AlertDialog(
            title: const Text('Generate Bills'),
            content: Text(
              'This action will create bills for all tenants with active templates. '
              'Bills will be generated for each property where tenants are assigned to rooms.\n\n'
              'IMPORTANT: Please ensure you have verified all $activeTemplatesCount active templates '
              'before proceeding. This process will generate bills based on the current template settings.\n\n'
              'Do you want to continue?',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('CANCEL'),
              ),
              FilledButton(
                onPressed: () => Navigator.of(context).pop(true),
                child: const Text('GENERATE'),
              ),
            ],
          );
        },
      );

      if (proceed != true) return;

      setState(() {
        _isLoading = true;
      });

      try {
        final billsGenerated =
            await BillGenerationService.forceRunBillGeneration();

        if (!mounted) return;

        setState(() {
          _isLoading = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              billsGenerated > 0
                  ? '$billsGenerated bills generated successfully'
                  : 'No new bills were generated',
            ),
            action: SnackBarAction(label: 'REFRESH', onPressed: _loadBills),
          ),
        );

        // Reload bills to show newly generated ones
        _loadBills();
      } catch (e) {
        if (!mounted) return;

        setState(() {
          _isLoading = false;
        });

        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Error generating bills: $e')));
      }
    } catch (e) {
      if (!mounted) return;

      setState(() {
        _isLoading = false;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error checking active templates: $e')),
      );
    }
  }
}

// Expandable FAB implementation
@immutable
class ExpandableFab extends StatefulWidget {
  const ExpandableFab({
    super.key,
    this.initialOpen = false,
    required this.distance,
    required this.children,
  });

  final bool initialOpen;
  final double distance;
  final List<Widget> children;

  @override
  State<ExpandableFab> createState() => _ExpandableFabState();
}

class _ExpandableFabState extends State<ExpandableFab>
    with SingleTickerProviderStateMixin {
  late final AnimationController _controller;
  late final Animation<double> _expandAnimation;
  bool _open = false;

  @override
  void initState() {
    super.initState();
    _open = widget.initialOpen;
    _controller = AnimationController(
      value: _open ? 1.0 : 0.0,
      duration: const Duration(milliseconds: 250),
      vsync: this,
    );
    _expandAnimation = CurvedAnimation(
      curve: Curves.fastOutSlowIn,
      reverseCurve: Curves.easeOutQuad,
      parent: _controller,
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _toggle() {
    setState(() {
      _open = !_open;
      if (_open) {
        _controller.forward();
      } else {
        _controller.reverse();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox.expand(
      child: Stack(
        alignment: Alignment.bottomRight,
        clipBehavior: Clip.none,
        children: [
          _buildTapToCloseFab(),
          ..._buildExpandingActionButtons(),
          _buildTapToOpenFab(),
        ],
      ),
    );
  }

  Widget _buildTapToCloseFab() {
    return SizedBox(
      width: 56.0,
      height: 56.0,
      child: Center(
        child: Tooltip(
          message: 'Close Menu',
          waitDuration: const Duration(milliseconds: 500),
          showDuration: const Duration(seconds: 2),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.primaryContainer.withValues(
              alpha: 0.9,
              red:
                  (Theme.of(context).colorScheme.primaryContainer.r * 255.0)
                      .round() /
                  255.0,
              green:
                  (Theme.of(context).colorScheme.primaryContainer.g * 255.0)
                      .round() /
                  255.0,
              blue:
                  (Theme.of(context).colorScheme.primaryContainer.b * 255.0)
                      .round() /
                  255.0,
            ),
            borderRadius: BorderRadius.circular(8),
          ),
          textStyle: TextStyle(
            color: Theme.of(context).colorScheme.onPrimaryContainer,
            fontWeight: FontWeight.bold,
          ),
          child: Material(
            shape: const CircleBorder(),
            clipBehavior: Clip.antiAlias,
            elevation: 4.0,
            child: InkWell(
              onTap: _toggle,
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Icon(Icons.close, color: Theme.of(context).primaryColor),
              ),
            ),
          ),
        ),
      ),
    );
  }

  List<Widget> _buildExpandingActionButtons() {
    final children = <Widget>[];
    final count = widget.children.length;
    final step = 90.0 / (count - 1);
    for (
      var i = 0, angleInDegrees = 0.0;
      i < count;
      i++, angleInDegrees += step
    ) {
      children.add(
        _ExpandingActionButton(
          directionInDegrees: angleInDegrees,
          maxDistance: widget.distance,
          progress: _expandAnimation,
          child: widget.children[i],
        ),
      );
    }
    return children;
  }

  Widget _buildTapToOpenFab() {
    return IgnorePointer(
      ignoring: _open,
      child: AnimatedContainer(
        transformAlignment: Alignment.center,
        transform: Matrix4.diagonal3Values(
          _open ? 0.7 : 1.0,
          _open ? 0.7 : 1.0,
          1.0,
        ),
        duration: const Duration(milliseconds: 250),
        curve: const Interval(0.0, 0.5, curve: Curves.easeOut),
        child: AnimatedOpacity(
          opacity: _open ? 0.0 : 1.0,
          curve: const Interval(0.25, 1.0, curve: Curves.easeInOut),
          duration: const Duration(milliseconds: 250),
          child: FloatingActionButton(
            onPressed: _toggle,
            tooltip: 'Show Options',
            child: const Icon(Icons.add),
          ),
        ),
      ),
    );
  }
}

@immutable
class _ExpandingActionButton extends StatelessWidget {
  const _ExpandingActionButton({
    required this.directionInDegrees,
    required this.maxDistance,
    required this.progress,
    required this.child,
  });

  final double directionInDegrees;
  final double maxDistance;
  final Animation<double> progress;
  final Widget child;

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: progress,
      builder: (context, child) {
        final offset = Offset.fromDirection(
          directionInDegrees * (pi / 180.0),
          progress.value * maxDistance,
        );
        return Positioned(
          right: 4.0 + offset.dx,
          bottom: 4.0 + offset.dy,
          child: Transform.rotate(
            angle: (1.0 - progress.value) * pi / 2,
            child: child!,
          ),
        );
      },
      child: FadeTransition(opacity: progress, child: child),
    );
  }
}

@immutable
class LabeledActionButton extends StatelessWidget {
  const LabeledActionButton({
    super.key,
    this.onPressed,
    required this.icon,
    required this.label,
    this.tooltip,
    this.backgroundColor,
    this.foregroundColor,
  });

  final VoidCallback? onPressed;
  final Widget icon;
  final String label;
  final String? tooltip;
  final Color? backgroundColor;
  final Color? foregroundColor;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Label that appears when expanded
        Container(
          margin: const EdgeInsets.only(right: 8),
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: backgroundColor ?? theme.colorScheme.secondary,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withAlpha(50),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Text(
            label,
            style: TextStyle(
              color: foregroundColor ?? theme.colorScheme.onSecondary,
              fontWeight: FontWeight.bold,
              fontSize: 14,
            ),
          ),
        ),
        // Button
        Material(
          shape: const CircleBorder(),
          clipBehavior: Clip.antiAlias,
          color: backgroundColor ?? theme.colorScheme.secondary,
          elevation: 4.0,
          child: IconButton(
            onPressed: onPressed,
            icon: icon,
            color: foregroundColor ?? theme.colorScheme.onSecondary,
            tooltip: tooltip,
            iconSize: 24,
            splashRadius: 24,
          ),
        ),
      ],
    );
  }
}

@immutable
class ActionButton extends StatelessWidget {
  const ActionButton({
    super.key,
    this.onPressed,
    required this.icon,
    this.tooltip,
    this.backgroundColor,
    this.foregroundColor,
  });

  final VoidCallback? onPressed;
  final Widget icon;
  final String? tooltip;
  final Color? backgroundColor;
  final Color? foregroundColor;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Tooltip(
      message: tooltip ?? '',
      waitDuration: const Duration(milliseconds: 500),
      showDuration: const Duration(seconds: 2),
      preferBelow: false,
      decoration: BoxDecoration(
        color: theme.colorScheme.primaryContainer.withAlpha(230),
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(50),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      textStyle: TextStyle(
        color: theme.colorScheme.onPrimaryContainer,
        fontWeight: FontWeight.bold,
        fontSize: 14,
        letterSpacing: 0.5,
      ),
      verticalOffset: 20,
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
      child: Material(
        shape: const CircleBorder(),
        clipBehavior: Clip.antiAlias,
        color: backgroundColor ?? theme.colorScheme.secondary,
        elevation: 4.0,
        child: IconButton(
          onPressed: onPressed,
          icon: icon,
          color: foregroundColor ?? theme.colorScheme.onSecondary,
          tooltip: tooltip,
          iconSize: 24,
          splashRadius: 24,
        ),
      ),
    );
  }
}
